version: '3.8'

services:
  trix-supplier-service:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    ports:
      - "8015:8015"
    environment:
      - SERVICE_PORT=8015
      - DATABASE_URL=postgresql+asyncpg://supplier_user:supplier_pass@postgres-supplier:5432/supplier_db
      - REDIS_URL=redis://:redis123@redis:6379/9
      - REDIS_STREAMS_URL=redis://:redis123@redis-streams:6379/0
      - AUTH_SERVICE_URL=http://trix-core-auth:8001
      - USER_SERVICE_URL=http://trix-core-user:8002
      - TENANT_SERVICE_URL=http://trix-core-tenant:8003
      - CORE_SERVICE_URL=http://trix-core-core:8004
      - COMMERCE_SERVICE_URL=http://trix-core-commerce:8005
      - NOTIFICATION_SERVICE_URL=http://trix-core-notification:8006
      - I18N_SERVICE_URL=http://trix-core-i18n:8007
      - CDN_SERVICE_URL=http://trix-core-cdn:8008
      - MEDIA_SERVICE_URL=http://trix-core-media:8009
      - PAYMENT_SERVICE_URL=http://trix-core-payment:8010
      - DOMAIN_SERVICE_URL=http://trix-core-domain:8015
      - KAFKA_BOOTSTRAP_SERVERS=kafka-broker-1:9092,kafka-broker-2:9092,kafka-broker-3:9092
      - RABBITMQ_URL=amqp://supplier_user:supplier_pass@rabbitmq:5672/supplier_vhost
      - VAULT_URL=http://vault:8200
      - OPA_URL=http://opa-service:8181
      - AUTO_APPROVE_SUPPLIERS=false
      - TVENDOR_APPROVAL_REQUIRED=true
      - PERFORMANCE_TRACKING_ENABLED=true
      - EMAIL_NOTIFICATIONS_ENABLED=true
      - PROMETHEUS_ENABLED=true
      - JAEGER_ENABLED=true
      - ELK_ENABLED=true
      - ENVIRONMENT=development
    depends_on:
      - postgres-supplier
      - redis
    volumes:
      - ..:/app
    networks:
      - supplier_service_network

  postgres-supplier:
    image: postgres:15
    environment:
      POSTGRES_DB: supplier_db
      POSTGRES_USER: supplier_user
      POSTGRES_PASSWORD: supplier_pass
    ports:
      - "5442:5432"
    volumes:
      - postgres_supplier_data:/var/lib/postgresql/data
    networks:
      - supplier_service_network

  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes --requirepass redis123
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - supplier_service_network

volumes:
  postgres_supplier_data:
  redis_data:

networks:
  supplier_service_network:
    driver: bridge
