"""
Consultancy Service Vault Configuration
=======================================
Consultancy Service specific Vault paths and configuration.
"""

from typing import Dict, Optional

# Import shared Vault utilities
try:
    from microservices.core.shared_lib.config.vault_config import VaultBaseSettings
    SHARED_LIB_AVAILABLE = True
except ImportError:
    SHARED_LIB_AVAILABLE = False

from .settings import get_settings


class ConsultancyVaultConfig:
    """Consultancy Service specific Vault configuration."""
    
    def __init__(self):
        self.settings = get_settings()
    
    def get_vault_paths(self) -> Dict[str, str]:
        """Get Consultancy Service specific Vault paths."""
        return self.settings.get_consultancy_vault_paths()
    
    def get_vault_path(self, secret_type: str) -> Optional[str]:
        """Get specific Vault path by secret type."""
        paths = self.get_vault_paths()
        return paths.get(secret_type)
    
    def get_database_vault_path(self) -> str:
        """Get database secrets Vault path."""
        return self.get_vault_path("database") or "secret/consultancy-service/database"
    
    def get_kafka_vault_path(self) -> str:
        """Get Kafka secrets Vault path."""
        return self.get_vault_path("kafka") or "secret/consultancy-service/kafka"
    
    def get_redis_vault_path(self) -> str:
        """Get Redis secrets Vault path."""
        return self.get_vault_path("redis") or "secret/consultancy-service/redis"
    
    def get_billing_vault_path(self) -> str:
        """Get billing secrets Vault path."""
        return self.get_vault_path("billing") or "secret/consultancy-service/billing"
    
    def get_compliance_vault_path(self) -> str:
        """Get compliance secrets Vault path."""
        return self.get_vault_path("compliance") or "secret/consultancy-service/compliance"
    
    def get_external_apis_vault_path(self) -> str:
        """Get external APIs secrets Vault path."""
        return self.get_vault_path("external_apis") or "secret/consultancy-service/external-apis"


# Global Vault configuration instance
consultancy_vault_config = ConsultancyVaultConfig()


def get_consultancy_vault_config() -> ConsultancyVaultConfig:
    """Get consultancy Vault configuration."""
    return consultancy_vault_config
