"""
Media System - Seed Data
========================

Seed data para o Media System incluindo contextos de mídia,
configurações de processamento, tipos MIME permitidos e
configurações de storage.
"""

import asyncio
import logging
from typing import Dict, List, Any
from uuid import uuid4
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

try:
    from ..distributed_base import MicroserviceSeedModule
    from ..base import generate_uuid
    from ..monitoring import global_monitor
except ImportError:
    import sys
    from pathlib import Path
    sys.path.insert(0, str(Path(__file__).parent.parent))
    from distributed_base import MicroserviceSeedModule
    from base import generate_uuid
    from monitoring import global_monitor

logger = logging.getLogger(__name__)


class MediaSeed(MicroserviceSeedModule):
    """Seed para o Media System."""

    def __init__(self):
        super().__init__("media", "media")

    async def check_existing_data(self) -> bool:
        """Check if media configurations already exist."""
        db = await self.get_database_session()
        try:
            # Verificar se existe alguma configuração de media
            result = await db.execute(
                text("SELECT COUNT(*) FROM media.media_contexts WHERE id IS NOT NULL LIMIT 1")
            )
            # Se há contextos, assumimos que o sistema já foi inicializado
            return result.scalar() > 0
        except Exception as e:
            self.logger.warning(f"Error checking existing media data: {e}")
            # Se a tabela não existe, não há dados
            return False
        finally:
            await db.close()

    async def create_data(self) -> None:
        """Create media configurations."""
        db = await self.get_database_session()

        try:
            # Criar tabelas e dados do Media System
            await self.create_tables(db)
            counts = await self.seed_data(db)

            # Registrar métricas
            total_records = sum(counts.values())
            await global_monitor.record_seed_execution(
                microservice="media",
                module="media_config",
                records_created=total_records,
                execution_time=0.5,
                success=True
            )

            await db.commit()
            self.logger.info(f"✅ Media System initialized successfully: {counts}")

        except Exception as e:
            await db.rollback()
            self.logger.error(f"❌ Error creating media configurations: {e}")
            raise
        finally:
            await db.close()

    async def get_summary(self) -> dict:
        """Get summary of created media data."""
        return {
            "media_contexts": 6,
            "processing_configs": 6,
            "storage_regions": 3,
            "ai_processing": True,
            "ocr_enabled": True,
            "content_moderation": True
        }
    
    async def create_tables(self, session: AsyncSession) -> None:
        """Criar tabelas específicas do Media System."""
        
        # Schema para media
        await session.execute(text("CREATE SCHEMA IF NOT EXISTS media"))
        
        # Tabela de contextos de mídia (reference table)
        await session.execute(text("""
            CREATE TABLE IF NOT EXISTS media.media_contexts (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                name VARCHAR(50) UNIQUE NOT NULL,
                description VARCHAR(255) NOT NULL,
                allowed_mime_types TEXT[] DEFAULT '{}',
                max_file_size BIGINT DEFAULT 104857600, -- 100MB
                requires_processing BOOLEAN DEFAULT true,
                auto_thumbnail BOOLEAN DEFAULT true,
                auto_compression BOOLEAN DEFAULT false,
                enable_ocr BOOLEAN DEFAULT false,
                enable_ai_analysis BOOLEAN DEFAULT false,
                is_active BOOLEAN DEFAULT true,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            )
        """))
        
        # Tabela de configurações de processamento (reference table)
        await session.execute(text("""
            CREATE TABLE IF NOT EXISTS media.processing_configs (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                name VARCHAR(100) UNIQUE NOT NULL,
                type VARCHAR(50) NOT NULL, -- thumbnail, compression, ocr, ai_analysis
                config JSONB NOT NULL DEFAULT '{}',
                is_default BOOLEAN DEFAULT false,
                is_active BOOLEAN DEFAULT true,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            )
        """))
        
        # Tabela de regiões de storage (reference table)
        await session.execute(text("""
            CREATE TABLE IF NOT EXISTS media.storage_regions (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                name VARCHAR(50) UNIQUE NOT NULL,
                endpoint VARCHAR(255) NOT NULL,
                region VARCHAR(50) NOT NULL,
                bucket_prefix VARCHAR(100) DEFAULT 'trix-media',
                is_primary BOOLEAN DEFAULT false,
                is_active BOOLEAN DEFAULT true,
                priority INTEGER DEFAULT 5,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            )
        """))
        
        # Tabela principal de mídia (distributed table)
        await session.execute(text("""
            CREATE TABLE IF NOT EXISTS media.media (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                tenant_id UUID NOT NULL,
                user_id UUID NOT NULL,
                filename VARCHAR(255) NOT NULL,
                original_filename VARCHAR(255) NOT NULL,
                file_path VARCHAR(500) NOT NULL,
                file_size BIGINT NOT NULL,
                mime_type VARCHAR(100) NOT NULL,
                file_hash VARCHAR(64) UNIQUE NOT NULL,
                context VARCHAR(50) NOT NULL,
                region VARCHAR(50) NOT NULL,
                is_processed BOOLEAN DEFAULT false,
                is_public BOOLEAN DEFAULT false,
                processing_status VARCHAR(50) DEFAULT 'pending',
                metadata JSONB DEFAULT '{}',
                ai_analysis JSONB DEFAULT '{}',
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            )
        """))
        
        # Índices para performance
        await session.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_media_tenant_context 
            ON media.media (tenant_id, context)
        """))
        
        await session.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_media_tenant_user 
            ON media.media (tenant_id, user_id)
        """))
        
        await session.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_media_hash 
            ON media.media (file_hash)
        """))
        
        await session.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_media_processing_status 
            ON media.media (processing_status)
        """))
        
        await session.commit()
        logger.info("✅ Tabelas do Media System criadas com sucesso")
    
    async def seed_data(self, session: AsyncSession) -> Dict[str, int]:
        """Inserir dados iniciais do Media System."""
        
        counts = {
            'media_contexts': 0,
            'processing_configs': 0,
            'storage_regions': 0
        }
        
        # Contextos de mídia padrão
        media_contexts = [
            {
                'name': 'TENANT',
                'description': 'Mídia associada ao tenant (logos, banners)',
                'allowed_mime_types': ['image/jpeg', 'image/png', 'image/webp', 'image/svg+xml'],
                'max_file_size': 10485760,  # 10MB
                'requires_processing': True,
                'auto_thumbnail': True,
                'auto_compression': True,
                'enable_ai_analysis': True
            },
            {
                'name': 'USER',
                'description': 'Mídia associada ao usuário (avatares, documentos)',
                'allowed_mime_types': ['image/jpeg', 'image/png', 'image/webp', 'application/pdf'],
                'max_file_size': 52428800,  # 50MB
                'requires_processing': True,
                'auto_thumbnail': True,
                'enable_ocr': True
            },
            {
                'name': 'MENU_ITEM',
                'description': 'Imagens de itens do menu',
                'allowed_mime_types': ['image/jpeg', 'image/png', 'image/webp'],
                'max_file_size': 20971520,  # 20MB
                'requires_processing': True,
                'auto_thumbnail': True,
                'auto_compression': True,
                'enable_ai_analysis': True
            },
            {
                'name': 'PRODUCT',
                'description': 'Imagens de produtos do e-commerce',
                'allowed_mime_types': ['image/jpeg', 'image/png', 'image/webp'],
                'max_file_size': 20971520,  # 20MB
                'requires_processing': True,
                'auto_thumbnail': True,
                'auto_compression': True,
                'enable_ai_analysis': True
            },
            {
                'name': 'DOCUMENT',
                'description': 'Documentos diversos (PDFs, contratos)',
                'allowed_mime_types': ['application/pdf', 'application/msword', 
                                     'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
                'max_file_size': 104857600,  # 100MB
                'requires_processing': True,
                'enable_ocr': True,
                'enable_ai_analysis': True
            },
            {
                'name': 'MEDIA',
                'description': 'Mídia geral (vídeos, áudios)',
                'allowed_mime_types': ['video/mp4', 'video/avi', 'video/mov', 'audio/mp3', 'audio/wav'],
                'max_file_size': 1073741824,  # 1GB
                'requires_processing': True,
                'auto_thumbnail': True,
                'auto_compression': True
            }
        ]
        
        for context_data in media_contexts:
            await session.execute(text("""
                INSERT INTO media.media_contexts 
                (name, description, allowed_mime_types, max_file_size, requires_processing, 
                 auto_thumbnail, auto_compression, enable_ocr, enable_ai_analysis)
                VALUES (:name, :description, :allowed_mime_types, :max_file_size, :requires_processing,
                        :auto_thumbnail, :auto_compression, :enable_ocr, :enable_ai_analysis)
                ON CONFLICT (name) DO NOTHING
            """), context_data)
            counts['media_contexts'] += 1
        
        # Configurações de processamento padrão
        processing_configs = [
            {
                'name': 'thumbnail_small',
                'type': 'thumbnail',
                'config': {'width': 150, 'height': 150, 'quality': 85, 'format': 'webp'},
                'is_default': True
            },
            {
                'name': 'thumbnail_medium',
                'type': 'thumbnail',
                'config': {'width': 300, 'height': 300, 'quality': 85, 'format': 'webp'}
            },
            {
                'name': 'thumbnail_large',
                'type': 'thumbnail',
                'config': {'width': 600, 'height': 600, 'quality': 90, 'format': 'webp'}
            },
            {
                'name': 'compression_standard',
                'type': 'compression',
                'config': {'quality': 85, 'format': 'webp', 'progressive': True},
                'is_default': True
            },
            {
                'name': 'ocr_multilang',
                'type': 'ocr',
                'config': {'languages': ['por', 'eng', 'spa'], 'confidence_threshold': 60},
                'is_default': True
            },
            {
                'name': 'ai_content_analysis',
                'type': 'ai_analysis',
                'config': {'enable_classification': True, 'enable_moderation': True, 'enable_tagging': True},
                'is_default': True
            }
        ]
        
        for config_data in processing_configs:
            await session.execute(text("""
                INSERT INTO media.processing_configs (name, type, config, is_default)
                VALUES (:name, :type, :config, :is_default)
                ON CONFLICT (name) DO NOTHING
            """), config_data)
            counts['processing_configs'] += 1
        
        # Regiões de storage padrão
        storage_regions = [
            {
                'name': 'us-east-1',
                'endpoint': 'minio-us-east.trix.com:9000',
                'region': 'us-east-1',
                'bucket_prefix': 'trix-media',
                'is_primary': True,
                'priority': 1
            },
            {
                'name': 'eu-west-1',
                'endpoint': 'minio-eu-west.trix.com:9000',
                'region': 'eu-west-1',
                'bucket_prefix': 'trix-media',
                'priority': 2
            },
            {
                'name': 'ap-southeast-1',
                'endpoint': 'minio-ap-southeast.trix.com:9000',
                'region': 'ap-southeast-1',
                'bucket_prefix': 'trix-media',
                'priority': 3
            }
        ]
        
        for region_data in storage_regions:
            await session.execute(text("""
                INSERT INTO media.storage_regions (name, endpoint, region, bucket_prefix, is_primary, priority)
                VALUES (:name, :endpoint, :region, :bucket_prefix, :is_primary, :priority)
                ON CONFLICT (name) DO NOTHING
            """), region_data)
            counts['storage_regions'] += 1
        
        await session.commit()
        logger.info(f"✅ Dados do Media System inseridos: {counts}")
        
        return counts


async def seed():
    """Entry point for media service seed."""
    media_seed = MediaSeed()

    try:
        success = await media_seed.run()

        if success:
            summary = await media_seed.get_summary()
            print(f"✅ Media seed completed successfully!")
            print(f"📊 Summary: {summary}")
        else:
            print("❌ Media seed failed!")

        return success

    except Exception as e:
        print(f"❌ Critical error in media seed: {e}")
        return False


if __name__ == "__main__":
    asyncio.run(seed())
