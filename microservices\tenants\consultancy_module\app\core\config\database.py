"""
Consultancy Service Database Configuration
=========================================
Consultancy Service specific database configuration that extends shared_lib.
"""

from typing import Optional
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.pool import NullPool

# Import shared database utilities
try:
    from microservices.core.shared_lib.config.database_config import DatabaseSettings
    from microservices.core.shared_lib.config.infrastructure.database.connection import (
        get_database_engine,
        get_session_factory
    )
    SHARED_LIB_AVAILABLE = True
except ImportError:
    SHARED_LIB_AVAILABLE = False

from .settings import get_settings


class ConsultancyDatabaseConfig:
    """Consultancy Service specific database configuration."""
    
    def __init__(self):
        self.settings = get_settings()
        self._engine = None
        self._session_factory = None
    
    @property
    def engine(self):
        """Get or create database engine."""
        if self._engine is None:
            if SHARED_LIB_AVAILABLE:
                # Use shared database engine configuration
                self._engine = get_database_engine(
                    database_url=self.settings.DATABASE_URL,
                    service_name=self.settings.SERVICE_NAME,
                    pool_size=getattr(self.settings, 'DATABASE_POOL_SIZE', 20),
                    max_overflow=getattr(self.settings, 'DATABASE_MAX_OVERFLOW', 30),
                    pool_timeout=getattr(self.settings, 'DATABASE_POOL_TIMEOUT', 30),
                    pool_recycle=getattr(self.settings, 'DATABASE_POOL_RECYCLE', 3600)
                )
            else:
                # Fallback to local configuration
                self._engine = create_async_engine(
                    self.settings.DATABASE_URL,
                    poolclass=NullPool,
                    echo=False
                )
        return self._engine
    
    @property
    def session_factory(self):
        """Get or create session factory."""
        if self._session_factory is None:
            if SHARED_LIB_AVAILABLE:
                # Use shared session factory
                self._session_factory = get_session_factory(self.engine)
            else:
                # Fallback to local configuration
                self._session_factory = async_sessionmaker(
                    bind=self.engine,
                    class_=AsyncSession,
                    expire_on_commit=False
                )
        return self._session_factory
    
    async def get_session(self) -> AsyncSession:
        """Get database session."""
        async with self.session_factory() as session:
            yield session


# Global database configuration instance
consultancy_db_config = ConsultancyDatabaseConfig()


async def get_consultancy_db_session() -> AsyncSession:
    """Dependency to get consultancy database session."""
    async with consultancy_db_config.session_factory() as session:
        try:
            yield session
        finally:
            await session.close()


def get_consultancy_database_config() -> ConsultancyDatabaseConfig:
    """Get consultancy database configuration."""
    return consultancy_db_config
