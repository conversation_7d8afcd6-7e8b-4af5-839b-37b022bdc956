# ============================================================================
# MEDIA SYSTEM v2.0.0 - Enterprise Media Management Microservice
# ============================================================================
# Port: 8007 | Citus Data Sharding | Event-Driven Architecture | Full Observability
# Features: Multi-Region Storage, AI Processing, Real-time Updates, Enterprise Security
# ============================================================================

version: '3.8'

services:
  # ============================================================================
  # MEDIA SYSTEM - Enterprise Service
  # ============================================================================
  trix-core-media:
    build:
      context: ../
      dockerfile: docker/Dockerfile
    container_name: trix-core-media
    ports:
      - "8007:8007"
    environment:
      # Service Configuration
      - SERVICE_NAME=media-system
      - SERVICE_VERSION=2.0.0
      - SERVICE_PORT=8007
      - ENVIRONMENT=development
      - DEBUG=false

      # Database (Citus Data)
      - DATABASE_URL=postgresql+asyncpg://${MEDIA_DB_USER}:${MEDIA_DB_PASSWORD}@trix-db-media-coordinator:5432/${MEDIA_DB_NAME}
      - CITUS_COORDINATOR_URL=postgresql+asyncpg://${MEDIA_DB_USER}:${MEDIA_DB_PASSWORD}@trix-db-media-coordinator:5432/${MEDIA_DB_NAME}
      - CITUS_WORKER_NODES=trix-db-media-worker1:5432,trix-db-media-worker2:5432
      - SHARD_COUNT=32
      - REPLICATION_FACTOR=2

      # Messaging (Event-Driven Architecture)
      - KAFKA_BOOTSTRAP_SERVERS=trix-kafka:9092
      - RABBITMQ_URL=amqp://guest:guest@trix-rabbitmq:5672/
      - REDIS_URL=redis://:${REDIS_PASSWORD}@trix-redis:6379/7

      # Service URLs (Microservice Integration)
      - AUTH_SERVICE_URL=http://trix-core-auth:8001
      - USER_SERVICE_URL=http://trix-core-user:8002
      - TENANT_SERVICE_URL=http://trix-core-tenant:8003
      - CORE_SERVICE_URL=http://trix-core-core:8004
      - CDN_SERVICE_URL=http://trix-core-cdn:8009

      # Storage (Multi-Region)
      - STORAGE_BACKEND=minio
      - MINIO_ENDPOINT=trix-minio:9000
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY}
      - MINIO_SECURE=false
      - STORAGE_REGIONS=us-east,eu-west,asia-pacific

      # Security (HashiCorp Vault)
      - VAULT_URL=http://trix-vault:8200
      - VAULT_TOKEN=${VAULT_TOKEN}
      - VAULT_ENABLED=true
      - SECRET_KEY=${JWT_SECRET_KEY}

      # Observability (Prometheus + Jaeger + ELK)
      - PROMETHEUS_ENABLED=true
      - JAEGER_ENABLED=true
      - JAEGER_AGENT_HOST=trix-jaeger-agent
      - ELASTICSEARCH_HOST=trix-elasticsearch
      - LOG_LEVEL=INFO

      # Processing Configuration
      - AI_PROCESSING_ENABLED=true
      - OCR_ENABLED=true
      - VIRUS_SCAN_ENABLED=true
      - CDN_ENABLED=true
      - MAX_UPLOAD_SIZE=**********  # 1GB

    volumes:
      - media_storage:/app/media
      - ../app:/app/app:ro
    depends_on:
      - trix-db-media-coordinator
      - trix-redis
      - trix-kafka
      - trix-rabbitmq
      - trix-minio
      - trix-vault
    networks:
      - trix-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8007/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.media-system.rule=Host(`media.trix.local`)"
      - "traefik.http.services.media-system.loadbalancer.server.port=8007"
      - "prometheus.io/scrape=true"
      - "prometheus.io/port=8007"
      - "prometheus.io/path=/metrics"

  # ============================================================================
  # CITUS DATA - Coordinator Node
  # ============================================================================
  trix-db-media-coordinator:
    image: citusdata/citus:12.1
    container_name: trix-db-media-coordinator
    environment:
      - POSTGRES_DB=${MEDIA_DB_NAME}
      - POSTGRES_USER=${MEDIA_DB_USER}
      - POSTGRES_PASSWORD=${MEDIA_DB_PASSWORD}
      - CITUS_HOST=trix-db-media-coordinator
    volumes:
      - media_coordinator_data:/var/lib/postgresql/data
      - ./init-citus.sql:/docker-entrypoint-initdb.d/init-citus.sql
    ports:
      - "5440:5432"
    networks:
      - trix-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${MEDIA_DB_USER} -d ${MEDIA_DB_NAME}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # ============================================================================
  # CITUS DATA - Worker Node 1
  # ============================================================================
  trix-db-media-worker1:
    image: citusdata/citus:12.1
    container_name: trix-db-media-worker1
    environment:
      - POSTGRES_DB=${MEDIA_DB_NAME}
      - POSTGRES_USER=${MEDIA_DB_USER}
      - POSTGRES_PASSWORD=${MEDIA_DB_PASSWORD}
      - CITUS_HOST=trix-db-media-worker1
    volumes:
      - media_worker1_data:/var/lib/postgresql/data
    networks:
      - trix-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${MEDIA_DB_USER} -d ${MEDIA_DB_NAME}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # ============================================================================
  # CITUS DATA - Worker Node 2
  # ============================================================================
  trix-db-media-worker2:
    image: citusdata/citus:12.1
    container_name: trix-db-media-worker2
    environment:
      - POSTGRES_DB=${MEDIA_DB_NAME}
      - POSTGRES_USER=${MEDIA_DB_USER}
      - POSTGRES_PASSWORD=${MEDIA_DB_PASSWORD}
      - CITUS_HOST=trix-db-media-worker2
    volumes:
      - media_worker2_data:/var/lib/postgresql/data
    networks:
      - trix-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${MEDIA_DB_USER} -d ${MEDIA_DB_NAME}"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  media_coordinator_data:
    driver: local
  media_worker1_data:
    driver: local
  media_worker2_data:
    driver: local
  media_storage:
    driver: local

networks:
  trix-network:
    external: true
    
