"""
Ghost Function Service - Database Configuration

Configuração e gerenciamento de conexões com PostgreSQL usando SQLAlchemy async.
Suporta sharding com Citus Data para escala planetária.
Utiliza a shared_lib para configurações centralizadas.

Author: Trix Platform Team
"""

import asyncio
from typing import AsyncGenerator, Optional

from sqlalchemy import MetaData, event, text
from sqlalchemy.ext.asyncio import (
    AsyncEngine,
    AsyncSession,
    async_sessionmaker,
    create_async_engine,
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.pool import NullPool, QueuePool

from app.core.config import get_settings
from microservices.core.shared_lib.config import (
    ConnectionManager,
    get_logger,
    setup_logging
)

# Configurações
settings = get_settings()

# Logger estruturado (usando shared_lib)
logger = get_logger("ghost-function-service")

# Metadata para convenções de nomenclatura
metadata = MetaData(
    naming_convention={
        "ix": "ix_%(column_0_label)s",
        "uq": "uq_%(table_name)s_%(column_0_name)s",
        "ck": "ck_%(table_name)s_%(constraint_name)s",
        "fk": "fk_%(table_name)s_%(column_0_name)s_%(referred_table_name)s",
        "pk": "pk_%(table_name)s",
    }
)

# Base para modelos SQLAlchemy
Base = declarative_base(metadata=metadata)

# Engine global
engine: Optional[AsyncEngine] = None
async_session_maker: Optional[async_sessionmaker[AsyncSession]] = None


def create_engine() -> AsyncEngine:
    """
    Cria e configura o engine SQLAlchemy async.
    
    Returns:
        AsyncEngine: Engine configurado para PostgreSQL
    """
    # Configuração do pool de conexões
    if settings.SERVICE_ENVIRONMENT == "production":
        # Produção: Pool otimizado para alta concorrência
        engine = create_async_engine(
            settings.database_config.DATABASE_URL,
            echo=settings.database_config.DB_ECHO,
            poolclass=QueuePool,
            pool_size=settings.database_config.DB_POOL_SIZE,
            max_overflow=settings.database_config.DB_MAX_OVERFLOW,
            pool_pre_ping=True,
            pool_recycle=3600,  # 1 hora
            # Configurações específicas para PostgreSQL
            connect_args={
                "server_settings": {
                    "application_name": "ghost_function_service",
                }
            }
        )
    else:
        # Desenvolvimento: Pool simples (NullPool não aceita pool_size/max_overflow)
        engine = create_async_engine(
            settings.database_config.DATABASE_URL,
            echo=settings.database_config.DB_ECHO,
            poolclass=NullPool,
            # Configurações específicas para PostgreSQL
            connect_args={
                "server_settings": {
                    "application_name": "ghost_function_service",
                }
            }
        )
    
    # Event listeners para logging e monitoramento
    @event.listens_for(engine.sync_engine, "connect")
    def set_sqlite_pragma(dbapi_connection, connection_record):
        """Configura parâmetros de conexão."""
        logger.debug("Nova conexão estabelecida com PostgreSQL")
    
    @event.listens_for(engine.sync_engine, "checkout")
    def receive_checkout(dbapi_connection, connection_record, connection_proxy):
        """Log quando uma conexão é retirada do pool."""
        logger.debug("Conexão retirada do pool")
    
    @event.listens_for(engine.sync_engine, "checkin")
    def receive_checkin(dbapi_connection, connection_record):
        """Log quando uma conexão é devolvida ao pool."""
        logger.debug("Conexão devolvida ao pool")
    
    return engine


async def init_db() -> None:
    """
    Inicializa o banco de dados e cria as tabelas.
    
    Esta função deve ser chamada na inicialização da aplicação.
    """
    global engine, async_session_maker
    
    try:
        logger.info("🗄️ Inicializando conexão com PostgreSQL...")
        
        # Criar engine
        engine = create_engine()
        
        # Criar session maker
        async_session_maker = async_sessionmaker(
            engine,
            class_=AsyncSession,
            expire_on_commit=False,
            autoflush=True,
            autocommit=False,
        )
        
        # Testar conexão
        async with engine.begin() as conn:
            # Verificar se é Citus Data (para sharding)
            result = await conn.execute(text("SELECT version()"))
            version_info = result.scalar()
            
            if "citus" in version_info.lower():
                logger.info("✅ Citus Data detectado - Sharding habilitado")
            else:
                logger.info("✅ PostgreSQL padrão detectado")
        
        # Importar modelos para garantir que sejam registrados
        from app.models import (
            GhostConfiguration,
            ServiceHealth,
            ServiceVersion,
            ReplicationLog,
            FailoverEvent,
            ThermalConfiguration,
            GoldenSnapshot,
            PredictiveMetrics,
            RecoveryLog,
        )
        
        # Criar tabelas (em produção, usar Alembic)
        if settings.SERVICE_ENVIRONMENT != "production":
            async with engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)
                logger.info("✅ Tabelas criadas/verificadas")
        
        logger.info("✅ Database inicializado com sucesso")
        
    except Exception as e:
        logger.error(f"❌ Erro ao inicializar database: {e}")
        raise


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency para obter uma sessão do banco de dados.
    
    Yields:
        AsyncSession: Sessão do banco de dados
    """
    if not async_session_maker:
        raise RuntimeError("Database não foi inicializado. Chame init_db() primeiro.")
    
    async with async_session_maker() as session:
        try:
            yield session
        except Exception as e:
            await session.rollback()
            logger.error(f"Erro na sessão do database: {e}")
            raise
        finally:
            await session.close()


async def close_db() -> None:
    """
    Fecha as conexões com o banco de dados.
    
    Esta função deve ser chamada na finalização da aplicação.
    """
    global engine
    
    if engine:
        logger.info("🔄 Fechando conexões com o database...")
        await engine.dispose()
        logger.info("✅ Conexões fechadas")


class DatabaseHealthCheck:
    """
    Classe para verificar a saúde do banco de dados.
    """
    
    @staticmethod
    async def check_connection() -> bool:
        """
        Verifica se a conexão com o database está funcionando.
        
        Returns:
            bool: True se a conexão estiver OK
        """
        try:
            if not engine:
                return False
            
            async with engine.begin() as conn:
                await conn.execute("SELECT 1")
            
            return True
            
        except Exception as e:
            logger.error(f"Database health check falhou: {e}")
            return False
    
    @staticmethod
    async def get_pool_status() -> dict:
        """
        Retorna informações sobre o pool de conexões.
        
        Returns:
            dict: Status do pool
        """
        if not engine or not hasattr(engine.pool, 'size'):
            return {"status": "unavailable"}
        
        pool = engine.pool
        return {
            "size": pool.size(),
            "checked_in": pool.checkedin(),
            "checked_out": pool.checkedout(),
            "overflow": pool.overflow(),
            "invalid": pool.invalid(),
        }


# Instância global do health check
db_health = DatabaseHealthCheck()
