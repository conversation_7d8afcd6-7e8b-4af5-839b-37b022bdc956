"""
Configuration settings for Ghost Function Service.

DEPRECATED: This file is deprecated. Use app.core.config.settings instead.
This file is kept for backward compatibility and will be removed in v4.0.

Author: Trix Platform Team
"""

import warnings
from .config.settings import GhostFunctionSettings, get_settings

# Issue deprecation warning
warnings.warn(
    "app.core.config is deprecated. Use app.core.config.settings instead. "
    "This module will be removed in v4.0.",
    DeprecationWarning,
    stacklevel=2
)

# Re-export for backward compatibility
__all__ = ["GhostFunctionSettings", "get_settings"]

