"""
Restaurant Module Settings Configuration
=======================================
Restaurant Module specific settings that extend shared_lib configurations.
Follows the pattern established by core services for consistency.
"""

from typing import List, Optional, Dict, Any
from pydantic import Field

# Try to import from shared_lib first, fallback if not available
try:
    from microservices.core.shared_lib.config.tenant_modules_config import RestaurantModuleSettings as SharedRestaurantSettings
    SHARED_LIB_AVAILABLE = True
except ImportError:
    from pydantic_settings import BaseSettings as SharedRestaurantSettings
    SHARED_LIB_AVAILABLE = False




class RestaurantSettings(SharedRestaurantSettings):
    """
    Restaurant Service specific settings extending shared_lib configurations.

    Inherits common configurations from:
    - SharedRestaurantSettings: Common tenant module configurations from shared_lib
    - Adds restaurant-specific business and technical configurations
    """

    # ===== SECURITY CONFIGURATION =====
    # JWT Settings (Restaurant-specific)
    JWT_SECRET_KEY: str = Field(
        default="restaurant-secret-key-change-in-production",
        env="JWT_SECRET_KEY"
    )
    JWT_ALGORITHM: str = Field(default="HS256", env="JWT_ALGORITHM")
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=30, env="JWT_ACCESS_TOKEN_EXPIRE_MINUTES")

    # ===== BASIC SERVICE CONFIGURATION =====
    # Core service attributes (always needed)
    SERVICE_NAME: str = Field(default="restaurant-service", env="SERVICE_NAME")
    SERVICE_VERSION: str = Field(default="2.0.0", env="SERVICE_VERSION")
    SERVICE_PORT: int = Field(default=8004, env="SERVICE_PORT")
    ENVIRONMENT: str = Field(default="development", env="ENVIRONMENT")

    # ===== RESTAURANT SPECIFIC TECHNICAL SETTINGS =====
    # Only restaurant-specific technical configurations here
    # Common configurations are inherited from shared_lib
    # Business configurations come from tenant_service via TenantConfigService

    # Kitchen Display System Technical Settings
    KDS_AUTO_REFRESH: bool = Field(default=True, env="KDS_AUTO_REFRESH")
    KDS_SOUND_ALERTS: bool = Field(default=True, env="KDS_SOUND_ALERTS")
    KDS_PRINTER_INTEGRATION: bool = Field(default=False, env="KDS_PRINTER_INTEGRATION")

    # API Rate Limiting (Technical)
    API_RATE_LIMIT_PER_MINUTE: int = Field(default=1000, env="API_RATE_LIMIT_PER_MINUTE")
    API_RATE_LIMIT_BURST: int = Field(default=100, env="API_RATE_LIMIT_BURST")

    # Cache Settings (Technical)
    CACHE_TTL_SECONDS: int = Field(default=300, env="CACHE_TTL_SECONDS")
    CACHE_MAX_SIZE: int = Field(default=1000, env="CACHE_MAX_SIZE")

    # File Upload Settings (Technical)
    MAX_UPLOAD_SIZE_MB: int = Field(default=10, env="MAX_UPLOAD_SIZE_MB")
    ALLOWED_IMAGE_TYPES: str = Field(
        default="image/jpeg,image/png,image/webp",
        env="ALLOWED_IMAGE_TYPES"
    )

    # ===== COMPATIBILITY PROPERTIES =====
    # Properties for backward compatibility with legacy code

    @property
    def service_name(self) -> str:
        """Backward compatibility property for SERVICE_NAME."""
        return self.SERVICE_NAME

    @property
    def service_version(self) -> str:
        """Backward compatibility property for SERVICE_VERSION."""
        return self.SERVICE_VERSION

    @property
    def service_port(self) -> int:
        """Backward compatibility property for SERVICE_PORT."""
        return self.SERVICE_PORT

    @property
    def prometheus_enabled(self) -> bool:
        """Backward compatibility property for Prometheus metrics."""
        return True  # Enable Prometheus by default

    @property
    def observability_settings(self) -> Dict[str, Any]:
        """Backward compatibility property for observability settings."""
        return {
            "jaeger_service_name": self.SERVICE_NAME,
            "jaeger_endpoint": "http://jaeger:14268/api/traces",
            "prometheus_endpoint": "http://prometheus:9090",
            "log_level": "INFO",
            "enable_tracing": True,
            "enable_metrics": True
        }

    @property
    def service_observability_config(self) -> Dict[str, Any]:
        """Get observability configuration for the service."""
        return self.observability_settings

    @property
    def allowed_image_types(self) -> List[str]:
        """Get allowed image types as list."""
        if isinstance(self.ALLOWED_IMAGE_TYPES, str):
            return [t.strip() for t in self.ALLOWED_IMAGE_TYPES.split(",")]
        return self.ALLOWED_IMAGE_TYPES

    # Security config compatibility
    @property
    def security_config(self):
        """Security config compatibility for legacy code."""
        class SecurityConfig:
            def __init__(self, settings):
                self.JWT_SECRET_KEY = settings.JWT_SECRET_KEY
                self.JWT_ALGORITHM = settings.JWT_ALGORITHM
                self.JWT_ACCESS_TOKEN_EXPIRE_MINUTES = settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES
        return SecurityConfig(self)

    # ===== FALLBACK CONFIGURATIONS =====
    # These are used when shared_lib is not available

    # Always define these attributes for compatibility
    REDIS_URL: str = Field(default="redis://redis:6379/0", env="REDIS_URL")
    REDIS_HOST: str = Field(default="redis", env="REDIS_HOST")
    REDIS_PORT: int = Field(default=6379, env="REDIS_PORT")
    REDIS_PASSWORD: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    REDIS_DB: int = Field(default=0, env="REDIS_DB")

    KAFKA_ENABLED: bool = Field(default=True, env="KAFKA_ENABLED")
    KAFKA_BOOTSTRAP_SERVERS: str = Field(default="kafka:9092", env="KAFKA_BOOTSTRAP_SERVERS")

    JAEGER_ENABLED: bool = Field(default=True, env="JAEGER_ENABLED")
    JAEGER_AGENT_HOST: str = Field(default="jaeger", env="JAEGER_AGENT_HOST")
    JAEGER_AGENT_PORT: int = Field(default=6831, env="JAEGER_AGENT_PORT")

    PROMETHEUS_ENABLED: bool = Field(default=True, env="PROMETHEUS_ENABLED")
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    METRICS_ENABLED: bool = Field(default=True, env="METRICS_ENABLED")
    TRACING_ENABLED: bool = Field(default=True, env="TRACING_ENABLED")

    # Database configuration (always needed)
    DATABASE_URL: str = Field(
        default="postgresql+asyncpg://restaurant_user:restaurant_pass@citus-coordinator:5432/restaurant_db",
        env="DATABASE_URL"
    )
    DATABASE_POOL_SIZE: int = Field(default=20, env="DATABASE_POOL_SIZE")
    DATABASE_MAX_OVERFLOW: int = Field(default=30, env="DATABASE_MAX_OVERFLOW")
    DATABASE_POOL_TIMEOUT: int = Field(default=30, env="DATABASE_POOL_TIMEOUT")
    DATABASE_POOL_RECYCLE: int = Field(default=3600, env="DATABASE_POOL_RECYCLE")


# ===== GLOBAL SETTINGS INSTANCE =====

def get_settings() -> RestaurantSettings:
    """
    Get restaurant settings instance.
    Uses shared_lib configurations when available, fallback otherwise.
    """
    try:
        return RestaurantSettings()
    except Exception as e:
        print(f"Warning: Error creating RestaurantSettings: {e}")
        print("Using fallback configuration...")
        # Create fallback settings if shared_lib is not available
        return RestaurantSettings()

# Global settings instance
settings = get_settings()

# ===== SHARED_LIB INTEGRATION STATUS =====

def get_shared_lib_status() -> Dict[str, Any]:
    """Get shared_lib integration status for debugging."""
    return {
        "shared_lib_available": SHARED_LIB_AVAILABLE,
        "settings_class": settings.__class__.__name__,
        "service_name": settings.SERVICE_NAME,
        "service_port": settings.SERVICE_PORT,
        "integration_pattern": "tenant_modules_config"
    }
