#!/usr/bin/env python3
"""
🚀 Deploy Final do Citus Distribuído - Trix
Script completo para deploy e validação do cluster Citus
"""

import asyncio
import subprocess
import logging
import time
import os
from pathlib import Path

# Configuração de logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CitusDeployManager:
    """Gerenciador completo do deploy Citus"""
    
    def __init__(self):
        self.steps = [
            "check_prerequisites",
            "start_citus_cluster", 
            "setup_migrations",
            "setup_seeds",
            "validate_cluster",
            "generate_report"
        ]
        self.completed_steps = []
        self.failed_steps = []
        
    async def deploy_complete_citus(self):
        """Executa deploy completo do Citus"""
        logger.info("🚀 Iniciando deploy completo do Citus Distribuído - Trix")
        logger.info("=" * 70)
        
        start_time = time.time()
        
        try:
            for step in self.steps:
                logger.info(f"📋 Executando: {step}")
                
                if await self._execute_step(step):
                    self.completed_steps.append(step)
                    logger.info(f"✅ {step} concluído com sucesso")
                else:
                    self.failed_steps.append(step)
                    logger.error(f"❌ {step} falhou")
                    break
                
                logger.info("-" * 50)
            
            # Relatório final
            end_time = time.time()
            duration = end_time - start_time
            
            await self._generate_final_report(duration)
            
        except Exception as e:
            logger.error(f"❌ Erro crítico no deploy: {e}")
            raise
    
    async def _execute_step(self, step: str) -> bool:
        """Executa um passo específico do deploy"""
        try:
            if step == "check_prerequisites":
                return await self._check_prerequisites()
            elif step == "start_citus_cluster":
                return await self._start_citus_cluster()
            elif step == "setup_migrations":
                return await self._setup_migrations()
            elif step == "setup_seeds":
                return await self._setup_seeds()
            elif step == "validate_cluster":
                return await self._validate_cluster()
            elif step == "generate_report":
                return await self._generate_report()
            else:
                logger.error(f"Passo desconhecido: {step}")
                return False
                
        except Exception as e:
            logger.error(f"Erro no passo {step}: {e}")
            return False
    
    async def _check_prerequisites(self) -> bool:
        """Verifica pré-requisitos"""
        logger.info("🔍 Verificando pré-requisitos...")
        
        # Verificar Docker
        try:
            result = subprocess.run(['docker', '--version'], capture_output=True, text=True, shell=True)
            if result.returncode == 0:
                logger.info(f"✅ Docker: {result.stdout.strip()}")
            else:
                logger.error("❌ Docker não encontrado")
                return False
        except Exception as e:
            logger.error(f"❌ Erro ao verificar Docker: {e}")
            return False
        
        # Verificar Docker Compose
        try:
            result = subprocess.run(['docker-compose', '--version'], capture_output=True, text=True, shell=True)
            if result.returncode == 0:
                logger.info(f"✅ Docker Compose: {result.stdout.strip()}")
            else:
                logger.error("❌ Docker Compose não encontrado")
                return False
        except Exception as e:
            logger.error(f"❌ Erro ao verificar Docker Compose: {e}")
            return False
        
        # Verificar arquivos necessários
        required_files = [
            'docker-compose.yml',
            'setup-citus-migrations.py',
            'setup-citus-seeds.py'
        ]
        
        for file in required_files:
            if not Path(file).exists():
                logger.error(f"❌ Arquivo necessário não encontrado: {file}")
                return False
            else:
                logger.info(f"✅ {file} encontrado")
        
        return True
    
    async def _start_citus_cluster(self) -> bool:
        """Inicia o cluster Citus"""
        logger.info("🏗️ Iniciando cluster Citus...")
        
        try:
            # Criar rede se não existir
            subprocess.run(['docker', 'network', 'create', 'trix-network'], 
                         capture_output=True, shell=True)
            
            # Iniciar cluster
            result = subprocess.run(['docker-compose', 'up', '-d'], 
                                  capture_output=True, text=True, shell=True)
            
            if result.returncode == 0:
                logger.info("✅ Cluster Citus iniciado")
                # Aguardar inicialização
                logger.info("⏳ Aguardando inicialização dos serviços...")
                await asyncio.sleep(60)  # Aguardar 1 minuto
                return True
            else:
                logger.error(f"❌ Erro ao iniciar cluster: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Erro ao iniciar cluster: {e}")
            return False
    
    async def _setup_migrations(self) -> bool:
        """Configura migrations"""
        logger.info("🔄 Configurando migrations...")
        
        try:
            # Executar script de migrations
            result = subprocess.run(['python', 'setup-citus-migrations.py'], 
                                  capture_output=True, text=True, shell=True)
            
            if result.returncode == 0:
                logger.info("✅ Migrations configuradas")
                return True
            else:
                logger.error(f"❌ Erro nas migrations: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Erro ao configurar migrations: {e}")
            return False
    
    async def _setup_seeds(self) -> bool:
        """Configura seeds"""
        logger.info("🌱 Configurando seeds...")
        
        try:
            # Executar script de seeds
            result = subprocess.run(['python', 'setup-citus-seeds.py'], 
                                  capture_output=True, text=True, shell=True)
            
            if result.returncode == 0:
                logger.info("✅ Seeds configurados")
                return True
            else:
                logger.error(f"❌ Erro nos seeds: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Erro ao configurar seeds: {e}")
            return False
    
    async def _validate_cluster(self) -> bool:
        """Valida o cluster"""
        logger.info("🔍 Validando cluster...")
        
        try:
            # Verificar containers
            result = subprocess.run(['docker-compose', 'ps'], 
                                  capture_output=True, text=True, shell=True)
            
            if result.returncode == 0:
                logger.info("✅ Containers verificados")
                logger.info(result.stdout)
            else:
                logger.error("❌ Erro na verificação dos containers")
                return False
            
            # Verificar conectividade básica
            containers = [
                'trix-citus-core-coordinator',
                'trix-citus-shared-coordinator', 
                'trix-citus-tenant-coordinator',
                'trix-citus-worker-1',
                'trix-citus-worker-2'
            ]
            
            for container in containers:
                result = subprocess.run(['docker', 'exec', container, 'pg_isready', '-U', 'postgres'], 
                                      capture_output=True, text=True, shell=True)
                if result.returncode == 0:
                    logger.info(f"✅ {container}: OK")
                else:
                    logger.error(f"❌ {container}: FAIL")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro na validação: {e}")
            return False
    
    async def _generate_report(self) -> bool:
        """Gera relatório"""
        logger.info("📊 Gerando relatório...")
        
        try:
            # Informações de acesso
            access_info = {
                'Core Coordinator': 'localhost:5432',
                'Shared Coordinator': 'localhost:5433', 
                'Tenant Coordinator': 'localhost:5434',
                'Worker 1': 'localhost:5435',
                'Worker 2': 'localhost:5436',
                'PgAdmin': 'http://localhost:8081'
            }
            
            logger.info("🌐 Pontos de Acesso:")
            for service, url in access_info.items():
                logger.info(f"   {service}: {url}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro na geração do relatório: {e}")
            return False
    
    async def _generate_final_report(self, duration: float):
        """Gera relatório final"""
        logger.info("=" * 70)
        logger.info("📊 RELATÓRIO FINAL DO DEPLOY CITUS")
        logger.info("=" * 70)
        
        total_steps = len(self.steps)
        completed_count = len(self.completed_steps)
        failed_count = len(self.failed_steps)
        success_rate = (completed_count / total_steps) * 100
        
        logger.info(f"🎯 Total de passos: {total_steps}")
        logger.info(f"✅ Passos concluídos: {completed_count}")
        logger.info(f"❌ Passos falharam: {failed_count}")
        logger.info(f"📈 Taxa de sucesso: {success_rate:.1f}%")
        logger.info(f"⏱️ Tempo total: {duration:.1f}s")
        
        if self.completed_steps:
            logger.info("\n✅ Passos concluídos:")
            for step in self.completed_steps:
                logger.info(f"   - {step}")
        
        if self.failed_steps:
            logger.info("\n❌ Passos que falharam:")
            for step in self.failed_steps:
                logger.info(f"   - {step}")
        
        if success_rate == 100:
            logger.info("\n🎉 DEPLOY CONCLUÍDO COM SUCESSO!")
            logger.info("🌐 Cluster Citus está pronto para uso")
            logger.info("\n📋 Próximos passos:")
            logger.info("1. Testar conectividade dos microserviços")
            logger.info("2. Verificar distribuição de dados")
            logger.info("3. Monitorar performance")
        else:
            logger.info("\n⚠️ Deploy parcialmente concluído")
            logger.info("🔧 Verifique os passos que falharam")
        
        logger.info("=" * 70)

async def main():
    """Função principal"""
    deployer = CitusDeployManager()
    await deployer.deploy_complete_citus()

if __name__ == "__main__":
    asyncio.run(main())
