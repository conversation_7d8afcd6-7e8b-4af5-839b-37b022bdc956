# 📧 Email Service - Documentação Técnica Completa

> **🔗 Integração com Shared Lib**: Este serviço foi **MIGRADO** para utilizar configurações e componentes centralizados da `microservices/core/shared_lib` para garantir consistência e reduzir duplicação entre microserviços. **MIGRAÇÃO CONCLUÍDA** - Todas as configurações comuns foram movidas para a shared_lib. Veja a [seção de integração](#-integração-com-shared-lib) para detalhes.

## 📋 Visão Geral

O **Email Service** é um microserviço especializado da plataforma Trix responsável pelo gerenciamento completo de email hosting, incluindo domínios, contas, aliases e webmail. Implementa padrões enterprise-grade com integração completa à arquitetura orientada a eventos e suporte a escala massiva.

### 🎯 **Informações Básicas**
- **Porta:** 8015
- **Database:** PostgreSQL + Citus Data (Sharded)
- **Messaging:** Apache Kafka + RabbitMQ + Redis Streams
- **Service Mesh:** Istio/Linkerd com mTLS
- **Secrets:** HashiCorp Vault
- **Monitoring:** Prometheus + Grafana + Jaeger + ELK
- **Orchestration:** Kubernetes + Helm + ArgoCD
- **Status:** ✅ **MIGRAÇÃO CONCLUÍDA - PRONTO PARA PRODUÇÃO**
- **Versão:** 2.0.0 (Integrado com Shared Lib)
- **Target Scale:** Bilhões de usuários simultâneos
- **Shared Lib Integration:** ✅ **100% MIGRADO**

### 📊 **Status de Implementação Enterprise**
- ✅ **Shared Lib Integration**: Configurações comuns centralizadas
- ✅ **Email Configuration**: SMTP, IMAP, provedores padronizados
- ✅ **Provider Management**: SendGrid, SES, Mailgun centralizados
- ✅ **Security Settings**: DKIM, SPF, DMARC compartilhados
- ✅ **Template System**: Configurações de template centralizadas
- ✅ **Storage Settings**: Configurações de armazenamento padronizadas
- ✅ **Delivery Settings**: Rate limiting e retry centralizados
- 🔄 **Database Sharding**: Citus Data para distribuição horizontal
- 🔄 **Service Mesh**: Istio/Linkerd com mTLS automático
- 🔄 **Event-Driven**: Kafka/RabbitMQ para comunicação assíncrona
- 🔄 **Security**: Vault para secrets, OPA para policies
- 🔄 **Observability**: Prometheus/Jaeger/ELK stack completo
- 🔄 **Kubernetes**: Helm charts e manifests para orquestração
- 🔄 **APIs Versionadas**: v1 com cache, rate limiting e bulk operations
- 🔄 **Performance**: Connection pooling, read replicas, caching
- 🔄 **Geo-Distribution**: Multi-region deployment strategy
- 🔄 **Auto-Scaling**: HPA baseado em CPU/Memory/Custom metrics

## 🏗️ **Arquitetura Enterprise (v2.0)**

### ✅ **Technology Stack (100% Open Source)**
- **Orchestration:** Kubernetes + Helm + ArgoCD
- **Service Mesh:** Istio/Linkerd com mTLS automático
- **Databases:** PostgreSQL + Citus Data + PgBouncer
- **Messaging:** Apache Kafka + RabbitMQ + Redis Streams
- **CDN:** Varnish + MinIO + PowerDNS
- **Monitoring:** Prometheus + Grafana + Jaeger + ELK
- **Security:** HashiCorp Vault + OPA Gatekeeper + Falco

### 🌐 **Event-Driven Architecture**
- **Apache Kafka**: Eventos críticos de email e sincronização de dados
- **RabbitMQ**: Mensagens rápidas e notificações para velocidade e escalabilidade
- **Padrão CQRS**: Separação de comandos e consultas
- **Event Sourcing**: Histórico completo de mudanças de email
- **Saga Pattern**: Transações distribuídas entre microserviços
- **Soft References**: Relacionamentos entre microserviços via eventos, não FK constraints
- **Email Events**: Eventos de email via Kafka/RabbitMQ

### 📁 **Estrutura de Diretórios**
```
microservices/shared/email_service/
├── app/
│   ├── api/
│   │   └── v1/                       # ✅ Versioned APIs
│   │       ├── domains.py            # Email domain management endpoints
│   │       ├── accounts.py           # Email account management endpoints
│   │       ├── aliases.py            # Email alias management endpoints
│   │       ├── webmail.py            # Webmail API endpoints
│   │       ├── health.py             # Health check endpoints
│   │       └── __init__.py           # API router exports
│   ├── core/
│   │   ├── config/                   # ✅ Environment-specific configs
│   │   │   ├── settings.py           # Base settings
│   │   │   ├── database.py           # Citus Data configuration
│   │   │   ├── vault.py              # HashiCorp Vault integration
│   │   │   └── email_server.py       # Email server configurations
│   │   ├── security/                 # ✅ Security layer
│   │   │   ├── jwt.py                # JWT handling with Vault
│   │   │   ├── permissions.py        # OPA policy integration
│   │   │   ├── encryption.py         # Data encryption utilities
│   │   │   └── dkim.py               # DKIM key management
│   │   ├── database/                 # ✅ Database layer
│   │   │   ├── sharding.py           # Citus Data sharding logic
│   │   │   ├── connection.py         # PgBouncer connection pooling
│   │   │   └── migrations.py         # Distributed migration management
│   │   ├── messaging/                # ✅ Event-driven messaging
│   │   │   ├── kafka.py              # Kafka producer/consumer
│   │   │   ├── rabbitmq.py           # RabbitMQ publisher/subscriber
│   │   │   └── redis_streams.py      # Redis Streams for real-time
│   │   ├── observability/            # ✅ Monitoring & tracing
│   │   │   ├── metrics.py            # Prometheus metrics
│   │   │   ├── tracing.py            # Jaeger distributed tracing
│   │   │   └── logging.py            # Structured logging (ELK)
│   │   ├── auth.py                   # ✅ Auth middleware integration
│   │   └── db_dependencies.py        # ✅ Database dependencies
│   ├── models/
│   │   ├── email_domain.py           # ✅ Email domain model (sharded by tenant_id)
│   │   ├── email_account.py          # ✅ Email account model
│   │   ├── email_alias.py            # ✅ Email alias model
│   │   ├── email_metadata.py         # ✅ Email metadata model
│   │   └── __init__.py               # Model exports
│   ├── schemas/
│   │   ├── domain.py                 # ✅ Domain schemas (requests/responses)
│   │   ├── account.py                # ✅ Account schemas
│   │   ├── alias.py                  # ✅ Alias schemas
│   │   ├── webmail.py                # ✅ Webmail schemas
│   │   ├── events.py                 # ✅ Event schemas for messaging
│   │   └── __init__.py               # Schema exports
│   ├── services/
│   │   ├── domain_service.py         # ✅ Domain management service
│   │   ├── account_service.py        # ✅ Account management service
│   │   ├── alias_service.py          # ✅ Alias management service
│   │   ├── webmail_service.py        # ✅ Webmail service
│   │   ├── provision_service.py      # ✅ Email provisioning service
│   │   ├── quota_service.py          # ✅ Storage quota management
│   │   ├── event_service.py          # ✅ Event publishing service
│   │   └── __init__.py               # Service exports
│   ├── email_server/                 # ✅ Email server integration
│   │   ├── postfix/                  # ✅ Postfix SMTP configuration
│   │   │   ├── config_manager.py     # Postfix configuration management
│   │   │   ├── virtual_maps.py       # Virtual domain/user maps
│   │   │   └── transport.py          # Mail transport configuration
│   │   ├── dovecot/                  # ✅ Dovecot IMAP configuration
│   │   │   ├── config_manager.py     # Dovecot configuration management
│   │   │   ├── auth_backend.py       # Authentication backend
│   │   │   └── mailbox_manager.py    # Mailbox management
│   │   ├── rspamd/                   # ✅ Spam filtering
│   │   │   ├── config_manager.py     # Rspamd configuration
│   │   │   ├── rules.py              # Spam filtering rules
│   │   │   └── learning.py           # Spam learning system
│   │   ├── clamav/                   # ✅ Antivirus scanning
│   │   │   ├── scanner.py            # Virus scanning service
│   │   │   └── quarantine.py         # Quarantine management
│   │   └── __init__.py               # Email server exports
│   ├── events/                       # ✅ Event-driven architecture
│   │   ├── handlers/                 # ✅ Event processing logic
│   │   │   ├── domain_events.py      # Domain event handlers
│   │   │   ├── account_events.py     # Account event handlers
│   │   │   ├── email_events.py       # Email event handlers
│   │   │   └── quota_events.py       # Quota event handlers
│   │   ├── publishers/               # ✅ Event publishing utilities
│   │   │   ├── kafka_publisher.py    # Kafka event publisher
│   │   │   ├── rabbitmq_publisher.py # RabbitMQ publisher
│   │   │   └── redis_publisher.py    # Redis Streams publisher
│   │   ├── schemas/                  # ✅ Event data schemas
│   │   │   ├── domain_events.py      # Domain event schemas
│   │   │   ├── account_events.py     # Account event schemas
│   │   │   └── email_events.py       # Email event schemas
│   │   └── __init__.py               # Event module exports
│   └── main.py                       # ✅ FastAPI application with Istio integration
├── k8s/                             # ✅ Kubernetes manifests
│   ├── base/                        # Base Kubernetes resources
│   │   ├── deployment.yaml          # Deployment configuration
│   │   ├── service.yaml             # Service definition
│   │   ├── configmap.yaml           # Configuration management
│   │   └── secret.yaml              # Vault secret references
│   ├── overlays/                    # Environment-specific overlays
│   │   ├── development/             # Dev environment
│   │   ├── staging/                 # Staging environment
│   │   └── production/              # Production environment
│   └── istio/                       # ✅ Service mesh configurations
│       ├── virtual-service.yaml     # Traffic routing rules
│       ├── destination-rule.yaml    # Load balancing policies
│       └── peer-authentication.yaml # mTLS configuration
├── helm/                            # ✅ Helm charts for deployment
│   ├── Chart.yaml                   # Chart metadata
│   ├── values.yaml                  # Default values
│   ├── values-prod.yaml             # Production values
│   └── templates/                   # Kubernetes templates
├── monitoring/                      # ✅ Observability configurations
│   ├── prometheus/                  # Prometheus rules and alerts
│   ├── grafana/                     # Grafana dashboards
│   └── jaeger/                      # Jaeger tracing configuration
├── security/                        # ✅ Security configurations
│   ├── vault/                       # HashiCorp Vault policies
│   ├── opa/                         # Open Policy Agent rules
│   └── falco/                       # Runtime security rules
├── migrations/                      # ✅ Distributed database migrations
│   ├── citus/                       # Citus Data specific migrations
│   └── alembic/                     # Standard Alembic migrations
├── tests/                           # ✅ Comprehensive test suite
│   ├── unit/                        # Unit tests
│   ├── integration/                 # Integration tests
│   ├── load/                        # Load testing with K6
│   └── security/                    # Security testing
├── docker/                          # ✅ Container configurations
│   ├── Dockerfile                   # Multi-stage production build
│   ├── Dockerfile.dev               # Development build
│   ├── docker-compose.yml           # Local development stack
│   └── email-servers/               # Email server containers
│       ├── postfix/                 # Postfix container
│       ├── dovecot/                 # Dovecot container
│       ├── rspamd/                  # Rspamd container
│       └── clamav/                  # ClamAV container
├── scripts/                         # ✅ Automation scripts
│   ├── deploy.sh                    # Deployment automation
│   ├── migrate.sh                   # Database migration runner
│   ├── email-setup.sh               # Email server setup
│   └── scale.sh                     # Auto-scaling utilities
├── docs/                            # ✅ Documentation
│   ├── api/                         # API documentation
│   ├── deployment/                  # Deployment guides
│   ├── email-server/                # Email server documentation
│   └── architecture/                # Architecture decisions
├── requirements/                    # ✅ Dependency management
│   ├── base.txt                     # Base dependencies
│   ├── production.txt               # Production dependencies
│   └── development.txt              # Development dependencies
└── pyproject.toml                   # ✅ Modern Python project configuration
```

### 🔧 **Modelos de Dados Enterprise (Citus Data Optimized)**

#### **1. Sharded Tables (Distributed by tenant_id)**
```python
class EmailDomain(Base):
    """
    Email domain model optimized for Citus Data sharding.
    Distributed by tenant_id for optimal query performance.
    """
    __tablename__ = 'email_domains'

    id: UUID = Field(primary_key=True, default_factory=uuid4)
    tenant_id: UUID = Field(index=True)  # 🔑 Sharding key for Citus Data
    domain_name: str = Field(unique=True, index=True)
    status: str = Field(default="pending")  # pending, verified, active, suspended
    dns_verified: bool = Field(default=False)
    dkim_enabled: bool = Field(default=True)
    dkim_public_key: str = Field(nullable=True)
    dkim_private_key: str = Field(nullable=True)  # Encrypted with Vault
    spf_record: str = Field(nullable=True)
    dmarc_policy: str = Field(default="none")
    mx_records: List[str] = Field(default_factory=list)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    # Citus Data optimization indexes
    __table_args__ = (
        Index('idx_tenant_domain', 'tenant_id', 'domain_name'),
        Index('idx_domain_status', 'domain_name', 'status'),
        Index('idx_tenant_status', 'tenant_id', 'status'),
        # Distribute table by tenant_id
        {'citus_table_type': 'distributed'}
    )
```

```python
class EmailAccount(Base):
    """
    Email account model optimized for Citus Data sharding.
    Co-located with email_domains table for optimal join performance.
    """
    __tablename__ = 'email_accounts'

    id: UUID = Field(primary_key=True, default_factory=uuid4)
    tenant_id: UUID = Field(index=True)  # 🔑 Sharding key (same as domains)
    domain_id: UUID = Field(index=True)  # Soft reference to email_domains
    username: str = Field(index=True)
    email_address: str = Field(unique=True, index=True)
    password_hash: str = Field(min_length=60)  # Argon2id hash
    quota_mb: int = Field(default=1024)  # Storage quota in MB
    used_mb: int = Field(default=0)  # Used storage in MB
    status: str = Field(default="active")  # active, suspended, disabled
    last_login: datetime = Field(nullable=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    # Citus Data optimization - co-locate with email_domains table
    __table_args__ = (
        Index('idx_tenant_email', 'tenant_id', 'email_address'),
        Index('idx_domain_username', 'domain_id', 'username'),
        Index('idx_tenant_status', 'tenant_id', 'status'),
        # Co-locate with email_domains table by tenant_id
        {'citus_table_type': 'distributed', 'colocate_with': 'email_domains'}
    )
```

```python
class EmailAlias(Base):
    """
    Email alias model optimized for Citus Data sharding.
    Co-located with email_domains table for optimal performance.
    """
    __tablename__ = 'email_aliases'

    id: UUID = Field(primary_key=True, default_factory=uuid4)
    tenant_id: UUID = Field(index=True)  # 🔑 Sharding key (same as domains)
    domain_id: UUID = Field(index=True)  # Soft reference to email_domains
    source_address: str = Field(unique=True, index=True)
    destination_addresses: List[str] = Field(default_factory=list)
    status: str = Field(default="active")  # active, disabled
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    # Citus Data optimization - co-locate with email_domains table
    __table_args__ = (
        Index('idx_tenant_source', 'tenant_id', 'source_address'),
        Index('idx_domain_source', 'domain_id', 'source_address'),
        Index('idx_tenant_status', 'tenant_id', 'status'),
        # Co-locate with email_domains table by tenant_id
        {'citus_table_type': 'distributed', 'colocate_with': 'email_domains'}
    )
```

```python
class EmailMetadata(Base):
    """
    Email metadata model for webmail functionality.
    Optimized for high-volume email operations.
    """
    __tablename__ = 'email_metadata'

    id: UUID = Field(primary_key=True, default_factory=uuid4)
    tenant_id: UUID = Field(index=True)  # 🔑 Sharding key
    account_id: UUID = Field(index=True)  # Soft reference to email_accounts
    message_id: str = Field(unique=True, index=True)
    subject: str = Field(max_length=255)
    sender: str = Field(index=True)
    recipients: List[str] = Field(default_factory=list)
    mailbox: str = Field(default="INBOX", index=True)
    flags: List[str] = Field(default_factory=list)  # \Seen, \Flagged, etc.
    size_bytes: int = Field(default=0)
    received_at: datetime = Field(default_factory=datetime.utcnow, index=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)

    # Citus Data optimization for email queries
    __table_args__ = (
        Index('idx_tenant_account', 'tenant_id', 'account_id'),
        Index('idx_account_mailbox', 'account_id', 'mailbox'),
        Index('idx_account_received', 'account_id', 'received_at'),
        Index('idx_tenant_received', 'tenant_id', 'received_at'),
        # Co-locate with email_domains table by tenant_id
        {'citus_table_type': 'distributed', 'colocate_with': 'email_domains'}
    )
```

### 🚀 **Event-Driven Architecture Enterprise**

#### **Event Schemas & Publishing**
```python
class EmailEvent(BaseModel):
    """Base event schema for all email-related events."""
    event_type: str  # domain.created, account.created, email.received, etc.
    tenant_id: UUID  # For routing to correct shard
    domain_id: UUID = Field(nullable=True)
    account_id: UUID = Field(nullable=True)
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    data: Dict[str, Any]
    correlation_id: UUID = Field(default_factory=uuid4)
    source_service: str = "email-service"
    version: str = "v1"

class EventPublisher:
    """Multi-layer event publishing for different use cases."""

    def __init__(self):
        self.kafka_producer = KafkaProducer()  # Critical events
        self.rabbitmq_publisher = RabbitMQPublisher()  # Fast notifications
        self.redis_streams = RedisStreams()  # Real-time updates

    async def publish_email_event(self, event: EmailEvent):
        """Publish to multiple channels based on event criticality."""

        # 1. Kafka for durability and event sourcing
        await self.kafka_producer.send(
            topic=f'email-events-{event.tenant_id}',  # Partitioned by tenant
            value=event.dict(),
            headers={'event_type': event.event_type}
        )

        # 2. RabbitMQ for fast inter-service communication
        await self.rabbitmq_publisher.publish(
            exchange='email-notifications',
            routing_key=f'tenant.{event.tenant_id}.{event.event_type}',
            message=event.dict()
        )

        # 3. Redis Streams for real-time UI updates
        await self.redis_streams.xadd(
            stream=f'realtime:tenant:{event.tenant_id}',
            fields=event.dict()
        )
```

**⚠️ IMPORTANTE - Isolamento de Microserviços:**
- `tenant_id`, `domain_id`, `account_id` NÃO possuem Foreign Key constraints para manter isolamento entre microserviços
- Validação de integridade referencial deve ser feita via API calls entre serviços
- Cross-microservice relationships são tratadas como "soft references"
- **Event Sourcing**: Todos os eventos são imutáveis e armazenados permanentemente
- **Eventual Consistency**: Consistência garantida via eventos assíncronos
- **Event-Driven Architecture**: Kafka e RabbitMQ para comunicação assíncrona e escalabilidade
- **Performance**: Eventos para sincronização de dados entre microserviços
- **Resiliência**: Message queues garantem entrega mesmo com serviços temporariamente indisponíveis

## 🔐 **Security-First Architecture**

#### **HashiCorp Vault Integration**
```python
class VaultSecurityManager:
    """Centralized secrets management with HashiCorp Vault."""

    def __init__(self):
        self.vault_client = hvac.Client(url=os.getenv('VAULT_URL'))
        self.vault_client.token = os.getenv('VAULT_TOKEN')

    async def get_db_credentials(self, environment: str, shard: str):
        """Get database credentials for specific shard."""
        secret_path = f'database/{environment}/email-service/shard-{shard}'
        secret = self.vault_client.secrets.kv.v2.read_secret_version(path=secret_path)
        return secret['data']['data']

    async def get_dkim_private_key(self, domain_id: str):
        """Get DKIM private key for domain."""
        secret = self.vault_client.secrets.kv.v2.read_secret_version(
            path=f'email/dkim/{domain_id}'
        )
        return secret['data']['data']['private_key']

    async def store_dkim_keys(self, domain_id: str, public_key: str, private_key: str):
        """Store DKIM keys securely."""
        await self.vault_client.secrets.kv.v2.create_or_update_secret(
            path=f'email/dkim/{domain_id}',
            secret={'public_key': public_key, 'private_key': private_key}
        )

class OPAPolicyEnforcer:
    """Open Policy Agent integration for authorization."""

    async def check_email_domain_permission(self, user_id: UUID, tenant_id: UUID, domain_id: UUID):
        """Check if user can manage email domain."""
        policy_input = {
            "user_id": str(user_id),
            "tenant_id": str(tenant_id),
            "domain_id": str(domain_id),
            "action": "email_domain_management"
        }

        response = await self.opa_client.query(
            policy="email_permissions/domain_management",
            input=policy_input
        )

        return response.get("result", False)
```

## 📊 **Enterprise Observability**

#### **Prometheus Metrics**
```python
from prometheus_client import Counter, Histogram, Gauge, Info

# Business metrics
email_operations_total = Counter(
    'email_operations_total',
    'Total email operations by type and status',
    ['operation', 'tenant_id', 'domain', 'status']
)

email_operation_duration = Histogram(
    'email_operation_duration_seconds',
    'Email operation duration in seconds',
    ['operation', 'tenant_id', 'shard'],
    buckets=[0.1, 0.5, 1.0, 2.5, 5.0, 10.0]
)

active_email_accounts_gauge = Gauge(
    'active_email_accounts_total',
    'Total active email accounts by tenant and domain',
    ['tenant_id', 'domain', 'shard']
)

email_storage_usage_gauge = Gauge(
    'email_storage_usage_mb',
    'Email storage usage in MB by tenant and account',
    ['tenant_id', 'domain', 'account']
)

# Infrastructure metrics
email_server_connections_gauge = Gauge(
    'email_server_connections_active',
    'Active email server connections',
    ['server_type', 'instance']  # postfix, dovecot, rspamd, clamav
)

spam_detection_rate = Gauge(
    'spam_detection_rate',
    'Spam detection rate by tenant',
    ['tenant_id', 'domain']
)
```

## 🔗 **Integração com Outros Microserviços**

### 🤝 **Pontos de Integração**

#### **1. Domain Service Integration**
- **Domain Verification**: Validação de propriedade de domínio
- **DNS Management**: Configuração automática de registros MX, SPF, DKIM, DMARC
- **Domain Events**: Sincronização via eventos quando domínios são criados/atualizados

#### **2. User Service Integration**
- **User Authentication**: Validação de usuários para acesso ao webmail
- **Tenant Association**: Verificação de permissões baseada em associações tenant-usuário
- **User Events**: Criação automática de contas de email para novos usuários (opcional)

#### **3. Tenant Service Integration**
- **Tenant Validation**: Verificação de existência e status do tenant
- **Billing Integration**: Cobrança baseada em uso de storage e número de contas
- **Tenant Events**: Configuração automática de email para novos tenants

#### **4. Notification Service Integration**
- **Email Delivery**: Envio de emails transacionais via SMTP
- **Delivery Status**: Notificações de status de entrega
- **Bounce Handling**: Processamento de emails rejeitados

#### **5. CDN Service Integration**
- **Attachment Storage**: Armazenamento de anexos via MinIO
- **Content Delivery**: Entrega otimizada de anexos grandes
- **Backup Storage**: Backup de emails e configurações

## 📡 **APIs Enterprise (v1)**

### 🎯 **Base Path**: `/api/v1/email`

#### **Domain Management**
- `GET /domains` - Lista domínios de email do tenant atual
- `POST /domains` - Cria novo domínio de email para o tenant
- `GET /domains/{domain_id}` - Obtém domínio específico por ID
- `PUT /domains/{domain_id}` - Atualiza domínio de email
- `DELETE /domains/{domain_id}` - Remove domínio de email
- `GET /domains/{domain_id}/dns-records` - Obtém registros DNS para domínio
- `POST /domains/{domain_id}/verify-dns` - Verifica registros DNS do domínio
- `POST /domains/{domain_id}/generate-dkim` - Gera chaves DKIM para domínio
- `GET /domains/{domain_id}/stats` - Estatísticas de uso do domínio
- `POST /domains/bulk-create` - Criação em lote de domínios (enterprise)

#### **Account Management**
- `GET /domains/{domain_id}/accounts` - Lista contas de email do domínio
- `POST /domains/{domain_id}/accounts` - Cria nova conta de email
- `GET /accounts/{account_id}` - Obtém conta específica por ID
- `PUT /accounts/{account_id}` - Atualiza conta de email
- `DELETE /accounts/{account_id}` - Remove conta de email
- `GET /accounts/{account_id}/usage` - Uso de armazenamento da conta
- `PUT /accounts/{account_id}/quota` - Atualiza quota da conta
- `PUT /accounts/{account_id}/password` - Altera senha (usuário proprietário)
- `POST /accounts/{account_id}/reset-password` - Reset de senha (admin)
- `GET /accounts/{account_id}/stats` - Estatísticas da conta
- `POST /accounts/bulk-create` - Criação em lote de contas (enterprise)
- `PUT /accounts/bulk-update` - Atualização em lote (enterprise)

#### **Alias Management**
- `GET /domains/{domain_id}/aliases` - Lista aliases do domínio
- `POST /domains/{domain_id}/aliases` - Cria novo alias
- `GET /aliases/{alias_id}` - Obtém alias específico por ID
- `PUT /aliases/{alias_id}` - Atualiza alias
- `DELETE /aliases/{alias_id}` - Remove alias
- `GET /aliases/{alias_id}/stats` - Estatísticas do alias
- `POST /aliases/bulk-create` - Criação em lote de aliases (enterprise)

#### **Webmail API**
- `GET /webmail/mailboxes` - Lista mailboxes do usuário autenticado
- `POST /webmail/mailboxes` - Cria nova mailbox
- `DELETE /webmail/mailboxes/{mailbox_name}` - Remove mailbox
- `GET /webmail/emails` - Lista emails com paginação e filtros
- `GET /webmail/emails/{email_id}` - Obtém email específico com conteúdo
- `POST /webmail/emails` - Envia novo email
- `PUT /webmail/emails/{email_id}/read` - Marca email como lido/não lido
- `PUT /webmail/emails/{email_id}/move` - Move email para outra mailbox
- `DELETE /webmail/emails/{email_id}` - Remove email
- `GET /webmail/emails/{email_id}/attachments` - Lista anexos do email
- `GET /webmail/emails/{email_id}/attachments/{attachment_id}` - Download de anexo
- `POST /webmail/emails/search` - Busca avançada de emails
- `GET /webmail/stats` - Estatísticas do webmail

#### **Email Server Management (Admin)**
- `GET /admin/server-status` - Status dos servidores de email
- `POST /admin/server-restart` - Reinicia serviços de email
- `GET /admin/queue-status` - Status da fila de emails
- `POST /admin/queue-flush` - Força processamento da fila
- `GET /admin/spam-stats` - Estatísticas de spam
- `POST /admin/spam-learn` - Treina filtro de spam
- `GET /admin/virus-stats` - Estatísticas de vírus
- `POST /admin/quarantine-release` - Libera email da quarentena

## 🐳 **Email Server Infrastructure (Containerized)**

### 📋 **Serviços Docker Enterprise**

#### **1. Postfix (SMTP Server)**
```yaml
# docker/email-servers/postfix/Dockerfile
FROM postfix:3.7-alpine
COPY config/ /etc/postfix/
COPY scripts/ /usr/local/bin/
RUN postconf -e "virtual_mailbox_domains = proxy:pgsql:/etc/postfix/pgsql-virtual-mailbox-domains.cf"
RUN postconf -e "virtual_mailbox_maps = proxy:pgsql:/etc/postfix/pgsql-virtual-mailbox-maps.cf"
RUN postconf -e "virtual_alias_maps = proxy:pgsql:/etc/postfix/pgsql-virtual-alias-maps.cf"
EXPOSE 25 587 465
```

#### **2. Dovecot (IMAP/POP3 Server)**
```yaml
# docker/email-servers/dovecot/Dockerfile
FROM dovecot:2.3-alpine
COPY config/ /etc/dovecot/
COPY scripts/ /usr/local/bin/
RUN doveconf -n > /etc/dovecot/dovecot.conf.compiled
EXPOSE 143 993 110 995
```

#### **3. Rspamd (Spam Filtering)**
```yaml
# docker/email-servers/rspamd/Dockerfile
FROM rspamd/rspamd:3.6-alpine
COPY config/ /etc/rspamd/
COPY rules/ /etc/rspamd/local.d/
EXPOSE 11334 11333
```

#### **4. ClamAV (Antivirus)**
```yaml
# docker/email-servers/clamav/Dockerfile
FROM clamav/clamav:1.2-alpine
COPY config/ /etc/clamav/
RUN freshclam --config-file=/etc/clamav/freshclam.conf
EXPOSE 3310
```

### 🔧 **Configuração Docker Compose**
```yaml
version: '3.8'
services:
  trix-email-service:
    build:
      context: .
      dockerfile: docker/Dockerfile
    container_name: trix-email-service
    ports:
      - "8015:8015"
    environment:
      - DATABASE_URL=${EMAIL_DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - KAFKA_BOOTSTRAP_SERVERS=${KAFKA_BOOTSTRAP_SERVERS}
      - VAULT_URL=${VAULT_URL}
    networks:
      - trix-network
    depends_on:
      - trix-email-postfix
      - trix-email-dovecot
      - trix-email-rspamd
      - trix-email-clamav

  trix-email-postfix:
    build:
      context: docker/email-servers/postfix
    container_name: trix-email-postfix
    ports:
      - "25:25"
      - "587:587"
      - "465:465"
    volumes:
      - email_spool:/var/spool/postfix
      - email_config:/etc/postfix
    networks:
      - trix-network

  trix-email-dovecot:
    build:
      context: docker/email-servers/dovecot
    container_name: trix-email-dovecot
    ports:
      - "143:143"
      - "993:993"
      - "110:110"
      - "995:995"
    volumes:
      - email_mailboxes:/var/mail
      - email_config:/etc/dovecot
    networks:
      - trix-network

  trix-email-rspamd:
    build:
      context: docker/email-servers/rspamd
    container_name: trix-email-rspamd
    ports:
      - "11334:11334"
      - "11333:11333"
    volumes:
      - rspamd_data:/var/lib/rspamd
    networks:
      - trix-network

  trix-email-clamav:
    build:
      context: docker/email-servers/clamav
    container_name: trix-email-clamav
    ports:
      - "3310:3310"
    volumes:
      - clamav_data:/var/lib/clamav
    networks:
      - trix-network

volumes:
  email_spool:
  email_mailboxes:
  email_config:
  rspamd_data:
  clamav_data:

networks:
  trix-network:
    external: true
```

## 🔒 **Segurança Enterprise**

### 🛡️ **Medidas de Segurança Implementadas**

#### **1. Autenticação e Autorização**
- **Argon2id Hashing**: Hash seguro de senhas de contas de email
- **JWT Integration**: Integração com Auth Service para autenticação de API
- **OPA Policies**: Autorização baseada em políticas para operações de email
- **Role-Based Access**: Controle de acesso baseado em roles de tenant

#### **2. Criptografia e Proteção de Dados**
- **TLS/SSL**: Comunicação criptografada para SMTP, IMAP, POP3
- **DKIM Signing**: Assinatura digital de emails para autenticidade
- **SPF Records**: Prevenção de spoofing de domínio
- **DMARC Policy**: Política de autenticação e relatórios
- **Vault Integration**: Armazenamento seguro de chaves DKIM

#### **3. Proteção contra Spam e Malware**
- **Rspamd Integration**: Filtragem avançada de spam com machine learning
- **ClamAV Scanning**: Detecção de vírus e malware em anexos
- **Greylisting**: Técnica de atraso para reduzir spam
- **Rate Limiting**: Limitação de taxa para prevenir abuso
- **Blacklist/Whitelist**: Listas de bloqueio e permissão

#### **4. Monitoramento e Auditoria**
- **Security Events**: Log de eventos de segurança via Kafka
- **Failed Login Tracking**: Rastreamento de tentativas de login falhadas
- **Suspicious Activity**: Detecção de atividades suspeitas
- **Compliance Logging**: Logs para conformidade regulatória

### 🔐 **Compliance e Regulamentações**
- **GDPR**: Proteção de dados pessoais
- **LGPD**: Conformidade com lei brasileira
- **CAN-SPAM**: Conformidade com leis anti-spam
- **HIPAA**: Proteção de dados de saúde (se aplicável)
- **SOX**: Controles financeiros para emails corporativos

## 📈 **Performance e Escalabilidade**

### 🚀 **Otimizações de Performance**

#### **1. Database Optimization**
- **Citus Data Sharding**: Distribuição horizontal por tenant_id
- **Connection Pooling**: PgBouncer para otimização de conexões
- **Read Replicas**: Réplicas de leitura para consultas
- **Indexing Strategy**: Índices otimizados para queries de email

#### **2. Caching Strategy**
- **Redis Caching**: Cache de metadados de email frequentemente acessados
- **CDN Integration**: Cache de anexos via Varnish + MinIO
- **Query Caching**: Cache de consultas de mailbox
- **Session Caching**: Cache de sessões de webmail

#### **3. Email Server Optimization**
- **Postfix Tuning**: Configuração otimizada para alto volume
- **Dovecot Optimization**: Índices de mailbox para acesso rápido
- **Rspamd Performance**: Configuração para processamento paralelo
- **ClamAV Efficiency**: Scanning otimizado com cache de assinaturas

### 📊 **Métricas de Performance**
- **Email Delivery Rate**: Taxa de entrega de emails
- **Spam Detection Accuracy**: Precisão na detecção de spam
- **API Response Time**: Latência P95 < 200ms
- **Storage Efficiency**: Otimização de uso de armazenamento
- **Throughput**: Emails processados por segundo

## 🔗 **Conectividade entre Microserviços**

### 🌐 **Service Mesh Integration (Istio/Linkerd)**
```yaml
# k8s/istio/virtual-service.yaml
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: email-service
spec:
  hosts:
  - email-service
  http:
  - match:
    - uri:
        prefix: /api/v1/email
    route:
    - destination:
        host: email-service
        port:
          number: 8015
    fault:
      delay:
        percentage:
          value: 0.1
        fixedDelay: 5s
    retries:
      attempts: 3
      perTryTimeout: 2s
```

### 📡 **Inter-Service Communication**
```python
class ServiceConnector:
    """Connector for inter-service communication."""

    def __init__(self):
        self.user_service = HTTPXClient(base_url="http://user-service:8002")
        self.tenant_service = HTTPXClient(base_url="http://tenant-service:8003")
        self.domain_service = HTTPXClient(base_url="http://domain-service:8012")
        self.notification_service = HTTPXClient(base_url="http://notification-service:8007")

    async def validate_user_tenant_association(self, user_id: UUID, tenant_id: UUID):
        """Validate user-tenant association via User Service."""
        response = await self.user_service.get(
            f"/api/v1/users/{user_id}/tenants/{tenant_id}/association"
        )
        return response.status_code == 200

    async def verify_domain_ownership(self, tenant_id: UUID, domain_name: str):
        """Verify domain ownership via Domain Service."""
        response = await self.domain_service.post(
            f"/api/v1/domains/verify",
            json={"tenant_id": str(tenant_id), "domain": domain_name}
        )
        return response.json()

    async def send_notification(self, user_id: UUID, notification_type: str, data: dict):
        """Send notification via Notification Service."""
        await self.notification_service.post(
            "/api/v1/notifications/send",
            json={
                "user_id": str(user_id),
                "type": notification_type,
                "data": data
            }
        )
```

## 🧪 **Testing Strategy Enterprise**

### 📋 **Comprehensive Test Coverage**

#### **1. Unit Tests**
```python
# tests/unit/test_domain_service.py
import pytest
from app.services.domain_service import DomainService
from app.models.email_domain import EmailDomain

@pytest.mark.asyncio
async def test_create_email_domain():
    """Test email domain creation."""
    domain_service = DomainService()
    domain_data = {
        "tenant_id": "123e4567-e89b-12d3-a456-************",
        "domain_name": "example.com"
    }

    domain = await domain_service.create_domain(domain_data)
    assert domain.domain_name == "example.com"
    assert domain.status == "pending"
    assert domain.dkim_enabled is True

@pytest.mark.asyncio
async def test_generate_dkim_keys():
    """Test DKIM key generation."""
    domain_service = DomainService()
    public_key, private_key = await domain_service.generate_dkim_keys()

    assert public_key.startswith("-----BEGIN PUBLIC KEY-----")
    assert private_key.startswith("-----BEGIN PRIVATE KEY-----")
```

#### **2. Integration Tests**
```python
# tests/integration/test_email_api.py
import pytest
from httpx import AsyncClient
from app.main import app

@pytest.mark.asyncio
async def test_create_email_domain_api():
    """Test email domain creation via API."""
    async with AsyncClient(app=app, base_url="http://test") as client:
        response = await client.post(
            "/api/v1/email/domains",
            json={
                "domain_name": "test.com",
                "tenant_id": "123e4567-e89b-12d3-a456-************"
            },
            headers={"Authorization": "Bearer test-token"}
        )

    assert response.status_code == 201
    data = response.json()
    assert data["domain_name"] == "test.com"
    assert data["status"] == "pending"

@pytest.mark.asyncio
async def test_webmail_send_email():
    """Test sending email via webmail API."""
    async with AsyncClient(app=app, base_url="http://test") as client:
        response = await client.post(
            "/api/v1/email/webmail/emails",
            json={
                "to": ["<EMAIL>"],
                "subject": "Test Email",
                "body": "This is a test email",
                "from": "<EMAIL>"
            },
            headers={"Authorization": "Bearer test-token"}
        )

    assert response.status_code == 202
    data = response.json()
    assert "message_id" in data
```

#### **3. Load Testing (K6)**
```javascript
// tests/load/email_load_test.js
import http from 'k6/http';
import { check, sleep } from 'k6';

export let options = {
  stages: [
    { duration: '2m', target: 100 }, // Ramp up
    { duration: '5m', target: 100 }, // Stay at 100 users
    { duration: '2m', target: 200 }, // Ramp up to 200 users
    { duration: '5m', target: 200 }, // Stay at 200 users
    { duration: '2m', target: 0 },   // Ramp down
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95% of requests under 500ms
    http_req_failed: ['rate<0.1'],    // Error rate under 10%
  },
};

export default function () {
  // Test email domain listing
  let response = http.get('http://email-service:8015/api/v1/email/domains', {
    headers: { 'Authorization': 'Bearer test-token' },
  });

  check(response, {
    'status is 200': (r) => r.status === 200,
    'response time < 500ms': (r) => r.timings.duration < 500,
  });

  sleep(1);

  // Test webmail email listing
  response = http.get('http://email-service:8015/api/v1/email/webmail/emails', {
    headers: { 'Authorization': 'Bearer test-token' },
  });

  check(response, {
    'webmail status is 200': (r) => r.status === 200,
    'webmail response time < 1000ms': (r) => r.timings.duration < 1000,
  });

  sleep(1);
}
```

#### **4. Security Testing**
```python
# tests/security/test_email_security.py
import pytest
from app.core.security.encryption import encrypt_dkim_key, decrypt_dkim_key

def test_dkim_key_encryption():
    """Test DKIM key encryption/decryption."""
    original_key = "-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC..."

    encrypted_key = encrypt_dkim_key(original_key)
    decrypted_key = decrypt_dkim_key(encrypted_key)

    assert decrypted_key == original_key
    assert encrypted_key != original_key

@pytest.mark.asyncio
async def test_unauthorized_access():
    """Test unauthorized access to email APIs."""
    async with AsyncClient(app=app, base_url="http://test") as client:
        response = await client.get("/api/v1/email/domains")

    assert response.status_code == 401
    assert "Unauthorized" in response.json()["detail"]
```

## 🚀 **Roadmap Enterprise (v2.0)**

### 🎯 **Fase 1: Infrastructure Foundation (Sprint 1-2)**
1. **🔄 Citus Data Setup**: Configurar sharding PostgreSQL com tenant_id
2. **🔄 Vault Integration**: Migrar secrets para HashiCorp Vault
3. **🔄 Istio Service Mesh**: Implementar mTLS automático
4. **🔄 Kubernetes Manifests**: Helm charts para deployment
5. **🔄 Prometheus Metrics**: Instrumentação básica

### 🎯 **Fase 2: Event-Driven Core (Sprint 3-4)**
1. **🔄 Kafka Integration**: Event sourcing e messaging
2. **🔄 RabbitMQ Setup**: Fast notifications
3. **🔄 Redis Streams**: Real-time updates
4. **🔄 Event Schemas**: Padronização de eventos
5. **🔄 CQRS Implementation**: Separação read/write

### 🎯 **Fase 3: Email Server Integration (Sprint 5-6)**
1. **🔄 Postfix Configuration**: SMTP server setup
2. **🔄 Dovecot Integration**: IMAP/POP3 server
3. **🔄 Rspamd Setup**: Spam filtering
4. **🔄 ClamAV Integration**: Antivirus scanning
5. **🔄 Email Routing**: Dynamic routing configuration

### 🎯 **Fase 4: Security & Compliance (Sprint 7-8)**
1. **🔄 OPA Policies**: Autorização centralizada
2. **🔄 DKIM/SPF/DMARC**: Email authentication
3. **🔄 TLS/SSL**: Secure communication
4. **🔄 Audit Logging**: Compliance e auditoria
5. **🔄 Data Encryption**: Criptografia em repouso

### 🎯 **Fase 5: Observability & Performance (Sprint 9-10)**
1. **🔄 Jaeger Tracing**: Distributed tracing completo
2. **🔄 ELK Stack**: Centralized logging
3. **🔄 Grafana Dashboards**: Visualização de métricas
4. **🔄 Performance Testing**: Load testing com K6
5. **🔄 Auto-scaling**: HPA baseado em métricas customizadas

### 🎯 **Fase 6: Advanced Features (Sprint 11-12)**
1. **🔄 Webmail Frontend**: Interface web para email
2. **🔄 Email Templates**: Templates para emails transacionais
3. **🔄 Bulk Operations**: Operações em lote para enterprise
4. **🔄 Email Analytics**: Tracking de opens, clicks, etc.
5. **🔄 Advanced Filtering**: Filtros avançados de email

### 🎯 **Fase 7: Global Scale (Sprint 13-14)**
1. **🔄 Multi-Region**: Deployment geo-distribuído
2. **🔄 CDN Integration**: Varnish + MinIO + PowerDNS
3. **🔄 Read Replicas**: Otimização de consultas
4. **🔄 Connection Pooling**: PgBouncer optimization
5. **🔄 Chaos Engineering**: Resilience testing

## 📊 **Métricas e KPIs**

### 📈 **Business Metrics**
- **Email Delivery Rate**: > 99.5% de taxa de entrega
- **Spam Detection Accuracy**: > 99% de precisão
- **User Satisfaction**: NPS > 8.0 para webmail
- **Storage Efficiency**: < 80% de uso médio
- **Revenue per Domain**: Receita por domínio de email

### 🔧 **Technical Metrics**
- **API Response Time**: P95 < 200ms
- **Email Processing Time**: < 5 segundos por email
- **Database Query Performance**: P95 < 100ms
- **Error Rate**: < 0.1% de erros
- **Uptime**: > 99.9% de disponibilidade

### 🛡️ **Security Metrics**
- **Failed Login Attempts**: Monitoramento de tentativas
- **Spam Block Rate**: Taxa de bloqueio de spam
- **Virus Detection Rate**: Taxa de detecção de vírus
- **Security Incidents**: Zero incidentes críticos
- **Compliance Score**: 100% de conformidade

## 🔧 **Environment Variables**

### 📋 **Configuração de Ambiente**
```yaml
# Email Service Configuration
EMAIL_SERVICE_PORT: 8015
EMAIL_SERVICE_HOST: 0.0.0.0

# Database Configuration
EMAIL_DATABASE_URL: postgresql+asyncpg://${EMAIL_DB_USER}:${EMAIL_DB_PASSWORD}@trix-db-email:5432/${EMAIL_DB_NAME}
EMAIL_DB_POOL_SIZE: 20
EMAIL_DB_MAX_OVERFLOW: 30

# Redis Configuration
REDIS_URL: redis://:${REDIS_PASSWORD}@trix-redis:6379/5
REDIS_POOL_SIZE: 10

# Kafka Configuration
KAFKA_BOOTSTRAP_SERVERS: trix-kafka:9092
KAFKA_EMAIL_TOPIC: email-events
KAFKA_CONSUMER_GROUP: email-service

# RabbitMQ Configuration
RABBITMQ_URL: amqp://${RABBITMQ_USER}:${RABBITMQ_PASSWORD}@trix-rabbitmq:5672/
RABBITMQ_EMAIL_EXCHANGE: email-notifications

# Vault Configuration
VAULT_URL: http://trix-vault:8200
VAULT_TOKEN: ${VAULT_TOKEN}
VAULT_EMAIL_PATH: email-service

# Email Server Configuration
POSTFIX_HOST: trix-email-postfix
POSTFIX_PORT: 25
DOVECOT_HOST: trix-email-dovecot
DOVECOT_PORT: 143
RSPAMD_HOST: trix-email-rspamd
RSPAMD_PORT: 11334
CLAMAV_HOST: trix-email-clamav
CLAMAV_PORT: 3310

# Service Discovery
USER_SERVICE_URL: http://user-service:8002
TENANT_SERVICE_URL: http://tenant-service:8003
DOMAIN_SERVICE_URL: http://domain-service:8012
NOTIFICATION_SERVICE_URL: http://notification-service:8007

# Monitoring
PROMETHEUS_METRICS_PORT: 9090
JAEGER_AGENT_HOST: trix-jaeger-agent
JAEGER_AGENT_PORT: 6831

# Security
JWT_SECRET_KEY: ${JWT_SECRET_KEY}
ENCRYPTION_KEY: ${ENCRYPTION_KEY}
DKIM_KEY_SIZE: 2048
```

---

**Última Atualização:** 2025-07-14
**Versão:** 2.0.0 (Enterprise-Grade)
**Status:** 🔄 **REESTRUTURAÇÃO PARA ESCALA MASSIVA**
**Target Scale:** Bilhões de usuários simultâneos
**Responsável:** Trix Development Team

### 📝 **Log de Mudanças Majores (v2.0 - 2025-07-14)**

#### **🏗️ Reestruturação Arquitetural Completa**
- 🔄 **Database Sharding**: Migração para Citus Data com sharding por tenant_id
- 🔄 **Service Mesh**: Integração Istio/Linkerd com mTLS automático
- 🔄 **Event-Driven**: Arquitetura completa Kafka + RabbitMQ + Redis Streams
- 🔄 **Security-First**: HashiCorp Vault + OPA Gatekeeper + Falco
- 🔄 **Observability**: Prometheus + Grafana + Jaeger + ELK stack
- ✅ **Email Server Integration**: Postfix + Dovecot + Rspamd + ClamAV

#### **📊 Modelos de Dados Otimizados**
- ✅ **Sharded Tables**: Email domains, accounts, aliases distribuídas por tenant_id
- ✅ **Co-location**: Otimização de joins via co-location strategy
- ✅ **Indexes**: Índices otimizados para queries de email
- ✅ **Soft References**: Zero FK constraints entre microserviços

#### **🚀 Event-Driven Architecture**
- ✅ **Event Sourcing**: Histórico imutável de todas as mudanças
- ✅ **CQRS**: Separação total de comandos e consultas
- ✅ **Multi-Layer Messaging**: Kafka (durability) + RabbitMQ (speed) + Redis (real-time)
- ✅ **Email Events**: Eventos específicos para operações de email

#### **🔐 Security Enterprise**
- ✅ **Vault Integration**: Secrets management para chaves DKIM
- ✅ **OPA Policies**: Autorização baseada em políticas
- ✅ **Email Authentication**: DKIM, SPF, DMARC implementation
- ✅ **Spam/Virus Protection**: Rspamd + ClamAV integration

#### **📈 Observability Completa**
- ✅ **Email Metrics**: Business e infrastructure metrics específicas
- ✅ **Distributed Tracing**: Jaeger para requests de email
- ✅ **Centralized Logging**: ELK stack para logs de email
- ✅ **Custom Dashboards**: Grafana para visualização de email

#### **☸️ Kubernetes Native**
- ✅ **Helm Charts**: Deployment automatizado para email service
- ✅ **Auto-scaling**: HPA baseado em métricas de email
- ✅ **Multi-environment**: Dev, staging, production overlays
- ✅ **Email Server Pods**: Containerização completa dos servidores

#### **🌍 Global Scale Preparation**
- ✅ **Multi-region**: Estratégia de deployment geo-distribuído
- ✅ **CDN Integration**: Varnish + MinIO para anexos
- ✅ **Connection Pooling**: PgBouncer para otimização
- ✅ **Performance Testing**: Load testing específico para email

#### **🔗 Conectividade entre Microserviços**
- ✅ **User Service**: Integração para autenticação e associações
- ✅ **Tenant Service**: Validação de tenants e billing
- ✅ **Domain Service**: Verificação de propriedade de domínios
- ✅ **Notification Service**: Envio de emails transacionais
- ✅ **CDN Service**: Armazenamento de anexos via MinIO
- ✅ **Auth Service**: Autenticação JWT e autorização
- ✅ **Core Service**: Configurações centralizadas
- ✅ **I18n Service**: Internacionalização de emails
- ✅ **Commerce Service**: Integração para emails de e-commerce
- ✅ **Media Service**: Processamento de anexos de mídia
- ✅ **Payment Service**: Emails de confirmação de pagamento
- ✅ **Other Service**: Integrações diversas
- ✅ **HR Module**: Emails de RH e comunicação interna
- ✅ **CRM Module**: Emails de relacionamento com clientes
- ✅ **Financial Module**: Relatórios financeiros por email

### 📋 **Roadmap de Implementação**
- **Fase 1-2**: Infrastructure Foundation (Citus, Vault, Istio, K8s)
- **Fase 3-4**: Event-Driven Core (Kafka, RabbitMQ, CQRS)
- **Fase 5-6**: Email Server Integration (Postfix, Dovecot, Rspamd, ClamAV)
- **Fase 7-8**: Security & Compliance (OPA, DKIM/SPF/DMARC, Audit)
- **Fase 9-10**: Observability & Performance (Jaeger, ELK, Auto-scaling)
- **Fase 11-12**: Advanced Features (Webmail, Templates, Analytics)
- **Fase 13-14**: Global Scale (Multi-region, CDN, Chaos Engineering)

### 🎯 **Próximos Passos Imediatos**
1. **Implementar Citus Data sharding** para distribuição horizontal
2. **Configurar HashiCorp Vault** para gerenciamento de secrets
3. **Implementar Event-Driven Architecture** com Kafka/RabbitMQ
4. **Integrar servidores de email** (Postfix, Dovecot, Rspamd, ClamAV)
5. **Configurar observabilidade completa** com Prometheus/Jaeger/ELK
6. **Implementar testes de carga** para validar escalabilidade
7. **Configurar deployment Kubernetes** com Helm charts
8. **Implementar conectividade** com todos os 16 microserviços
