"""
Consultancy Service Specific Metrics v2.0
==========================================
Consultancy-specific metrics that extend shared_lib observability.
Follows the pattern established by user_service for consistency.
"""

from prometheus_client import Counter, Histogram, Gauge, generate_latest
from typing import Dict, Any, Optional

# Import shared observability utilities
try:
    from microservices.core.shared_lib.config.infrastructure.observability.metrics import (
        MetricsCollector,
        get_metrics_collector
    )
    SHARED_LIB_AVAILABLE = True
except ImportError:
    SHARED_LIB_AVAILABLE = False


class ConsultancyMetricsManager:
    """
    Consultancy Service specific metrics manager.
    Integrates with shared_lib MetricsCollector.
    """
    
    def __init__(self):
        # ✅ Usando MetricsCollector da shared_lib (se disponível)
        if SHARED_LIB_AVAILABLE:
            self.shared_metrics = get_metrics_collector(
                service_name="consultancy-service",
                service_version="2.0.0",
                environment="production"
            )
        else:
            self.shared_metrics = None

        # ✅ Consultancy-specific metrics
        self._init_consultancy_metrics()
    
    def _init_consultancy_metrics(self):
        """Initialize consultancy-specific metrics."""
        
        # Project metrics
        self.project_duration_histogram = Histogram(
            'consultancy_project_duration_days',
            'Duration of consultancy projects in days',
            ['project_type', 'client_type', 'consultant_id'],
            buckets=[7, 14, 30, 60, 90, 180, 365]
        )
        
        self.project_budget_histogram = Histogram(
            'consultancy_project_budget_amount',
            'Budget amount for consultancy projects',
            ['project_type', 'currency'],
            buckets=[1000, 5000, 10000, 25000, 50000, 100000, 250000, 500000]
        )
        
        # Billing metrics
        self.billing_amount_histogram = Histogram(
            'consultancy_billing_amount',
            'Billing amounts for consultancy services',
            ['billing_type', 'currency', 'client_type'],
            buckets=[100, 500, 1000, 2500, 5000, 10000, 25000, 50000]
        )
        
        self.invoice_payment_time_histogram = Histogram(
            'consultancy_invoice_payment_time_days',
            'Time taken for invoice payment in days',
            ['client_type', 'payment_method'],
            buckets=[1, 7, 15, 30, 45, 60, 90]
        )
        
        # Timesheet metrics
        self.timesheet_hours_histogram = Histogram(
            'consultancy_timesheet_hours',
            'Hours logged in timesheets',
            ['consultant_id', 'project_type', 'is_billable'],
            buckets=[1, 4, 8, 16, 24, 40, 60, 80]
        )
        
        self.consultant_utilization_gauge = Gauge(
            'consultancy_consultant_utilization_percentage',
            'Consultant utilization percentage',
            ['consultant_id', 'period']
        )
        
        # Client metrics
        self.client_satisfaction_gauge = Gauge(
            'consultancy_client_satisfaction_score',
            'Client satisfaction score (1-10)',
            ['client_id', 'project_type']
        )
        
        self.client_retention_gauge = Gauge(
            'consultancy_client_retention_rate',
            'Client retention rate percentage',
            ['client_type', 'period']
        )
        
        # Compliance metrics
        self.compliance_score_gauge = Gauge(
            'consultancy_compliance_score',
            'Compliance score percentage',
            ['compliance_type', 'client_id']
        )
        
        self.compliance_violations_counter = Counter(
            'consultancy_compliance_violations_total',
            'Total compliance violations',
            ['violation_type', 'severity', 'client_id']
        )
        
        # Request metrics (consultancy-specific)
        self.consultancy_requests_counter = Counter(
            'consultancy_requests_total',
            'Total consultancy service requests',
            ['method', 'endpoint', 'status', 'client_type']
        )
    
    def record_project_completion(self, project_type: str, duration_days: float, 
                                budget_amount: float, currency: str, client_type: str,
                                consultant_id: str):
        """Record project completion metrics."""
        self.project_duration_histogram.labels(
            project_type=project_type,
            client_type=client_type,
            consultant_id=consultant_id
        ).observe(duration_days)
        
        self.project_budget_histogram.labels(
            project_type=project_type,
            currency=currency
        ).observe(budget_amount)
    
    def record_billing_event(self, billing_type: str, amount: float, currency: str,
                           client_type: str):
        """Record billing event metrics."""
        self.billing_amount_histogram.labels(
            billing_type=billing_type,
            currency=currency,
            client_type=client_type
        ).observe(amount)
    
    def record_timesheet_entry(self, consultant_id: str, project_type: str,
                             hours: float, is_billable: bool):
        """Record timesheet entry metrics."""
        self.timesheet_hours_histogram.labels(
            consultant_id=consultant_id,
            project_type=project_type,
            is_billable=str(is_billable).lower()
        ).observe(hours)
    
    def update_consultant_utilization(self, consultant_id: str, utilization: float,
                                    period: str):
        """Update consultant utilization metrics."""
        self.consultant_utilization_gauge.labels(
            consultant_id=consultant_id,
            period=period
        ).set(utilization)
    
    def update_client_satisfaction(self, client_id: str, project_type: str,
                                 satisfaction_score: float):
        """Update client satisfaction metrics."""
        self.client_satisfaction_gauge.labels(
            client_id=client_id,
            project_type=project_type
        ).set(satisfaction_score)
    
    def update_compliance_score(self, compliance_type: str, client_id: str,
                              score: float):
        """Update compliance score metrics."""
        self.compliance_score_gauge.labels(
            compliance_type=compliance_type,
            client_id=client_id
        ).set(score)
    
    def record_compliance_violation(self, violation_type: str, severity: str,
                                  client_id: str):
        """Record compliance violation."""
        self.compliance_violations_counter.labels(
            violation_type=violation_type,
            severity=severity,
            client_id=client_id
        ).inc()

    def record_consultancy_request(self, method: str, endpoint: str, status: str,
                                 client_type: str):
        """Record consultancy service request."""
        self.consultancy_requests_counter.labels(
            method=method,
            endpoint=endpoint,
            status=status,
            client_type=client_type
        ).inc()

    def get_metrics(self) -> str:
        """Get all metrics in Prometheus format."""
        return generate_latest()


# ✅ NOVO: Global metrics manager instance
metrics_manager = ConsultancyMetricsManager()

# ✅ NOVO: Export individual metrics for convenience
consultancy_requests_counter = metrics_manager.consultancy_requests_counter
project_duration_histogram = metrics_manager.project_duration_histogram
billing_amount_histogram = metrics_manager.billing_amount_histogram
timesheet_hours_histogram = metrics_manager.timesheet_hours_histogram
client_satisfaction_gauge = metrics_manager.client_satisfaction_gauge
consultant_utilization_gauge = metrics_manager.consultant_utilization_gauge
compliance_score_gauge = metrics_manager.compliance_score_gauge

# ✅ NOVO: Alias for compatibility with main.py
consultancy_metrics = metrics_manager
