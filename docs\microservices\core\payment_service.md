# 💳 Payment Service - Documentação Técnica Completa

> **🔗 Integração com Shared Lib**: Este serviço utiliza configurações e componentes centralizados da `microservices/core/shared_lib` para garantir consistência e reduzir duplicação entre microserviços. **MIGRAÇÃO EM ANDAMENTO** - Configurações comuns estão sendo movidas para a shared_lib. Veja a [seção de integração](#-integração-com-shared-lib) para detalhes.

## 📋 Visão Geral

O **Payment Service** é o microserviço central responsável por todo o ecossistema de pagamentos na plataforma Trix. Implementa processamento de pagamentos multi-gateway, gestão de assinaturas, transações seguras, compliance PCI-DSS, detecção de fraudes e suporte completo a métodos de pagamento brasileiros e internacionais. Projetado para escala massiva com arquitetura orientada a eventos e tecnologias 100% open source.

### 🎯 **Informações Básicas**
- **Porta:** 8020
- **Database:** PostgreSQL + Citus Data (Sharded)
- **Messaging:** Apache Kafka + RabbitMQ + Redis Streams
- **Service Mesh:** Istio/Linkerd com mTLS
- **Secrets:** HashiCorp Vault
- **Monitoring:** Prometheus + Grafana + Jaeger + ELK
- **Orchestration:** Kubernetes + Helm + ArgoCD
- **Status:** 🔄 **MIGRAÇÃO PARA SHARED LIB EM ANDAMENTO**
- **Versão:** 2.0.0 (Enterprise-Grade)
- **Target Scale:** Bilhões de transações simultâneas

### 📊 **Status de Implementação Enterprise**
- ✅ **Database Sharding**: Citus Data para distribuição horizontal
- ✅ **Service Mesh**: Istio/Linkerd com mTLS automático
- ✅ **Event-Driven**: Kafka/RabbitMQ para comunicação assíncrona
- ✅ **Security**: Vault para secrets, OPA para policies
- ✅ **Observability**: Prometheus/Jaeger/ELK stack completo
- ✅ **Kubernetes**: Helm charts e manifests para orquestração
- ✅ **APIs Versionadas**: v1 com cache, rate limiting e bulk operations
- ✅ **Performance**: Connection pooling, read replicas, caching
- ✅ **Geo-Distribution**: Multi-region deployment strategy
- ✅ **Auto-Scaling**: HPA baseado em CPU/Memory/Custom metrics

## 🌱 **Sistema de Seed Distribuído**

O Payment Service utiliza o **novo sistema de seed distribuído** para criação e gerenciamento de gateways de pagamento e configurações padrão do sistema.

### 📦 **Configuração do Microserviço**
```python
# Configuração no sistema distribuído
'payments': {
    'module': 'core_services.payments',
    'db_url': 'postgresql+asyncpg://payments_user:PaymentsSecure2024!#$@trix-postgres-primary:5432/payments_db',
    'priority': 5,  # Executado após auth, users, tenants
    'depends_on': ['auth', 'users', 'tenants'],  # Depende de outros serviços
    'health_check_timeout': 45,
    'retry_attempts': 3,
    'description': 'Gestão de pagamentos e transações'
}
```

### 🚀 **Execução de Seeds**

#### Via Orquestrador Central
```bash
# Executar payments service (inclui dependências automaticamente)
cd microservices/core/shared_lib/migration/seed/
python distributed_main.py --microservices payments

# Executar auth + users + tenants + payments em sequência
python distributed_main.py --microservices auth users tenants payments

# Com logs detalhados
python distributed_main.py --microservices payments --verbose
```

#### Via Módulo Individual
```bash
# Executar seed específico do payments
cd microservices/core/shared_lib/migration/seed/
python -c "from core_services.payments import seed; import asyncio; asyncio.run(seed())"
```

### 💳 **Gateways Padrão Criados**

O seed do Payment Service cria **gateways essenciais**:

```python
# Gateways criados automaticamente
GATEWAYS_PADRAO = [
    {
        'name': 'Stripe',
        'provider': 'stripe',
        'supported_methods': ['credit_card', 'debit_card'],
        'supported_currencies': ['USD', 'EUR', 'BRL'],
        'regions': ['us-east-1', 'eu-west-1', 'sa-east-1'],
        'is_active': True,
        'description': 'Gateway Stripe para cartões internacionais'
    },
    {
        'name': 'PIX',
        'provider': 'pix',
        'supported_methods': ['pix'],
        'supported_currencies': ['BRL'],
        'regions': ['sa-east-1'],
        'is_active': True,
        'description': 'Gateway PIX para pagamentos instantâneos Brasil'
    },
    {
        'name': 'Boleto',
        'provider': 'boleto',
        'supported_methods': ['boleto'],
        'supported_currencies': ['BRL'],
        'regions': ['sa-east-1'],
        'is_active': True,
        'description': 'Gateway Boleto Bancário Brasil'
    },
    {
        'name': 'PayPal',
        'provider': 'paypal',
        'supported_methods': ['paypal', 'credit_card'],
        'supported_currencies': ['USD', 'EUR', 'BRL'],
        'regions': ['us-east-1', 'eu-west-1', 'sa-east-1'],
        'is_active': True,
        'description': 'Gateway PayPal internacional'
    }
]
```

### 🔐 **Sistema de Segurança PCI-DSS**

O seed configura o **sistema de segurança PCI-DSS Level 1**:
- **Tokenização**: Configuração automática de tokens seguros
- **Criptografia**: Chaves de criptografia via HashiCorp Vault
- **Compliance**: Configurações de auditoria e logs
- **Fraud Detection**: Modelos básicos de detecção de fraude

### 🔍 **Health Checks**

Verificações automáticas:
- ✅ **Conectividade**: Conexão com `payments_db`
- ✅ **Dependências**: Auth, User e Tenant Services operacionais
- ✅ **Gateways**: Conectividade com gateways de pagamento
- ✅ **Vault**: Acesso a secrets de pagamento
- ✅ **Compliance**: Verificação de configurações PCI-DSS

### 📊 **Monitoramento de Seeds**

```bash
# Métricas detalhadas
python distributed_main.py --microservices payments --verbose

# Saída esperada:
# ✅ Created gateway: Stripe
# ✅ Created gateway: PIX
# ✅ Created gateway: Boleto
# ✅ Created gateway: PayPal
# ✅ Configured PCI-DSS compliance
# ✅ Payments seed completed successfully! Created 4 gateways.
```

### 🔗 **Integração com Outros Microserviços**

Os gateways criados são utilizados por:
- **Commerce Service**: Para processamento de pedidos
- **Subscription Service**: Para assinaturas recorrentes
- **Billing Service**: Para faturamento
- **Todos os módulos**: Como gateways base do sistema

### 🎯 **Dados Exportados**

O seed exporta IDs dos gateways criados para uso por outros microserviços:
```python
# Estrutura exportada
created_gateways = {
    'stripe': {'id': 'uuid-stripe', 'provider': 'stripe'},
    'pix': {'id': 'uuid-pix', 'provider': 'pix'},
    'boleto': {'id': 'uuid-boleto', 'provider': 'boleto'},
    'paypal': {'id': 'uuid-paypal', 'provider': 'paypal'}
}
```

## 🏗️ **Arquitetura Enterprise (v2.0)**

### ✅ **Technology Stack (100% Open Source)**
- **Orchestration:** Kubernetes + Helm + ArgoCD
- **Service Mesh:** Istio/Linkerd com mTLS automático
- **Databases:** PostgreSQL + Citus Data + PgBouncer
- **Messaging:** Apache Kafka + RabbitMQ + Redis Streams
- **CDN:** Varnish + MinIO + PowerDNS
- **Monitoring:** Prometheus + Grafana + Jaeger + ELK
- **Security:** HashiCorp Vault + OPA Gatekeeper + Falco

### 🔄 **Event-Driven Architecture**
- **Apache Kafka**: Eventos críticos de pagamento e auditoria
- **RabbitMQ**: Mensagens rápidas e notificações para velocidade e escalabilidade
- **Padrão CQRS**: Separação de comandos e consultas
- **Event Sourcing**: Histórico completo de transações
- **Saga Pattern**: Transações distribuídas entre microserviços
- **Soft References**: Relacionamentos entre microserviços via eventos, não FK constraints
- **PCI Compliance**: Eventos de auditoria para compliance

### 📁 **Estrutura de Diretórios**

> **📚 Shared Components**: Este serviço utiliza componentes compartilhados da `microservices/core/shared_lib/` para configurações, utilitários e integrações comuns. **MIGRAÇÃO EM ANDAMENTO** - Componentes comuns estão sendo movidos para a shared_lib.

```
microservices/core/payment_service/
├── app/
│   ├── api/
│   │   └── v1/                       # ✅ Versioned APIs
│   │       ├── payments.py           # Payment management endpoints
│   │       ├── gateways.py           # Gateway management endpoints
│   │       ├── transactions.py       # Transaction endpoints
│   │       ├── subscriptions.py      # Subscription endpoints
│   │       ├── webhooks.py           # Webhook endpoints
│   │       ├── health.py             # Health check endpoints
│   │       └── __init__.py           # API router exports
│   ├── core/
│   │   ├── config/                   # ✅ Payment-specific configs only
│   │   │   ├── settings.py           # Payment-specific settings (extends shared_lib)
│   │   │   ├── database.py           # Payment-specific database config
│   │   │   ├── gateways.py           # Gateway configurations
│   │   │   └── vault.py              # Payment-specific Vault paths
│   │   ├── security/                 # ✅ Payment-specific security
│   │   │   ├── pci_compliance.py     # PCI-DSS compliance
│   │   │   ├── fraud_detection.py    # ML-based fraud detection
│   │   │   ├── tokenization.py       # Card tokenization
│   │   │   └── validation.py         # Payment data validation
│   │   ├── database/                 # ✅ Payment-specific database layer
│   │   │   ├── sharding.py           # Payment-specific sharding logic
│   │   │   ├── connection.py         # Payment database connections
│   │   │   └── migrations.py         # Payment migration management
│   │   ├── observability/            # ✅ Payment-specific observability only
│   │   │   ├── logging.py            # Payment-specific structured logging
│   │   │   ├── tracing.py            # Payment-specific distributed tracing
│   │   │   └── __init__.py           # Re-exports metrics from shared_lib
│   │   ├── integrations/             # ✅ Service integrations
│   │   │   ├── service_clients.py    # External service clients
│   │   │   ├── auth_client.py        # Auth service integration
│   │   │   ├── user_client.py        # User service integration
│   │   │   ├── tenant_client.py      # Tenant service integration
│   │   │   └── commerce_client.py    # Commerce service integration
│   │   ├── auth.py                   # ✅ Auth middleware integration
│   │   └── db_dependencies.py        # ✅ Database dependencies
│   ├── models/
│   │   ├── payment.py                # ✅ Payment model (sharded by tenant_id)
│   │   ├── payment_transaction.py    # ✅ Transaction model
│   │   ├── payment_method.py         # ✅ Payment methods
│   │   ├── subscription.py           # ✅ Subscription model
│   │   ├── gateway.py                # ✅ Gateway configuration
│   │   └── __init__.py               # Model exports
│   ├── schemas/
│   │   ├── payment.py                # ✅ Payment schemas (requests/responses)
│   │   ├── transaction.py            # ✅ Transaction schemas
│   │   ├── subscription.py           # ✅ Subscription schemas
│   │   ├── events.py                 # ✅ Event schemas for messaging
│   │   └── __init__.py               # Schema exports
│   ├── services/
│   │   ├── payment_service.py        # ✅ Payment processing service
│   │   ├── gateway_service.py        # ✅ Gateway management service
│   │   ├── subscription_service.py   # ✅ Subscription service
│   │   ├── fraud_service.py          # ✅ Fraud detection service
│   │   ├── event_service.py          # ✅ Event publishing service
│   │   └── __init__.py               # Service exports
│   ├── processors/                   # ✅ Gateway integrations
│   │   ├── base.py                   # Base processor class
│   │   ├── stripe/                   # Stripe integration
│   │   ├── paypal/                   # PayPal integration
│   │   ├── pix/                      # PIX integration (Brazil)
│   │   ├── boleto/                   # Boleto integration (Brazil)
│   │   └── __init__.py               # Processor exports
│   ├── main.py                       # ✅ FastAPI application
│   └── dependencies.py               # ✅ Dependency injection
├── docker/
│   ├── Dockerfile                    # ✅ Container configuration
│   └── docker-compose.yml            # ✅ Service orchestration
├── k8s/                             # ✅ Kubernetes manifests
│   ├── deployment.yaml              # ✅ Kubernetes deployment
│   ├── service.yaml                 # ✅ Service definition
│   ├── configmap.yaml               # ✅ Configuration management
│   ├── secret.yaml                  # ✅ Secrets management
│   ├── hpa.yaml                     # ✅ Horizontal Pod Autoscaler
│   ├── pdb.yaml                     # ✅ Pod Disruption Budget
│   ├── networkpolicy.yaml           # ✅ Network policies
│   └── istio/                       # ✅ Service mesh configuration
│       ├── virtualservice.yaml      # ✅ Traffic routing
│       ├── destinationrule.yaml     # ✅ Load balancing
│       └── peerauthentication.yaml  # ✅ mTLS configuration
├── helm/                            # ✅ Helm chart
│   ├── Chart.yaml                   # ✅ Chart metadata
│   ├── values.yaml                  # ✅ Default values
│   ├── values-dev.yaml              # ✅ Development values
│   ├── values-staging.yaml          # ✅ Staging values
│   ├── values-prod.yaml             # ✅ Production values
│   └── templates/                   # ✅ Kubernetes templates
├── migrations/
│   ├── env.py                       # ✅ Alembic environment
│   ├── versions/                    # ✅ Migration files
│   └── seed/                        # ✅ Seed data
├── tests/                           # ✅ Test suites
│   ├── unit/                        # ✅ Unit tests
│   ├── integration/                 # ✅ Integration tests
│   ├── e2e/                         # ✅ End-to-end tests
│   └── performance/                 # ✅ Performance tests
├── monitoring/                      # ✅ Observability configuration
│   ├── prometheus/                  # ✅ Prometheus metrics
│   ├── grafana/                     # ✅ Grafana dashboards
│   └── jaeger/                      # ✅ Tracing configuration
├── requirements.txt                 # ✅ Payment-specific Python dependencies
├── requirements-dev.txt             # ✅ Development dependencies
├── Dockerfile.prod                  # ✅ Production container
├── .dockerignore                    # ✅ Docker ignore rules
└── alembic.ini                     # ✅ Migration configuration

# Shared Library Integration (MIGRAÇÃO EM ANDAMENTO - COMPONENTES SENDO MOVIDOS)
../shared_lib/config                      # 🔗 Configurações e utilitários compartilhados
├── financial_config.py           # 🔗 Configuração financeira compartilhada
├── vault_config.py               # 🔗 Configuração do HashiCorp Vault
├── kafka_config.py               # 🔗 Configuração do Apache Kafka
└── __init__.py                   # 🔗 Exportações das configurações
├── infrastructure/                 # 🔗 Componentes de infraestrutura
│   ├── financial/                 # 🔗 Componentes financeiros específicos
│   │   ├── messaging.py           # 🔗 MOVENDO: Clientes de messaging financeiros
│   │   ├── security.py            # 🔗 MOVENDO: Segurança financeira compartilhada
│   │   ├── observability.py       # 🔗 MOVENDO: Observabilidade financeira
│   │   └── __init__.py            # 🔗 Exportações financeiras
│   ├── messaging/                 # 🔗 Clientes de messaging compartilhados
│   │   ├── kafka_client.py        # 🔗 MOVENDO: Cliente Kafka compartilhado
│   │   ├── rabbitmq_client.py     # 🔗 MOVENDO: Cliente RabbitMQ compartilhado
│   │   ├── redis_client.py        # 🔗 MOVENDO: Cliente Redis compartilhado
│   │   └── __init__.py            # 🔗 Exportações dos clientes
│   ├── observability/             # 🔗 Utilitários de monitoramento
│   │   ├── metrics.py             # 🔗 MOVENDO: Métricas Prometheus compartilhadas
│   │   ├── tracing.py             # 🔗 Tracing distribuído compartilhado
│   │   ├── logging.py             # 🔗 Logging estruturado compartilhado
│   │   └── __init__.py            # 🔗 Exportações de observabilidade
│   ├── security/                  # 🔗 Utilitários de segurança
│   │   ├── encryption.py          # 🔗 Utilitários de criptografia compartilhados
│   │   ├── rate_limiter.py        # 🔗 Rate limiting compartilhado
│   │   ├── session_manager.py     # 🔗 Gerenciamento de sessões compartilhado
│   │   └── __init__.py            # 🔗 Exportações de segurança
│   ├── database/                  # 🔗 Utilitários de banco de dados
│   │   ├── connection.py          # 🔗 Conexões de banco compartilhadas
│   │   ├── sharding.py            # 🔗 Sharding compartilhado
│   │   └── __init__.py            # 🔗 Exportações de banco
│   └── __init__.py                # 🔗 Exportações da infraestrutura
└── utils/                         # 🔗 Utilitários comuns
    ├── event_sourcing.py          # 🔗 Event sourcing compartilhado
    ├── common.py                  # 🔗 Utilitários comuns
    └── __init__.py                # 🔗 Exportações dos utilitários

```

### 🔗 **Integração com Shared Lib (MIGRAÇÃO EM ANDAMENTO)**

O Payment Service está sendo migrado para utilizar completamente a `shared_lib` para:

> **🚧 MIGRAÇÃO EM PROGRESSO**: Configurações comuns estão sendo movidas para a shared_lib e o Payment Service está sendo limpo e otimizado. Funcionalidades específicas de pagamento serão mantidas e organizadas.

#### **📋 Configurações Compartilhadas (EM MIGRAÇÃO)**
- **Financial Configuration**: ✅ Configuração financeira centralizada na shared_lib
- **Vault Configuration**: 🔄 Configuração centralizada do HashiCorp Vault
- **Kafka Configuration**: 🔄 Configuração dos brokers e tópicos Kafka
- **Environment Settings**: 🔄 Configurações de ambiente padrão

#### **🛠️ Utilitários Compartilhados (EM MIGRAÇÃO)**
- **Financial Messaging**: 🔄 Clientes Kafka, RabbitMQ e Redis especializados para finanças
- **Financial Observability**: 🔄 Métricas Prometheus, logs e tracing financeiros
- **Financial Security**: 🔄 Funções de criptografia e PCI-DSS compliance
- **Database Utilities**: 🔄 Conexões e migrações padronizadas

#### **🧹 Limpeza Planejada (A FAZER)**
- **Messaging Infrastructure**: ❌ A ser removida (movida para shared_lib)
- **Metrics Duplicadas**: ❌ A serem removidas (centralizadas na shared_lib)
- **Imports Incorretos**: 🔄 A serem corrigidos e otimizados
- **Tracing Duplicado**: 🔄 A ser corrigido (inicialização única)
- **Error Handling**: 🔄 A ser melhorado para desenvolvimento

#### **📦 Como Usar (PÓS-MIGRAÇÃO)**
```python
# Importar configurações financeiras compartilhadas
from microservices.core.shared_lib.config import FinancialSettings, get_financial_config

# Importar clientes de messaging financeiros compartilhados
from microservices.core.shared_lib.config.infrastructure.financial.messaging import (
    FinancialKafkaClient,
    FinancialRabbitMQClient,
    FinancialRedisClient,
    FinancialEventManager
)

# Importar utilitários de observabilidade compartilhados
from microservices.core.shared_lib.config.infrastructure.observability import (
    MetricsCollector,
    get_metrics_collector
)

# Importar métricas específicas do payment_service (re-exportadas da shared_lib)
from app.core.observability import (
    payment_requests_counter,
    transaction_duration_histogram,
    fraud_detection_counter,
    metrics_manager
)

# Exemplo de uso (PÓS-MIGRAÇÃO)
financial_config = get_financial_config()
kafka_client = FinancialKafkaClient("payment-service")
rabbitmq_client = FinancialRabbitMQClient("payment-service")
redis_client = FinancialRedisClient("payment-service")
event_manager = FinancialEventManager("payment-service")
metrics = get_metrics_collector("payment-service", "2.0.0", "production")

# Usar métricas específicas do payment
payment_requests_counter.inc({"method": "process_payment", "status": "success"})
transaction_duration_histogram.observe(0.250)  # 250ms
fraud_detection_counter.inc({"result": "approved", "risk_level": "low"})
```

#### **🎯 Benefícios da Migração**

**✅ Consistência Entre Microserviços Financeiros**:
- Configurações financeiras padronizadas em todos os serviços
- Mesmos padrões de segurança PCI-DSS, observability e messaging
- Redução de duplicação de código financeiro

**✅ Manutenibilidade Financeira**:
- Atualizações centralizadas na shared_lib para componentes financeiros
- Versionamento controlado de componentes financeiros comuns
- Facilita upgrades e patches de segurança PCI-DSS

**✅ Reutilização Financeira**:
- Novos microserviços financeiros podem importar componentes prontos
- Padrões enterprise financeiros já implementados e testados
- Reduz tempo de desenvolvimento de novos serviços de pagamento

#### **🔗 Como Usar a Shared Library (PÓS-MIGRAÇÃO)**
```python
# Imports da shared library no payment_service
from microservices.core.shared_lib.config import FinancialSettings, get_financial_config
from microservices.core.shared_lib.config.infrastructure.financial import (
    messaging, security, observability
)
from microservices.core.shared_lib.config.infrastructure.messaging import (
    kafka_client, rabbitmq_client, redis_client
)
from microservices.core.shared_lib.config.infrastructure.observability import (
    metrics, tracing, logging
)
```

#### **✅ Submódulos Específicos Preservados**
- **`processors/`**: Integrações específicas com gateways de pagamento
- **`security/pci_compliance.py`**: Compliance PCI-DSS específico do Payment Service
- **`security/fraud_detection.py`**: Detecção de fraude específica do domínio
- **Vantagem**: Cada submódulo mantém sua estrutura própria (api/, models/, schemas/, services/)

### 🔧 **Modelos de Dados Enterprise (Citus Data Optimized)**

#### **1. Sharded Tables (Distributed by tenant_id)**
```python
class Payment(Base):
    """
    Payment model optimized for Citus Data sharding.
    Distributed by tenant_id for optimal query performance.
    """
    __tablename__ = 'payments'

    id: UUID = Field(primary_key=True, default_factory=uuid4)
    tenant_id: UUID = Field(index=True)  # 🔑 Sharding key for Citus Data
    order_id: UUID = Field(nullable=True)  # Soft reference to commerce service
    user_id: UUID = Field(index=True)  # Soft reference to user service
    gateway_id: UUID = Field(index=True)  # Reference to payment gateway

    # Payment Details
    amount: Decimal = Field(max_digits=12, decimal_places=2)
    currency: str = Field(default="BRL", max_length=3)
    status: PaymentStatus = Field(default=PaymentStatus.PENDING)
    payment_method: PaymentMethodType = Field()
    reference_number: str = Field(nullable=True, max_length=100)

    # Security & Compliance
    encrypted_data: str = Field(nullable=True)  # PCI-compliant encrypted data
    fraud_score: float = Field(default=0.0, ge=0.0, le=1.0)
    risk_level: RiskLevel = Field(default=RiskLevel.LOW)

    # Metadata
    gateway_response: dict = Field(default_factory=dict)
    metadata: dict = Field(default_factory=dict)
    region: str = Field(index=True)  # For geo-distribution

    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    completed_at: datetime = Field(nullable=True)

    # Citus Data optimization indexes
    __table_args__ = (
        Index('idx_tenant_status', 'tenant_id', 'status'),
        Index('idx_tenant_created', 'tenant_id', 'created_at'),
        Index('idx_user_tenant', 'user_id', 'tenant_id'),
        Index('idx_region_created', 'region', 'created_at'),
        # Distribute table by tenant_id
        {'citus_table_type': 'distributed'}
    )
```

```python
class PaymentTransaction(Base):
    """
    Transaction model co-located with payments for optimal joins.
    """
    __tablename__ = 'payment_transactions'

    id: UUID = Field(primary_key=True, default_factory=uuid4)
    payment_id: UUID = Field(index=True)  # Soft reference to payments
    tenant_id: UUID = Field(index=True)  # 🔑 Sharding key (same as payments)
    gateway_transaction_id: str = Field(nullable=True, max_length=255)

    # Transaction Details
    amount: Decimal = Field(max_digits=12, decimal_places=2)
    status: TransactionStatus = Field()
    gateway_response: dict = Field(default_factory=dict)
    error_code: str = Field(nullable=True, max_length=50)
    error_message: str = Field(nullable=True, max_length=500)

    # Processing Info
    retry_count: int = Field(default=0, ge=0)
    last_retry_at: datetime = Field(nullable=True)
    processing_time_ms: int = Field(nullable=True)

    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    processed_at: datetime = Field(nullable=True)

    # Co-locate with payments table by tenant_id
    __table_args__ = (
        Index('idx_tenant_payment', 'tenant_id', 'payment_id'),
        Index('idx_tenant_status', 'tenant_id', 'status'),
        Index('idx_gateway_transaction', 'gateway_transaction_id'),
        # Co-locate with payments table
        {'citus_table_type': 'distributed', 'colocate_with': 'payments'}
    )
```

#### **2. Reference Tables (Global/Replicated)**
```python
class PaymentGateway(Base):
    """
    Payment gateways - replicated across all Citus Data nodes.
    Reference table for consistent gateway definitions.
    """
    __tablename__ = 'payment_gateways'

    id: UUID = Field(primary_key=True, default_factory=uuid4)
    name: str = Field(unique=True, max_length=50)
    provider: str = Field(max_length=50)  # stripe, paypal, pix, boleto
    supported_methods: List[str] = Field(default_factory=list)
    supported_currencies: List[str] = Field(default_factory=list)
    regions: List[str] = Field(default_factory=list)
    is_active: bool = Field(default=True)
    configuration: dict = Field(default_factory=dict)

    # Reference table - replicated across all shards
    __table_args__ = {'citus_table_type': 'reference'}
```

**⚠️ IMPORTANTE - Isolamento de Microserviços:**
- `tenant_id`, `user_id`, `order_id` NÃO possuem Foreign Key constraints para manter isolamento entre microserviços
- Validação de integridade referencial deve ser feita via API calls entre serviços
- Cross-microservice relationships são tratadas como "soft references"
- **Event Sourcing**: Todos os eventos de pagamento são imutáveis e armazenados permanentemente
- **Eventual Consistency**: Consistência garantida via eventos assíncronos
- **Event-Driven Architecture**: Kafka e RabbitMQ para comunicação assíncrona e escalabilidade
- **Performance**: Eventos para sincronização de dados entre microserviços
- **Resiliência**: Message queues garantem entrega mesmo com serviços temporariamente indisponíveis

### 🚀 **Event-Driven Architecture Enterprise (Usando Shared Library)**

#### **Event Schemas & Publishing (v2.0 - Shared Library)**
```python
# ✅ NOVO: Usando FinancialEventBase da shared_lib
from microservices.core.shared_lib.config.infrastructure.financial.events import FinancialEventBase
from microservices.core.shared_lib.config.infrastructure.financial.messaging import (
    FinancialEventManager, FinancialKafkaClient
)

class PaymentEvent(FinancialEventBase):
    """Payment event schema específico, herdando da base financeira comum."""
    event_type: str  # payment.created, payment.completed, payment.failed, payment.refunded
    payment_id: UUID
    gateway_id: UUID
    amount: Decimal
    currency: str
    payment_method: str
    source_service: str = "payment-service"

class PaymentEventPublisher:
    """Payment-specific event publisher usando shared_lib (v2.0)."""

    def __init__(self):
        # ✅ NOVO: Usando FinancialEventManager da shared_lib
        self.event_manager = FinancialEventManager("payment-service")

        # ✅ NOVO: Usando PaymentServiceKafkaClient da shared_lib
        from microservices.core.payment_service.app.core.messaging.kafka import payment_kafka_client
        self.kafka_client = payment_kafka_client

    async def publish_payment_event(self, event: PaymentEvent):
        """Publish payment events usando shared_lib infrastructure."""

        # ✅ NOVO: Usando event manager compartilhado da shared_lib
        await self.event_manager.publish_event(
            event=event,
            channels=["kafka", "rabbitmq", "redis"]  # Multi-channel publishing
        )

    async def publish_transaction_event(self, transaction_data: dict):
        """Publish transaction events usando shared_lib."""

        # ✅ NOVO: Usando cliente Kafka financeiro compartilhado da shared_lib
        await self.kafka_client.publish_financial_event(
            event_type="transaction.processed",
            tenant_id=transaction_data["tenant_id"],
            user_id=transaction_data["user_id"],
            data=transaction_data,
            topic_type="transactions"
        )

# ✅ NOVO: Funções de conveniência para eventos específicos
async def publish_payment_completed_event(payment_id: UUID, tenant_id: UUID, payment_data: dict):
    """Convenience function using shared_lib infrastructure."""
    from microservices.core.payment_service.app.core.messaging.kafka import publish_payment_completed_event
    await publish_payment_completed_event(payment_id, tenant_id, payment_data)

async def publish_fraud_detected_event(payment_id: UUID, tenant_id: UUID, fraud_data: dict):
    """Convenience function using shared_lib infrastructure."""
    from microservices.core.payment_service.app.core.messaging.kafka import publish_fraud_detected_event
    await publish_fraud_detected_event(payment_id, tenant_id, fraud_data)

async def publish_subscription_renewed_event(subscription_id: UUID, tenant_id: UUID, renewal_data: dict):
    """Convenience function using shared_lib infrastructure."""
    from microservices.core.payment_service.app.core.messaging.kafka import publish_subscription_renewed_event
    await publish_subscription_renewed_event(subscription_id, tenant_id, renewal_data)
```

### 🔐 **Security-First Architecture (Usando Shared Library)**

#### **HashiCorp Vault Integration (v2.0 - Shared Library)**
```python
# ✅ NOVO: Usando VaultBaseSettings da shared_lib
from microservices.core.shared_lib.config import VaultBaseSettings, FinancialSettings
from microservices.core.shared_lib.config.infrastructure.security import encryption

class PaymentVaultManager(VaultBaseSettings):
    """Payment-specific Vault manager usando shared_lib."""

    def __init__(self):
        super().__init__()
        # ✅ NOVO: Usando configuração financeira da shared_lib
        self.financial_config = FinancialSettings()

    async def get_gateway_credentials(self, gateway: str, environment: str):
        """Get payment gateway credentials usando shared_lib."""
        # ✅ NOVO: Usando paths financeiros da shared_lib
        vault_paths = self.financial_config.get_financial_vault_paths()
        secret_path = f"{vault_paths['payment_gateways']}/{gateway}"

        return await self.get_secret(secret_path)

    async def get_encryption_key(self, environment: str):
        """Get encryption key for PCI compliance usando shared_lib."""
        # ✅ NOVO: Usando paths de criptografia da shared_lib
        vault_paths = self.financial_config.get_financial_vault_paths()
        secret_path = f"{vault_paths['encryption']}/payment-service"

        secret = await self.get_secret(secret_path)
        return secret.get('encryption_key')

    async def get_pci_compliance_config(self):
        """Get PCI compliance configuration usando shared_lib."""
        vault_paths = self.financial_config.get_financial_vault_paths()
        return await self.get_secret(f"{vault_paths['certificates']}/pci-dss")

class PaymentOPAPolicyEnforcer:
    """Payment-specific OPA integration usando shared_lib."""

    def __init__(self):
        # ✅ NOVO: Usando configuração OPA da shared_lib
        from microservices.core.shared_lib.config.infrastructure.opa import OPAClient
        self.opa_client = OPAClient("payment-service")

    async def check_payment_permission(self, user_id: UUID, tenant_id: UUID, amount: Decimal):
        """Check payment permission usando shared_lib OPA integration."""
        policy_input = {
            "user_id": str(user_id),
            "tenant_id": str(tenant_id),
            "amount": float(amount),
            "action": "payment_processing",
            "service": "payment-service"
        }

        # ✅ NOVO: Usando cliente OPA compartilhado
        return await self.opa_client.evaluate_policy(
            policy_path="financial/payment_permissions",
            input_data=policy_input
        )

    async def check_fraud_risk_policy(self, payment_data: dict):
        """Check fraud risk policy usando shared_lib."""
        return await self.opa_client.evaluate_policy(
            policy_path="financial/fraud_detection",
            input_data=payment_data
        )
```

#### **PCI-DSS Compliance Implementation (v2.0 - Shared Library)**
```python
# ✅ NOVO: Usando utilitários de criptografia da shared_lib
from microservices.core.shared_lib.config.infrastructure.security.encryption import (
    AdvancedEncryption, SecureTokenGenerator
)
from microservices.core.shared_lib.config.infrastructure.financial.security import (
    PCIComplianceManager, FinancialDataEncryption
)

class PaymentPCICompliantTokenizer:
    """Advanced tokenization for PCI-DSS Level 1 compliance usando shared_lib."""

    def __init__(self):
        # ✅ NOVO: Usando gerenciador PCI da shared_lib
        self.pci_manager = PCIComplianceManager("payment-service")
        self.encryption = FinancialDataEncryption()
        self.token_generator = SecureTokenGenerator()

        # ✅ NOVO: Usando vault manager da shared_lib
        self.vault_manager = PaymentVaultManager()

    async def tokenize_card_data(self, card_data: CardData) -> TokenizedCard:
        """Tokenize sensitive card data with PCI compliance usando shared_lib."""

        # ✅ NOVO: Validação PCI usando shared_lib
        await self.pci_manager.validate_card_data(card_data)

        sensitive_data = {
            "number": card_data.number,
            "cvv": card_data.cvv,
            "expiry": card_data.expiry
        }

        # ✅ NOVO: Criptografia financeira da shared_lib
        encrypted_data = await self.encryption.encrypt_financial_data(
            data=sensitive_data,
            data_type="payment_card"
        )

        # ✅ NOVO: Geração de token seguro da shared_lib
        token = await self.token_generator.generate_financial_token(
            data_type="payment_card",
            tenant_id=card_data.tenant_id
        )

        # ✅ NOVO: Armazenamento seguro no Vault usando shared_lib
        await self.vault_manager.store_encrypted_token(
            token=token,
            encrypted_data=encrypted_data,
            data_type="payment_card"
        )

        return TokenizedCard(
            token=token,
            last_four=card_data.number[-4:],
            brand=self.detect_card_brand(card_data.number),
            expiry_month=card_data.expiry.month,
            expiry_year=card_data.expiry.year,
            pci_compliant=True,
            encryption_standard="AES-256-GCM"
        )

    async def detokenize_card_data(self, token: str) -> Optional[CardData]:
        """Detokenize card data usando shared_lib."""

        # ✅ NOVO: Recuperação segura do Vault usando shared_lib
        encrypted_data = await self.vault_manager.get_encrypted_token(token)

        if encrypted_data:
            # ✅ NOVO: Descriptografia financeira da shared_lib
            card_data = await self.encryption.decrypt_financial_data(
                encrypted_data=encrypted_data,
                data_type="payment_card"
            )
            return CardData(**card_data)

        return None
```

### 🔧 **Enterprise Configuration Management (v2.0 - Shared Library)**

#### **Vault-Integrated Settings (Usando Shared Library)**
```python
# ✅ NOVO: Usando FinancialSettings da shared_lib
from microservices.core.shared_lib.config import FinancialSettings, VaultBaseSettings
from microservices.core.shared_lib.config.infrastructure.database import DatabaseConfig
from microservices.core.shared_lib.config.infrastructure.messaging import MessagingConfig

class PaymentServiceSettings(FinancialSettings):
    """Payment Service configuration extending shared financial settings."""

    # ✅ Service Identity (específico do payment)
    SERVICE_NAME: str = "payment-service"
    SERVICE_VERSION: str = "2.0.0"
    SERVICE_PORT: int = Field(default=8020, env="PAYMENT_SERVICE_PORT")

    # ✅ NOVO: Configurações específicas de pagamento (herda configurações financeiras da shared_lib)
    # Configurações financeiras básicas vêm da FinancialSettings

    # Payment Processing Specific
    FRAUD_DETECTION_ENABLED: bool = Field(default=True, env="PAYMENT_FRAUD_DETECTION_ENABLED")
    PCI_COMPLIANCE_LEVEL: str = Field(default="level-1", env="PAYMENT_PCI_COMPLIANCE_LEVEL")
    TOKEN_EXPIRY_HOURS: int = Field(default=24, env="PAYMENT_TOKEN_EXPIRY_HOURS")

    # Gateway Configuration
    STRIPE_ENABLED: bool = Field(default=True, env="PAYMENT_STRIPE_ENABLED")
    PAYPAL_ENABLED: bool = Field(default=True, env="PAYMENT_PAYPAL_ENABLED")
    PIX_ENABLED: bool = Field(default=True, env="PAYMENT_PIX_ENABLED")
    BOLETO_ENABLED: bool = Field(default=True, env="PAYMENT_BOLETO_ENABLED")

    # Retry Configuration
    RETRY_ATTEMPTS: int = Field(default=3, env="PAYMENT_RETRY_ATTEMPTS")
    RETRY_BACKOFF_FACTOR: float = Field(default=2.0, env="PAYMENT_RETRY_BACKOFF_FACTOR")

    # Webhook Configuration
    WEBHOOK_TIMEOUT: int = Field(default=30, env="PAYMENT_WEBHOOK_TIMEOUT")
    WEBHOOK_RETRY_ATTEMPTS: int = Field(default=5, env="PAYMENT_WEBHOOK_RETRY_ATTEMPTS")

    def get_payment_vault_paths(self) -> Dict[str, str]:
        """Get payment-specific Vault paths extending financial paths."""
        financial_paths = self.get_financial_vault_paths()

        payment_paths = {
            **financial_paths,
            "gateways": f"payment/{self.vault_environment}/gateways",
            "webhooks": f"payment/{self.vault_environment}/webhooks",
            "fraud_models": f"payment/{self.vault_environment}/fraud-models",
            "pci_certificates": f"payment/{self.vault_environment}/pci-certificates"
        }

        return payment_paths

    def get_enabled_gateways(self) -> List[str]:
        """Get list of enabled payment gateways."""
        gateways = []
        if self.STRIPE_ENABLED:
            gateways.append("stripe")
        if self.PAYPAL_ENABLED:
            gateways.append("paypal")
        if self.PIX_ENABLED:
            gateways.append("pix")
        if self.BOLETO_ENABLED:
            gateways.append("boleto")
        return gateways

    class Config:
        env_prefix = "PAYMENT_"
        case_sensitive = True
        env_file = ".env"

# ✅ NOVO: Configuração integrada usando shared_lib
class PaymentServiceConfig:
    """Integrated configuration manager using shared_lib components."""

    def __init__(self):
        self.settings = PaymentServiceSettings()
        self.financial_config = self.settings  # PaymentServiceSettings extends FinancialSettings

        # ✅ NOVO: Usando configurações compartilhadas da shared_lib
        self.database_config = DatabaseConfig(service_name="payment-service")
        self.messaging_config = MessagingConfig(service_name="payment-service")

    def get_payment_limits(self) -> Dict[str, Decimal]:
        """Get payment limits from financial configuration."""
        return self.financial_config.get_transaction_limits()

    def get_compliance_config(self) -> Dict[str, Any]:
        """Get compliance configuration from financial settings."""
        return self.financial_config.get_compliance_config()

    def get_gateway_config(self, gateway: str) -> Dict[str, Any]:
        """Get gateway-specific configuration."""
        return {
            "enabled": gateway in self.settings.get_enabled_gateways(),
            "timeout": self.settings.WEBHOOK_TIMEOUT,
            "retry_attempts": self.settings.WEBHOOK_RETRY_ATTEMPTS,
            "vault_path": f"{self.settings.get_payment_vault_paths()['gateways']}/{gateway}"
        }

# ✅ NOVO: Factory function
def get_payment_settings() -> PaymentServiceSettings:
    """Get payment service settings instance."""
    return PaymentServiceSettings()

def get_payment_config() -> PaymentServiceConfig:
    """Get payment service configuration instance."""
    return PaymentServiceConfig()
```

#### **Kubernetes Integration**
```python
class KubernetesConfig:
    """Kubernetes-native configuration management."""

    @staticmethod
    async def load_from_configmap(configmap_name: str) -> dict:
        """Load configuration from Kubernetes ConfigMap."""
        async with kubernetes.client.ApiClient() as api:
            v1 = kubernetes.client.CoreV1Api(api)
            configmap = await v1.read_namespaced_config_map(
                name=configmap_name,
                namespace="trix-payment"
            )
            return configmap.data

    @staticmethod
    async def load_secrets_from_vault() -> dict:
        """Load secrets from HashiCorp Vault via Kubernetes integration."""
        vault_client = hvac.Client(url=os.getenv('VAULT_URL'))

        # Use Kubernetes service account for authentication
        with open('/var/run/secrets/kubernetes.io/serviceaccount/token', 'r') as f:
            jwt_token = f.read()

        vault_client.auth.kubernetes.login(
            role='payment-service',
            jwt=jwt_token
        )

        # Retrieve payment service secrets
        secrets = {}
        secret_paths = [
            'payment-gateways/production',
            'database/production/payment-service',
            'encryption/production/payment-service'
        ]

        for path in secret_paths:
            secret = vault_client.secrets.kv.v2.read_secret_version(path=path)
            secrets.update(secret['data']['data'])

        return secrets
```

### 📦 **Enterprise Dependencies**
```txt
# Core Framework
fastapi==0.104.1              # Web framework
uvicorn[standard]==0.24.0     # ASGI server
gunicorn==21.2.0              # Production WSGI server

# Database & ORM
sqlalchemy==2.0.23            # ORM with Citus support
alembic==1.12.1               # Database migrations
asyncpg==0.29.0               # PostgreSQL async driver
psycopg2-binary==2.9.7        # PostgreSQL sync driver
pgbouncer==1.21.0             # Connection pooling

# Messaging & Events
kafka-python==2.0.2           # Apache Kafka client
pika==1.3.2                   # RabbitMQ client
redis[hiredis]==5.0.1         # Redis with C extensions
celery[redis]==5.3.4          # Background task processing

# Service Mesh & Security
hvac==1.2.1                   # HashiCorp Vault client
kubernetes==28.1.0            # Kubernetes Python client
istio-client==1.19.0          # Istio service mesh
cryptography==41.0.7          # Encryption utilities
python-jose[cryptography]==3.3.0  # JWT handling

# Observability
prometheus-client==0.18.0     # Prometheus metrics
opentelemetry-api==1.21.0     # OpenTelemetry tracing
opentelemetry-sdk==1.21.0     # OpenTelemetry SDK
jaeger-client==4.8.0          # Jaeger tracing
structlog==23.2.0             # Structured logging

# Payment Gateways
stripe==7.8.0                 # Stripe integration
paypalrestsdk==1.13.3         # PayPal integration
mercadopago==2.2.1            # Mercado Pago integration

# Machine Learning & Fraud Detection
scikit-learn==1.3.2          # ML algorithms
pandas==2.1.3                 # Data manipulation
numpy==1.25.2                 # Numerical computing

# Performance & Monitoring
aioredis==2.0.1               # Async Redis client
httpx==0.25.2                 # Async HTTP client
tenacity==8.2.3               # Retry mechanisms
```

## 🔗 **Integração com Microserviços Enterprise**

### 🤝 **Service-to-Service Communication**

#### **Auth Service Integration (8001)**
```python
class AuthServiceClient:
    """Secure integration with Auth Service via service mesh."""

    def __init__(self):
        self.base_url = "http://trix-core-auth:8001"
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(10.0),
            headers={"X-Service-Name": "payment-service"}
        )

    async def validate_payment_authorization(self, user_id: UUID, amount: Decimal, tenant_id: UUID) -> bool:
        """Validate user authorization for payment processing."""
        try:
            response = await self.client.post(
                f"{self.base_url}/api/v1/auth/validate-payment",
                json={
                    "user_id": str(user_id),
                    "tenant_id": str(tenant_id),
                    "amount": float(amount),
                    "action": "payment_processing"
                },
                headers=await self.get_service_token()
            )
            return response.status_code == 200 and response.json().get("authorized", False)
        except Exception as e:
            logger.error(f"Auth service validation failed: {e}")
            return False

    async def get_service_token(self) -> dict:
        """Get service-to-service authentication token."""
        # Implementation with mTLS and service mesh authentication
        pass
```

#### **User Service Integration (8002)**
```python
class UserServiceClient:
    """Integration with User Service for payment profiles."""

    async def get_user_payment_profile(self, user_id: UUID, tenant_id: UUID) -> UserPaymentProfile:
        """Retrieve user payment profile and preferences."""
        try:
            response = await self.client.get(
                f"{self.base_url}/api/v1/users/{user_id}/payment-profile",
                params={"tenant_id": str(tenant_id)},
                headers=await self.get_service_token()
            )

            if response.status_code == 200:
                return UserPaymentProfile(**response.json())
            else:
                return UserPaymentProfile.default()

        except Exception as e:
            logger.error(f"User service integration failed: {e}")
            return UserPaymentProfile.default()

    async def update_payment_history(self, user_id: UUID, payment_event: PaymentEvent):
        """Update user payment history via event."""
        await self.event_publisher.publish_user_payment_event(
            user_id=user_id,
            event_type="payment_completed",
            data=payment_event.dict()
        )
```

#### **Tenant Service Integration (8003)**
```python
class TenantServiceClient:
    """Integration with Tenant Service for payment configurations."""

    async def get_tenant_payment_config(self, tenant_id: UUID) -> TenantPaymentConfig:
        """Get tenant-specific payment configuration."""
        try:
            response = await self.client.get(
                f"{self.base_url}/api/v1/tenants/{tenant_id}/payment-config",
                headers=await self.get_service_token()
            )

            if response.status_code == 200:
                return TenantPaymentConfig(**response.json())
            else:
                return TenantPaymentConfig.default()

        except Exception as e:
            logger.error(f"Tenant service integration failed: {e}")
            return TenantPaymentConfig.default()

    async def validate_tenant_limits(self, tenant_id: UUID, amount: Decimal) -> bool:
        """Validate payment against tenant limits."""
        config = await self.get_tenant_payment_config(tenant_id)
        return amount <= config.max_transaction_amount
```

#### **Commerce Service Integration (8010)**
```python
class CommerceServiceClient:
    """Integration with Commerce Service for order processing."""

    async def process_order_payment(self, order_id: UUID, payment_data: PaymentData) -> PaymentResult:
        """Process payment for commerce order."""
        try:
            # Validate order exists and is payable
            order = await self.get_order_details(order_id)
            if not order or order.status != "pending_payment":
                raise PaymentError("Invalid order for payment")

            # Process payment
            payment = await self.payment_service.process_payment(payment_data)

            # Update order status via event
            await self.event_publisher.publish_order_payment_event(
                order_id=order_id,
                payment_id=payment.id,
                status="payment_completed" if payment.status == "completed" else "payment_failed"
            )

            return PaymentResult(
                payment_id=payment.id,
                status=payment.status,
                order_id=order_id
            )

        except Exception as e:
            logger.error(f"Commerce payment processing failed: {e}")
            raise PaymentError(f"Payment processing failed: {str(e)}")
```

#### **Notification Service Integration (8019)**
```python
class NotificationServiceClient:
    """Integration with Notification Service for payment alerts."""

    async def send_payment_notification(self, user_id: UUID, payment_event: PaymentEvent):
        """Send payment notification to user."""
        notification_data = {
            "user_id": str(user_id),
            "type": "payment_update",
            "title": self.get_notification_title(payment_event.event_type),
            "message": self.get_notification_message(payment_event),
            "data": payment_event.dict(),
            "channels": ["email", "sms", "push"],
            "priority": "high" if payment_event.event_type in ["payment.failed", "payment.fraud_detected"] else "normal"
        }

        await self.event_publisher.publish_notification_event(
            event_type="notification.send",
            data=notification_data
        )

    def get_notification_title(self, event_type: str) -> str:
        """Get localized notification title."""
        titles = {
            "payment.completed": "Payment Successful",
            "payment.failed": "Payment Failed",
            "payment.fraud_detected": "Suspicious Activity Detected",
            "subscription.renewed": "Subscription Renewed"
        }
        return titles.get(event_type, "Payment Update")
```

### 📡 **Event-Driven Integration Patterns**

#### **Saga Pattern for Distributed Transactions**
```python
class PaymentProcessingSaga:
    """Orchestrates distributed payment processing across services."""

    def __init__(self):
        self.event_publisher = EventPublisher()
        self.compensation_handlers = {}

    async def process_payment_saga(self, payment_request: PaymentRequest) -> SagaResult:
        """Execute payment processing saga with compensation."""
        saga_id = uuid4()

        try:
            # Step 1: Validate user authorization
            auth_result = await self.validate_user_auth(payment_request, saga_id)
            if not auth_result.success:
                return SagaResult(success=False, error="Authorization failed")

            # Step 2: Reserve payment amount
            reserve_result = await self.reserve_payment_amount(payment_request, saga_id)
            if not reserve_result.success:
                await self.compensate_auth(auth_result, saga_id)
                return SagaResult(success=False, error="Amount reservation failed")

            # Step 3: Process payment with gateway
            payment_result = await self.process_gateway_payment(payment_request, saga_id)
            if not payment_result.success:
                await self.compensate_reservation(reserve_result, saga_id)
                await self.compensate_auth(auth_result, saga_id)
                return SagaResult(success=False, error="Gateway processing failed")

            # Step 4: Update order status
            order_result = await self.update_order_status(payment_request, saga_id)
            if not order_result.success:
                await self.compensate_payment(payment_result, saga_id)
                await self.compensate_reservation(reserve_result, saga_id)
                await self.compensate_auth(auth_result, saga_id)
                return SagaResult(success=False, error="Order update failed")

            # Success - publish completion event
            await self.event_publisher.publish_saga_completed_event(saga_id, payment_result)
            return SagaResult(success=True, payment_id=payment_result.payment_id)

        except Exception as e:
            logger.error(f"Saga execution failed: {e}")
            await self.compensate_all(saga_id)
            return SagaResult(success=False, error=str(e))
```

#### **CQRS Implementation**
```python
class PaymentCommandHandler:
    """Handles payment commands with event sourcing."""

    async def handle_process_payment_command(self, command: ProcessPaymentCommand) -> CommandResult:
        """Process payment command and generate events."""

        # Validate command
        validation_result = await self.validate_command(command)
        if not validation_result.valid:
            return CommandResult(success=False, errors=validation_result.errors)

        # Create payment aggregate
        payment = Payment.create(
            tenant_id=command.tenant_id,
            user_id=command.user_id,
            amount=command.amount,
            payment_method=command.payment_method
        )

        # Generate domain events
        events = [
            PaymentInitiatedEvent(
                payment_id=payment.id,
                tenant_id=payment.tenant_id,
                amount=payment.amount,
                timestamp=datetime.utcnow()
            )
        ]

        # Persist events
        await self.event_store.save_events(payment.id, events)

        # Publish events
        for event in events:
            await self.event_publisher.publish_payment_event(event)

        return CommandResult(success=True, payment_id=payment.id)

class PaymentQueryHandler:
    """Handles payment queries with optimized read models."""

    async def get_payment_history(self, user_id: UUID, tenant_id: UUID, filters: PaymentFilters) -> PaymentHistory:
        """Get paginated payment history with filters."""

        # Use read-optimized view
        query = self.read_db.query(PaymentView).filter(
            PaymentView.user_id == user_id,
            PaymentView.tenant_id == tenant_id
        )

        # Apply filters
        if filters.status:
            query = query.filter(PaymentView.status == filters.status)
        if filters.date_from:
            query = query.filter(PaymentView.created_at >= filters.date_from)
        if filters.date_to:
            query = query.filter(PaymentView.created_at <= filters.date_to)

        # Apply pagination
        total = await query.count()
        payments = await query.offset(filters.offset).limit(filters.limit).all()

        return PaymentHistory(
            payments=payments,
            total=total,
            page=filters.page,
            per_page=filters.limit
        )
```

### 📡 **Enterprise API Architecture**

#### **Versioned REST APIs (v1)**
```python
# Payment Processing APIs
POST   /api/v1/payments/process              # Process single payment
POST   /api/v1/payments/bulk                 # Bulk payment processing
GET    /api/v1/payments/{payment_id}         # Get payment details
POST   /api/v1/payments/{payment_id}/refund  # Process refund
GET    /api/v1/payments/history              # Payment history with filters
GET    /api/v1/payments/analytics            # Payment analytics

# Gateway Management APIs
GET    /api/v1/gateways                      # List available gateways
POST   /api/v1/gateways/{gateway_id}/configure # Configure gateway
GET    /api/v1/gateways/{gateway_id}/status  # Gateway health status
POST   /api/v1/gateways/{gateway_id}/test    # Test gateway connection

# Transaction Management APIs
GET    /api/v1/transactions                  # List transactions with filters
GET    /api/v1/transactions/{transaction_id} # Get transaction details
POST   /api/v1/transactions/{transaction_id}/retry # Retry failed transaction
GET    /api/v1/transactions/analytics        # Transaction analytics

# Subscription Management APIs
POST   /api/v1/subscriptions                 # Create subscription
GET    /api/v1/subscriptions/{subscription_id} # Get subscription details
PUT    /api/v1/subscriptions/{subscription_id}/plan # Change subscription plan
DELETE /api/v1/subscriptions/{subscription_id} # Cancel subscription
POST   /api/v1/subscriptions/{subscription_id}/pause # Pause subscription
POST   /api/v1/subscriptions/{subscription_id}/resume # Resume subscription

# Billing & Invoicing APIs
GET    /api/v1/billing/invoices              # List invoices
POST   /api/v1/billing/invoices/{invoice_id}/pay # Pay invoice
GET    /api/v1/billing/statements            # Billing statements
POST   /api/v1/billing/invoices/{invoice_id}/download # Download invoice PDF

# Fraud Detection APIs
POST   /api/v1/fraud/analyze                 # Analyze transaction for fraud
GET    /api/v1/fraud/rules                   # Get fraud detection rules
POST   /api/v1/fraud/rules                   # Create fraud detection rule
PUT    /api/v1/fraud/rules/{rule_id}         # Update fraud rule
```

#### **Webhook Endpoints (Multi-Gateway)**
```python
# Gateway Webhooks
POST   /webhooks/stripe                      # Stripe webhook handler
POST   /webhooks/paypal                      # PayPal webhook handler
POST   /webhooks/pagseguro                   # PagSeguro webhook handler
POST   /webhooks/mercadopago                 # Mercado Pago webhook handler
POST   /webhooks/pix                         # PIX webhook handler
POST   /webhooks/boleto                      # Boleto webhook handler

# Internal Webhooks
POST   /webhooks/fraud-alert                 # Fraud detection alerts
POST   /webhooks/subscription-renewal        # Subscription renewal notifications
POST   /webhooks/payment-failure             # Payment failure notifications
```

#### **Health & Observability Endpoints**
```python
# Health Checks
GET    /health                               # Overall service health
GET    /health/detailed                      # Detailed health with dependencies
GET    /ready                                # Kubernetes readiness probe
GET    /live                                 # Kubernetes liveness probe

# Metrics & Monitoring
GET    /metrics                              # Prometheus metrics
GET    /metrics/payment-gateways             # Gateway-specific metrics
GET    /metrics/fraud-detection              # Fraud detection metrics
GET    /metrics/performance                  # Performance metrics

# Debugging & Diagnostics
GET    /debug/config                         # Service configuration (sanitized)
GET    /debug/connections                    # Database connection status
GET    /debug/cache                          # Cache status and statistics
GET    /debug/events                         # Recent event processing status
```

#### **GraphQL API (Optional)**
```python
# GraphQL endpoint for complex queries
POST   /graphql                              # GraphQL query endpoint
GET    /graphql/playground                   # GraphQL playground (dev only)

# Example GraphQL queries:
# query PaymentHistory($userId: UUID!, $filters: PaymentFilters) {
#   payments(userId: $userId, filters: $filters) {
#     id
#     amount
#     status
#     createdAt
#     gateway {
#       name
#       provider
#     }
#     transactions {
#       id
#       status
#       gatewayResponse
#     }
#   }
# }
```

## 🚀 **Como Usar o Payment Service v2.0 (com shared_lib)**

### **1. Configuração e Inicialização**
```python
# ✅ NOVO: Configuração simplificada usando shared_lib
from microservices.core.payment_service.app.core.config.settings import get_payment_config
from microservices.core.shared_lib.config.infrastructure.financial.messaging import FinancialEventManager
from microservices.core.payment_service.app.core.observability.metrics import payment_metrics

# Configurações do Payment Service (integra shared_lib automaticamente)
config = get_payment_config()
settings = config.settings

# Inicializar componentes
async def initialize_payment_service():
    """Initialize Payment Service with shared_lib integration."""

    # ✅ NOVO: Event manager financeiro da shared_lib
    event_manager = FinancialEventManager("payment-service")
    await event_manager.initialize()

    # ✅ NOVO: Configuração de gateways usando shared_lib
    enabled_gateways = settings.get_enabled_gateways()
    for gateway in enabled_gateways:
        gateway_config = config.get_gateway_config(gateway)
        await initialize_gateway(gateway, gateway_config)

    # ✅ Métricas (usa shared_lib + específicas)
    # payment_metrics já está inicializado automaticamente

    print(f"✅ {settings.SERVICE_NAME} v2.0 initialized with shared_lib")
    print(f"✅ Enabled gateways: {enabled_gateways}")
```

### **2. Publicação de Eventos**
```python
# ✅ NOVO: Eventos simplificados usando shared_lib
from microservices.core.shared_lib.config.infrastructure.financial.messaging import FinancialEventManager
from microservices.core.shared_lib.config.infrastructure.financial.events import FinancialEventType

# Exemplo: Processar pagamento e publicar evento
async def process_payment_with_events(payment_data: dict, tenant_id: UUID):
    """Process payment and publish events using shared_lib."""

    # 1. Processar pagamento
    payment = await process_payment_in_gateway(payment_data, tenant_id)

    # 2. ✅ NOVO: Criar evento financeiro usando shared_lib
    payment_event = PaymentEvent(
        event_type=FinancialEventType.TRANSACTION_COMPLETED,
        tenant_id=tenant_id,
        user_id=payment_data["user_id"],
        payment_id=payment.id,
        gateway_id=payment.gateway_id,
        amount=payment.amount,
        currency=payment.currency,
        payment_method=payment.payment_method
    )

    # 3. ✅ NOVO: Publicar usando event manager da shared_lib
    event_manager = FinancialEventManager("payment-service")
    await event_manager.publish_event(
        event=payment_event,
        channels=["kafka", "rabbitmq", "redis"]  # Multi-channel
    )

    return payment

async def handle_fraud_detection(payment_id: UUID, fraud_score: float, tenant_id: UUID):
    """Handle fraud detection using shared_lib."""

    if fraud_score > 0.8:  # High risk
        fraud_event = PaymentEvent(
            event_type=FinancialEventType.FRAUD_DETECTED,
            tenant_id=tenant_id,
            payment_id=payment_id,
            data={"fraud_score": fraud_score, "risk_level": "high"}
        )

        event_manager = FinancialEventManager("payment-service")
        await event_manager.publish_event(fraud_event, channels=["kafka", "rabbitmq"])
```

### **3. Métricas e Observabilidade**
```python
# ✅ NOVO: Métricas usando shared_lib + específicas
from microservices.core.shared_lib.config.infrastructure.observability.metrics import MetricsCollector
from microservices.core.payment_service.app.core.observability.metrics import payment_metrics

async def payment_operation_with_metrics(operation: str, tenant_id: str, gateway: str):
    """Payment operation with metrics using shared_lib."""

    start_time = time.time()
    status = "success"

    try:
        # Sua lógica de negócio aqui
        result = await perform_payment_operation()
        return result

    except Exception as e:
        status = "error"
        raise

    finally:
        duration = time.time() - start_time

        # ✅ NOVO: Métricas compartilhadas (HTTP, infraestrutura)
        shared_metrics = MetricsCollector("payment-service", "2.0.0", "production")
        shared_metrics.requests_counter.labels(
            endpoint=f"payment.{operation}",
            method="POST",
            status=status
        ).inc()

        # ✅ NOVO: Métricas específicas do Payment Service
        payment_metrics.record_payment_operation(
            operation=operation,
            tenant_id=tenant_id,
            gateway=gateway,
            status=status,
            duration=duration
        )

        # ✅ NOVO: Métricas financeiras da shared_lib
        if operation == "process_payment":
            payment_metrics.record_transaction_processed(
                tenant_id=tenant_id,
                gateway=gateway,
                amount=result.get("amount", 0),
                currency=result.get("currency", "USD")
            )
```

### **4. Segurança e Compliance PCI-DSS**
```python
# ✅ NOVO: Segurança usando shared_lib
from microservices.core.shared_lib.config.infrastructure.financial.security import PCIComplianceManager

async def secure_payment_processing(card_data: dict, tenant_id: UUID):
    """Secure payment processing with PCI compliance using shared_lib."""

    # ✅ NOVO: Gerenciador PCI da shared_lib
    pci_manager = PCIComplianceManager("payment-service")

    # 1. Validação PCI
    await pci_manager.validate_card_data(card_data)

    # 2. Tokenização segura
    tokenizer = PaymentPCICompliantTokenizer()
    tokenized_card = await tokenizer.tokenize_card_data(card_data)

    # 3. Processamento com dados tokenizados
    payment_result = await process_payment_with_token(tokenized_card, tenant_id)

    # 4. Auditoria PCI
    await pci_manager.log_pci_event(
        event_type="payment_processed",
        tenant_id=tenant_id,
        data={"payment_id": payment_result.id, "tokenized": True}
    )

    return payment_result
```

## 🚀 **Roadmap Enterprise (v2.0)**

### 🎯 **Fase 1: Migração para Shared Library (Sprint 1-2)**
1. **✅ Financial Configuration**: Migrar para FinancialSettings da shared_lib
2. **🔄 Messaging Integration**: Migrar para FinancialEventManager da shared_lib
3. **🔄 Security Components**: Migrar para componentes de segurança financeira
4. **🔄 Observability**: Migrar para métricas e logging compartilhados
5. **🔄 Database Layer**: Migrar para utilitários de banco compartilhados

### 🎯 **Fase 2: Event-Driven Architecture (Sprint 3-4)**
1. **✅ Kafka Integration**: Eventos críticos de pagamento usando shared_lib
2. **✅ RabbitMQ Setup**: Mensagens rápidas e notificações usando shared_lib
3. **🔄 Event Sourcing**: Histórico imutável de transações
4. **🔄 CQRS Implementation**: Separação comando/consulta
5. **🔄 Saga Patterns**: Transações distribuídas

### 🎯 **Fase 3: Security & Compliance (Sprint 5-6)**
1. **✅ PCI-DSS Level 1**: Compliance usando shared_lib
2. **🔄 Advanced Tokenization**: Vault-based tokenization usando shared_lib
3. **🔄 ML Fraud Detection**: Modelos de machine learning
4. **✅ OPA Policies**: Autorização baseada em políticas usando shared_lib
5. **🔄 Security Monitoring**: Falco para runtime security

### 🎯 **Fase 4: Scalability & Performance (Sprint 7-8)**
1. **🔄 Multi-Region Deployment**: Distribuição geográfica
2. **🔄 Auto-Scaling**: HPA e VPA para Kubernetes
3. **✅ Connection Pooling**: PgBouncer usando shared_lib
4. **✅ Caching Strategy**: Redis Cluster usando shared_lib
5. **🔄 Load Balancing**: Istio traffic management

### 🎯 **Fase 5: Observability & Analytics (Sprint 9-10)**
1. **✅ Distributed Tracing**: Jaeger usando shared_lib
2. **✅ ELK Stack**: Logging centralizado usando shared_lib
3. **🔄 Grafana Dashboards**: Visualização de métricas
4. **🔄 Business Intelligence**: Analytics de pagamentos
5. **🔄 Alerting**: PagerDuty integration

## 📈 **Enterprise Metrics & KPIs**

### 🎯 **Performance Targets (Enterprise Scale)**
- **Latência P95:** <50ms para processamento de pagamento
- **Latência P99:** <100ms para operações críticas
- **Throughput:** 100K+ transações/segundo por região
- **Disponibilidade:** 99.99% uptime (4.38 minutos/mês)
- **Success Rate:** >99.9% para transações válidas
- **Fraud Detection:** <0.1% false positives, >99% true positives

### 📊 **Business Metrics**
- **Revenue Processing:** Volume total processado
- **Gateway Performance:** Comparação de success rates
- **Subscription Metrics:** Churn rate, MRR, LTV
- **Regional Performance:** Latência por região geográfica
- **Compliance Metrics:** PCI-DSS audit scores
- **Cost Optimization:** Gateway fees vs. performance

### 🔍 **Technical Metrics**
```python
# Prometheus metrics examples
payment_requests_total = Counter('payment_requests_total', 'Total payment requests', ['gateway', 'status', 'region'])
payment_processing_duration = Histogram('payment_processing_duration_seconds', 'Payment processing time', ['gateway'])
fraud_detection_accuracy = Gauge('fraud_detection_accuracy', 'Fraud detection accuracy percentage')
database_connections = Gauge('database_connections_active', 'Active database connections', ['shard'])
cache_hit_ratio = Gauge('cache_hit_ratio', 'Cache hit ratio percentage', ['cache_type'])
```

## 🔌 **Advanced Gateway Integrations**

### **Multi-Gateway Processing Engine**
```python
class PaymentGatewayOrchestrator:
    """Orchestrates payment processing across multiple gateways with intelligent routing."""

    def __init__(self):
        self.gateways = {
            'stripe': StripeProcessor(),
            'paypal': PayPalProcessor(),
            'pix': PIXProcessor(),
            'boleto': BoletoProcessor(),
            'mercadopago': MercadoPagoProcessor()
        }
        self.router = GeographicPaymentRouter()
        self.fraud_detector = FraudDetectionEngine()

    async def process_payment(self, payment_request: PaymentRequest) -> PaymentResult:
        """Process payment with intelligent gateway selection and fraud detection."""

        # 1. Fraud detection
        fraud_analysis = await self.fraud_detector.analyze_transaction(payment_request)
        if fraud_analysis.risk_level == RiskLevel.HIGH:
            return PaymentResult(
                success=False,
                error="Transaction blocked due to high fraud risk",
                fraud_score=fraud_analysis.risk_score
            )

        # 2. Gateway selection based on geography, amount, and method
        selected_gateway = await self.router.select_optimal_gateway(
            region=payment_request.region,
            amount=payment_request.amount,
            payment_method=payment_request.payment_method,
            currency=payment_request.currency
        )

        # 3. Process payment with selected gateway
        processor = self.gateways[selected_gateway]
        result = await processor.process_payment(payment_request)

        # 4. Handle result and publish events
        if result.success:
            await self.publish_payment_success_event(payment_request, result)
        else:
            await self.handle_payment_failure(payment_request, result, selected_gateway)

        return result

    async def handle_payment_failure(self, request: PaymentRequest, result: PaymentResult, gateway: str):
        """Handle payment failure with retry logic and fallback gateways."""

        # Check if we should retry with a different gateway
        if result.error_type == "gateway_timeout" and request.retry_count < 3:
            fallback_gateway = await self.router.get_fallback_gateway(gateway, request.region)
            if fallback_gateway:
                request.retry_count += 1
                return await self.process_payment(request)

        # Publish failure event
        await self.publish_payment_failure_event(request, result)
```

### **Brazilian Payment Methods Integration**

#### **PIX Integration (Instant Payment)**
```python
class PIXProcessor(BasePaymentProcessor):
    """PIX (Brazilian Instant Payment) processor with QR code generation."""

    def __init__(self):
        self.certificate_path = self.vault_manager.get_secret('pix/certificate_path')
        self.private_key_path = self.vault_manager.get_secret('pix/private_key_path')
        self.bank_code = self.vault_manager.get_secret('pix/bank_code')
        self.pix_key = self.vault_manager.get_secret('pix/key')

    async def generate_pix_payment(self, payment_data: PaymentData) -> PIXPaymentResult:
        """Generate PIX payment with QR code and copy-paste code."""

        # Generate PIX payload
        pix_payload = {
            "version": "01",
            "initiation_method": "12",  # Static QR code
            "merchant_account": self.pix_key,
            "merchant_category": "0000",
            "transaction_currency": "986",  # BRL
            "transaction_amount": str(payment_data.amount),
            "country_code": "BR",
            "merchant_name": payment_data.merchant_name,
            "merchant_city": payment_data.merchant_city,
            "additional_data": {
                "reference": str(payment_data.payment_id)
            }
        }

        # Generate QR code
        qr_code_data = self.generate_pix_qr_code(pix_payload)
        copy_paste_code = self.generate_copy_paste_code(pix_payload)

        # Set expiration (30 minutes for PIX)
        expiration = datetime.utcnow() + timedelta(minutes=30)

        return PIXPaymentResult(
            qr_code_data=qr_code_data,
            copy_paste_code=copy_paste_code,
            pix_key=self.pix_key,
            expiration=expiration,
            payment_id=payment_data.payment_id
        )

    async def process_pix_webhook(self, webhook_data: dict) -> WebhookResult:
        """Process PIX webhook notification."""

        # Validate webhook signature
        if not self.validate_webhook_signature(webhook_data):
            raise WebhookValidationError("Invalid webhook signature")

        payment_id = webhook_data.get("external_reference")
        status = webhook_data.get("status")

        if status == "PAID":
            # Update payment status
            await self.update_payment_status(payment_id, PaymentStatus.COMPLETED)

            # Publish success event
            await self.event_publisher.publish_payment_event(
                PaymentCompletedEvent(
                    payment_id=payment_id,
                    transaction_id=webhook_data.get("transaction_id"),
                    amount=Decimal(webhook_data.get("amount")),
                    timestamp=datetime.utcnow()
                )
            )

        return WebhookResult(success=True, payment_id=payment_id)
```

#### **Boleto Bancário Integration**
```python
class BoletoProcessor(BasePaymentProcessor):
    """Boleto Bancário processor with PDF generation."""

    async def generate_boleto(self, payment_data: PaymentData) -> BoletoResult:
        """Generate boleto with barcode and PDF."""

        # Calculate due date (3 days from now)
        due_date = datetime.now() + timedelta(days=3)

        # Generate boleto data
        boleto_data = {
            "bank_code": "033",  # Santander
            "agency": self.vault_manager.get_secret('boleto/agency'),
            "account": self.vault_manager.get_secret('boleto/account'),
            "amount": payment_data.amount,
            "due_date": due_date,
            "document_number": self.generate_document_number(),
            "payer": {
                "name": payment_data.payer_name,
                "document": payment_data.payer_document,
                "address": payment_data.payer_address
            },
            "instructions": f"Pagamento referente ao pedido {payment_data.order_id}",
            "payment_id": payment_data.payment_id
        }

        # Generate barcode
        barcode = self.generate_boleto_barcode(boleto_data)

        # Generate PDF
        pdf_content = await self.generate_boleto_pdf(boleto_data, barcode)

        # Store boleto in database
        await self.store_boleto_data(boleto_data, barcode)

        return BoletoResult(
            barcode=barcode,
            due_date=due_date,
            pdf_content=pdf_content,
            boleto_number=boleto_data["document_number"],
            payment_id=payment_data.payment_id
        )

    def generate_boleto_barcode(self, boleto_data: dict) -> str:
        """Generate boleto barcode following FEBRABAN standards."""

        # Bank code (3 digits)
        bank_code = boleto_data["bank_code"]

        # Currency code (1 digit) - always 9 for Real
        currency_code = "9"

        # Due date factor (4 digits)
        due_date_factor = self.calculate_due_date_factor(boleto_data["due_date"])

        # Amount (10 digits)
        amount_str = f"{int(boleto_data['amount'] * 100):010d}"

        # Specific fields (25 digits)
        specific_fields = f"{boleto_data['agency']}{boleto_data['account']}{boleto_data['document_number']:011d}"

        # Calculate verification digit
        verification_digit = self.calculate_verification_digit(
            bank_code + currency_code + due_date_factor + amount_str + specific_fields
        )

        return f"{bank_code}{currency_code}{verification_digit}{due_date_factor}{amount_str}{specific_fields}"
```

## 🔐 **Enterprise Security & Compliance**

### **Advanced Fraud Detection Engine**
```python
class MLFraudDetectionEngine:
    """Machine Learning-based fraud detection with real-time analysis."""

    def __init__(self):
        self.models = {
            'velocity_analyzer': VelocityAnalyzer(),
            'geolocation_validator': GeolocationValidator(),
            'device_fingerprint_analyzer': DeviceFingerprintAnalyzer(),
            'behavior_analyzer': BehaviorAnalyzer(),
            'ml_classifier': MLFraudClassifier()
        }
        self.risk_rules = self.load_risk_rules()

    async def analyze_transaction(self, transaction: TransactionData) -> FraudAnalysisResult:
        """Comprehensive fraud analysis using multiple detection methods."""

        # Extract features for analysis
        features = await self.extract_features(transaction)

        # Rule-based analysis
        rule_score = await self.apply_risk_rules(features)

        # Velocity analysis
        velocity_score = await self.models['velocity_analyzer'].analyze(transaction)

        # Geolocation validation
        geo_score = await self.models['geolocation_validator'].validate(transaction)

        # Device fingerprinting
        device_score = await self.models['device_fingerprint_analyzer'].analyze(transaction)

        # Behavioral analysis
        behavior_score = await self.models['behavior_analyzer'].analyze(transaction)

        # Machine learning classification
        ml_score = await self.models['ml_classifier'].predict(features)

        # Calculate weighted final score
        final_score = self.calculate_weighted_score({
            'rules': rule_score,
            'velocity': velocity_score,
            'geolocation': geo_score,
            'device': device_score,
            'behavior': behavior_score,
            'ml': ml_score
        })

        # Determine risk level and recommended action
        risk_level = self.categorize_risk(final_score)
        recommended_action = self.get_recommendation(final_score, risk_level)

        return FraudAnalysisResult(
            risk_score=final_score,
            risk_level=risk_level,
            recommended_action=recommended_action,
            triggered_rules=self.get_triggered_rules(features),
            analysis_details={
                'rule_score': rule_score,
                'velocity_score': velocity_score,
                'geo_score': geo_score,
                'device_score': device_score,
                'behavior_score': behavior_score,
                'ml_score': ml_score
            }
        )

    async def extract_features(self, transaction: TransactionData) -> dict:
        """Extract features for fraud detection analysis."""

        # Get user transaction history
        user_history = await self.get_user_transaction_history(
            transaction.user_id,
            days=30
        )

        # Get device information
        device_info = await self.get_device_information(transaction.device_fingerprint)

        # Get geolocation data
        geo_data = await self.get_geolocation_data(transaction.ip_address)

        return {
            # Transaction features
            'amount': float(transaction.amount),
            'currency': transaction.currency,
            'payment_method': transaction.payment_method,
            'hour_of_day': transaction.timestamp.hour,
            'day_of_week': transaction.timestamp.weekday(),

            # User behavior features
            'user_age_days': (datetime.utcnow() - transaction.user_created_at).days,
            'transactions_last_24h': len([t for t in user_history if t.timestamp > datetime.utcnow() - timedelta(hours=24)]),
            'transactions_last_7d': len([t for t in user_history if t.timestamp > datetime.utcnow() - timedelta(days=7)]),
            'avg_transaction_amount': sum(t.amount for t in user_history) / len(user_history) if user_history else 0,

            # Device features
            'device_new': device_info.is_new_device,
            'device_trusted': device_info.is_trusted,
            'browser': device_info.browser,
            'os': device_info.operating_system,

            # Geolocation features
            'country': geo_data.country,
            'city': geo_data.city,
            'is_vpn': geo_data.is_vpn,
            'is_proxy': geo_data.is_proxy,
            'distance_from_usual': geo_data.distance_from_usual_location
        }
```

### **PCI-DSS Level 1 Compliance Framework**
```python
class PCIComplianceManager:
    """Comprehensive PCI-DSS Level 1 compliance management."""

    def __init__(self):
        self.vault_client = VaultClient()
        self.audit_logger = AuditLogger()
        self.encryption_service = EncryptionService()

    async def tokenize_sensitive_data(self, card_data: CardData) -> TokenizedCardData:
        """Tokenize sensitive card data with PCI compliance."""

        # Validate card data format
        if not self.validate_card_format(card_data):
            raise PCIValidationError("Invalid card data format")

        # Generate secure token
        token = self.generate_secure_token()

        # Encrypt sensitive data
        encrypted_data = await self.encryption_service.encrypt(
            data=card_data.to_dict(),
            key_id="payment-encryption-key"
        )

        # Store encrypted data in secure vault
        await self.vault_client.store_encrypted_data(
            token=token,
            encrypted_data=encrypted_data,
            expiry=datetime.utcnow() + timedelta(hours=24)
        )

        # Log tokenization event
        await self.audit_logger.log_tokenization_event(
            token=token,
            user_id=card_data.user_id,
            timestamp=datetime.utcnow()
        )

        return TokenizedCardData(
            token=token,
            last_four=card_data.number[-4:],
            brand=self.detect_card_brand(card_data.number),
            expiry_month=card_data.expiry_month,
            expiry_year=card_data.expiry_year
        )

    async def detokenize_for_processing(self, token: str, purpose: str) -> CardData:
        """Securely detokenize card data for payment processing."""

        # Validate token and purpose
        if not await self.validate_detokenization_request(token, purpose):
            raise PCISecurityError("Unauthorized detokenization request")

        # Retrieve encrypted data
        encrypted_data = await self.vault_client.retrieve_encrypted_data(token)

        # Decrypt data
        card_data = await self.encryption_service.decrypt(
            encrypted_data=encrypted_data,
            key_id="payment-encryption-key"
        )

        # Log detokenization event
        await self.audit_logger.log_detokenization_event(
            token=token,
            purpose=purpose,
            timestamp=datetime.utcnow()
        )

        return CardData(**card_data)

    async def audit_pci_compliance(self) -> PCIAuditReport:
        """Generate comprehensive PCI compliance audit report."""

        # Check encryption standards
        encryption_compliance = await self.audit_encryption_compliance()

        # Check access controls
        access_control_compliance = await self.audit_access_controls()

        # Check network security
        network_security_compliance = await self.audit_network_security()

        # Check vulnerability management
        vulnerability_compliance = await self.audit_vulnerability_management()

        # Check monitoring and logging
        monitoring_compliance = await self.audit_monitoring_logging()

        return PCIAuditReport(
            overall_score=self.calculate_compliance_score([
                encryption_compliance,
                access_control_compliance,
                network_security_compliance,
                vulnerability_compliance,
                monitoring_compliance
            ]),
            encryption_compliance=encryption_compliance,
            access_control_compliance=access_control_compliance,
            network_security_compliance=network_security_compliance,
            vulnerability_compliance=vulnerability_compliance,
            monitoring_compliance=monitoring_compliance,
            recommendations=self.generate_compliance_recommendations(),
            audit_timestamp=datetime.utcnow()
        )
```

## 📊 **Enterprise Analytics & Business Intelligence**

### **Real-Time Payment Analytics**
```python
class PaymentAnalyticsEngine:
    """Real-time analytics engine for payment processing insights."""

    def __init__(self):
        self.metrics_collector = PrometheusMetrics()
        self.time_series_db = InfluxDBClient()
        self.cache = RedisClient()

    async def track_payment_metrics(self, payment_event: PaymentEvent):
        """Track payment metrics in real-time."""

        # Update Prometheus metrics
        self.metrics_collector.increment_counter(
            'payments_total',
            labels={
                'gateway': payment_event.gateway,
                'status': payment_event.status,
                'currency': payment_event.currency,
                'region': payment_event.region,
                'payment_method': payment_event.payment_method
            }
        )

        # Record processing time
        if payment_event.processing_time_ms:
            self.metrics_collector.observe_histogram(
                'payment_processing_duration_seconds',
                payment_event.processing_time_ms / 1000,
                labels={'gateway': payment_event.gateway}
            )

        # Store detailed metrics in time series database
        await self.time_series_db.write_point(
            measurement='payment_events',
            tags={
                'gateway': payment_event.gateway,
                'status': payment_event.status,
                'currency': payment_event.currency,
                'region': payment_event.region
            },
            fields={
                'amount': float(payment_event.amount),
                'processing_time_ms': payment_event.processing_time_ms,
                'fraud_score': payment_event.fraud_score
            },
            timestamp=payment_event.timestamp
        )

    async def generate_business_intelligence_report(self, tenant_id: UUID, period: DateRange) -> BIReport:
        """Generate comprehensive business intelligence report."""

        # Payment volume analysis
        volume_metrics = await self.calculate_payment_volume_metrics(tenant_id, period)

        # Gateway performance analysis
        gateway_performance = await self.analyze_gateway_performance(tenant_id, period)

        # Fraud detection effectiveness
        fraud_metrics = await self.calculate_fraud_metrics(tenant_id, period)

        # Revenue analysis
        revenue_analysis = await self.analyze_revenue_trends(tenant_id, period)

        # Customer behavior insights
        customer_insights = await self.analyze_customer_behavior(tenant_id, period)

        return BIReport(
            tenant_id=tenant_id,
            period=period,
            volume_metrics=volume_metrics,
            gateway_performance=gateway_performance,
            fraud_metrics=fraud_metrics,
            revenue_analysis=revenue_analysis,
            customer_insights=customer_insights,
            generated_at=datetime.utcnow()
        )

    async def calculate_payment_volume_metrics(self, tenant_id: UUID, period: DateRange) -> VolumeMetrics:
        """Calculate payment volume metrics for the specified period."""

        query = """
        SELECT
            COUNT(*) as total_transactions,
            SUM(amount) as total_volume,
            AVG(amount) as average_transaction_value,
            COUNT(DISTINCT user_id) as unique_customers,
            COUNT(CASE WHEN status = 'completed' THEN 1 END) as successful_transactions,
            COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_transactions
        FROM payments
        WHERE tenant_id = $1
        AND created_at BETWEEN $2 AND $3
        """

        result = await self.db.fetch_one(query, tenant_id, period.start, period.end)

        success_rate = (result['successful_transactions'] / result['total_transactions'] * 100) if result['total_transactions'] > 0 else 0

        return VolumeMetrics(
            total_transactions=result['total_transactions'],
            total_volume=result['total_volume'],
            average_transaction_value=result['average_transaction_value'],
            unique_customers=result['unique_customers'],
            success_rate=success_rate,
            failure_rate=100 - success_rate
        )
```

### **Performance Monitoring & Alerting**
```python
class PaymentMonitoringSystem:
    """Comprehensive monitoring system for payment service."""

    def __init__(self):
        self.alert_manager = AlertManager()
        self.metrics_collector = PrometheusMetrics()
        self.notification_service = NotificationService()

    async def monitor_payment_health(self):
        """Continuous monitoring of payment system health."""

        while True:
            try:
                # Check gateway health
                await self.check_gateway_health()

                # Monitor transaction success rates
                await self.monitor_success_rates()

                # Check fraud detection performance
                await self.monitor_fraud_detection()

                # Monitor database performance
                await self.monitor_database_performance()

                # Check system resources
                await self.monitor_system_resources()

                await asyncio.sleep(30)  # Check every 30 seconds

            except Exception as e:
                logger.error(f"Monitoring error: {e}")
                await asyncio.sleep(60)  # Wait longer on error

    async def check_gateway_health(self):
        """Monitor payment gateway health and availability."""

        gateways = ['stripe', 'paypal', 'pix', 'boleto', 'mercadopago']

        for gateway in gateways:
            try:
                # Test gateway connectivity
                health_check_result = await self.test_gateway_connectivity(gateway)

                if not health_check_result.healthy:
                    await self.alert_manager.send_alert(
                        AlertType.GATEWAY_DOWN,
                        f"Gateway {gateway} is not responding",
                        severity=AlertSeverity.HIGH,
                        metadata={'gateway': gateway, 'error': health_check_result.error}
                    )

                # Update gateway health metrics
                self.metrics_collector.set_gauge(
                    'gateway_health_status',
                    1 if health_check_result.healthy else 0,
                    labels={'gateway': gateway}
                )

            except Exception as e:
                logger.error(f"Gateway health check failed for {gateway}: {e}")

    async def monitor_success_rates(self):
        """Monitor payment success rates and alert on anomalies."""

        # Calculate success rate for last 5 minutes
        current_success_rate = await self.calculate_recent_success_rate(minutes=5)

        # Get baseline success rate (last 24 hours)
        baseline_success_rate = await self.calculate_baseline_success_rate(hours=24)

        # Check if current rate is significantly lower than baseline
        if current_success_rate < baseline_success_rate * 0.9:  # 10% drop threshold
            await self.alert_manager.send_alert(
                AlertType.SUCCESS_RATE_DROP,
                f"Payment success rate dropped to {current_success_rate:.2f}% (baseline: {baseline_success_rate:.2f}%)",
                severity=AlertSeverity.HIGH,
                metadata={
                    'current_rate': current_success_rate,
                    'baseline_rate': baseline_success_rate
                }
            )

        # Update success rate metrics
        self.metrics_collector.set_gauge('payment_success_rate', current_success_rate)

    async def monitor_fraud_detection(self):
        """Monitor fraud detection system performance."""

        # Check fraud detection response time
        fraud_response_time = await self.measure_fraud_detection_latency()

        if fraud_response_time > 500:  # 500ms threshold
            await self.alert_manager.send_alert(
                AlertType.FRAUD_DETECTION_SLOW,
                f"Fraud detection response time: {fraud_response_time}ms",
                severity=AlertSeverity.MEDIUM,
                metadata={'response_time_ms': fraud_response_time}
            )

        # Check fraud detection accuracy
        fraud_accuracy = await self.calculate_fraud_detection_accuracy()

        if fraud_accuracy < 0.95:  # 95% accuracy threshold
            await self.alert_manager.send_alert(
                AlertType.FRAUD_DETECTION_ACCURACY,
                f"Fraud detection accuracy dropped to {fraud_accuracy:.2f}",
                severity=AlertSeverity.MEDIUM,
                metadata={'accuracy': fraud_accuracy}
            )
```

## 🐳 **Kubernetes & Container Orchestration**

### **Helm Chart Configuration**
```yaml
# helm/payment-service/values.yaml
replicaCount: 3

image:
  repository: trix/payment-service
  tag: "2.0.0"
  pullPolicy: IfNotPresent

service:
  type: ClusterIP
  port: 8020
  targetPort: 8020

ingress:
  enabled: true
  className: "istio"
  annotations:
    kubernetes.io/ingress.class: istio
    cert-manager.io/cluster-issuer: letsencrypt-prod
  hosts:
    - host: payment-api.trix.com
      paths:
        - path: /
          pathType: Prefix

autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 100
  targetCPUUtilizationPercentage: 70
  targetMemoryUtilizationPercentage: 80

resources:
  limits:
    cpu: 2000m
    memory: 4Gi
  requests:
    cpu: 500m
    memory: 1Gi

# Citus Data Configuration
database:
  coordinator:
    host: citus-coordinator
    port: 5432
    database: payment_db
  workers:
    - host: citus-worker-1
      port: 5432
    - host: citus-worker-2
      port: 5432

# Vault Integration
vault:
  enabled: true
  address: "http://vault:8200"
  role: "payment-service"
  secretPath: "payment-service/config"

# Service Mesh Configuration
istio:
  enabled: true
  mtls:
    mode: STRICT
  destinationRule:
    trafficPolicy:
      connectionPool:
        tcp:
          maxConnections: 100
        http:
          http1MaxPendingRequests: 50
          maxRequestsPerConnection: 10
```

### **Kubernetes Deployment Manifests**
```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: payment-service
  namespace: trix-payment
  labels:
    app: payment-service
    version: v2.0.0
spec:
  replicas: 3
  selector:
    matchLabels:
      app: payment-service
  template:
    metadata:
      labels:
        app: payment-service
        version: v2.0.0
      annotations:
        sidecar.istio.io/inject: "true"
        vault.hashicorp.com/agent-inject: "true"
        vault.hashicorp.com/role: "payment-service"
        vault.hashicorp.com/agent-inject-secret-config: "payment-service/config"
    spec:
      serviceAccountName: payment-service
      containers:
      - name: payment-service
        image: trix/payment-service:2.0.0
        ports:
        - containerPort: 8020
          name: http
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: SERVICE_NAME
          value: "payment-service"
        - name: VAULT_ADDR
          value: "http://vault:8200"
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /health/live
            port: 8020
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 8020
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: vault-secrets
          mountPath: /vault/secrets
          readOnly: true
      volumes:
      - name: vault-secrets
        emptyDir: {}
```

## 🔧 **Configuration Management**

### **Environment-Specific Configuration**
```python
# app/core/config/environments.py
class ProductionSettings(EnterpriseSettings):
    """Production environment configuration."""

    # Database Configuration
    DATABASE_URL: str = Field(default="")  # From Vault
    CITUS_COORDINATOR_URL: str = Field(default="")  # From Vault
    CITUS_WORKER_URLS: List[str] = Field(default_factory=list)  # From Vault

    # Performance Settings
    MAX_CONNECTIONS: int = Field(default=200)
    CONNECTION_POOL_SIZE: int = Field(default=50)
    WORKER_PROCESSES: int = Field(default=8)

    # Security Settings
    ENCRYPTION_ALGORITHM: str = Field(default="AES-256-GCM")
    TOKEN_EXPIRY_HOURS: int = Field(default=1)

    # Monitoring
    LOG_LEVEL: str = Field(default="INFO")
    METRICS_ENABLED: bool = Field(default=True)
    TRACING_ENABLED: bool = Field(default=True)

class StagingSettings(EnterpriseSettings):
    """Staging environment configuration."""

    # Reduced resource limits for staging
    MAX_CONNECTIONS: int = Field(default=50)
    CONNECTION_POOL_SIZE: int = Field(default=10)
    WORKER_PROCESSES: int = Field(default=2)

    # Enhanced logging for debugging
    LOG_LEVEL: str = Field(default="DEBUG")

class DevelopmentSettings(EnterpriseSettings):
    """Development environment configuration."""

    # Local development settings
    DATABASE_URL: str = Field(default="postgresql://localhost:5432/payment_dev")
    REDIS_URL: str = Field(default="redis://localhost:6379/0")

    # Disabled security features for development
    VAULT_ENABLED: bool = Field(default=False)
    ENCRYPTION_ENABLED: bool = Field(default=False)

    # Enhanced debugging
    LOG_LEVEL: str = Field(default="DEBUG")
    DEBUG: bool = Field(default=True)
```

## ✅ **Testing & Quality Assurance**

### **Comprehensive Test Strategy**
```python
# tests/integration/test_payment_processing.py
class TestPaymentProcessingIntegration:
    """Integration tests for payment processing workflows."""

    @pytest.mark.asyncio
    async def test_end_to_end_payment_flow(self):
        """Test complete payment flow from initiation to completion."""

        # Setup test data
        payment_request = PaymentRequest(
            tenant_id=uuid4(),
            user_id=uuid4(),
            amount=Decimal("100.00"),
            currency="BRL",
            payment_method="credit_card",
            card_token="test_token_123"
        )

        # Process payment
        result = await self.payment_service.process_payment(payment_request)

        # Verify payment was created
        assert result.success
        assert result.payment_id is not None

        # Verify payment status
        payment = await self.payment_repository.get_by_id(result.payment_id)
        assert payment.status == PaymentStatus.COMPLETED

        # Verify events were published
        events = await self.event_store.get_events(result.payment_id)
        assert len(events) >= 2  # PaymentInitiated and PaymentCompleted

    @pytest.mark.asyncio
    async def test_fraud_detection_integration(self):
        """Test fraud detection integration in payment flow."""

        # Create high-risk payment request
        high_risk_request = PaymentRequest(
            tenant_id=uuid4(),
            user_id=uuid4(),
            amount=Decimal("10000.00"),  # High amount
            currency="BRL",
            payment_method="credit_card",
            ip_address="***********",  # Suspicious IP
            device_fingerprint="suspicious_device"
        )

        # Process payment
        result = await self.payment_service.process_payment(high_risk_request)

        # Verify fraud detection triggered
        assert not result.success
        assert "fraud" in result.error_message.lower()

        # Verify fraud event was logged
        fraud_events = await self.fraud_event_store.get_events_by_payment(result.payment_id)
        assert len(fraud_events) > 0

# tests/performance/test_payment_load.py
class TestPaymentPerformance:
    """Performance tests for payment processing."""

    @pytest.mark.performance
    async def test_payment_throughput(self):
        """Test payment processing throughput under load."""

        # Create 1000 concurrent payment requests
        payment_requests = [
            self.create_test_payment_request()
            for _ in range(1000)
        ]

        start_time = time.time()

        # Process payments concurrently
        tasks = [
            self.payment_service.process_payment(request)
            for request in payment_requests
        ]

        results = await asyncio.gather(*tasks, return_exceptions=True)

        end_time = time.time()
        processing_time = end_time - start_time

        # Verify performance metrics
        successful_payments = len([r for r in results if isinstance(r, PaymentResult) and r.success])
        throughput = successful_payments / processing_time

        assert throughput >= 100  # At least 100 payments/second
        assert successful_payments >= 950  # At least 95% success rate
```

---

## 🚀 **Próximos Passos**

### **Implementação Imediata (Sprint Atual)**
1. **🔄 Configurar Citus Data**: Setup de sharding PostgreSQL
2. **🔄 Integrar HashiCorp Vault**: Migração de secrets
3. **🔄 Implementar Service Mesh**: Istio com mTLS
4. **🔄 Deploy Kubernetes**: Helm charts e manifests
5. **🔄 Configurar Observabilidade**: Prometheus + Grafana + Jaeger

### **Roadmap de Longo Prazo**
- **Q1 2025**: Event-driven architecture completa
- **Q2 2025**: ML-based fraud detection
- **Q3 2025**: Multi-region deployment
- **Q4 2025**: Advanced analytics e BI

**Status:** 🔄 **PRONTO PARA IMPLEMENTAÇÃO ENTERPRISE**
