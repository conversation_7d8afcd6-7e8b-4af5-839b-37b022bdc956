"""
Configurações do Tenant Service com integração às configurações compartilhadas.

MIGRAÇÃO CONCLUÍDA: Este arquivo agora estende as configurações da shared_lib
e adiciona apenas configurações específicas do Tenant Service.
"""

from microservices.core.shared_lib.config.vault_config import VaultBaseSettings
from microservices.core.shared_lib.config.kafka_config import KafkaSettings
from pydantic import Field
from typing import List, Dict, Any


class TenantServiceSettings(VaultBaseSettings):
    """
    Configurações do Tenant Service com integração às configurações compartilhadas.

    Herda configurações comuns de:
    - VaultBaseSettings: Configurações do HashiCorp Vault
    - Configurações de ambiente (DEBUG, LOG_LEVEL, etc.)
    """

    # ===== CONFIGURAÇÕES ESPECÍFICAS DO TENANT SERVICE =====

    # Service Identity
    SERVICE_NAME: str = Field(default="tenant-service", env="SERVICE_NAME")
    SERVICE_PORT: int = Field(default=8003, env="SERVICE_PORT")

    # Database - Citus Data Cluster (específico do Tenant)
    DATABASE_URL: str = Field(
        default="postgresql+asyncpg://tenants_user:TenantsSecure2024!#$@citus-coordinator:5432/tenants_db",
        env="TENANTS_DATABASE_URL"
    )
    CITUS_COORDINATOR_URL: str = Field(
        default="postgresql+asyncpg://tenant_user:${TENANT_DB_PASSWORD}@trix-citus-coordinator:5432/tenant_db",
        env="CITUS_COORDINATOR_URL"
    )
    CITUS_WORKER_URLS: List[str] = Field(
        default=[
            "postgresql+asyncpg://tenant_user:${TENANT_DB_PASSWORD}@trix-citus-worker-1:5432/tenant_db",
            "postgresql+asyncpg://tenant_user:${TENANT_DB_PASSWORD}@trix-citus-worker-2:5432/tenant_db",
            "postgresql+asyncpg://tenant_user:${TENANT_DB_PASSWORD}@trix-citus-worker-3:5432/tenant_db"
        ],
        env="CITUS_WORKER_URLS"
    )
    CITUS_SHARD_COUNT: int = Field(default=32, env="CITUS_SHARD_COUNT")
    CITUS_REPLICATION_FACTOR: int = Field(default=2, env="CITUS_REPLICATION_FACTOR")

    # Database Connection Pool (específico do Tenant)
    DATABASE_POOL_SIZE: int = Field(default=20, env="DATABASE_POOL_SIZE")
    DATABASE_MAX_OVERFLOW: int = Field(default=30, env="DATABASE_MAX_OVERFLOW")
    DATABASE_POOL_TIMEOUT: int = Field(default=30, env="DATABASE_POOL_TIMEOUT")
    DATABASE_POOL_RECYCLE: int = Field(default=3600, env="DATABASE_POOL_RECYCLE")

    # Redis Cluster (específico do Tenant)
    REDIS_CLUSTER_URLS: List[str] = Field(
        default=[
            "redis://:${REDIS_PASSWORD}@trix-redis-cluster-1:7000",
            "redis://:${REDIS_PASSWORD}@trix-redis-cluster-2:7001",
            "redis://:${REDIS_PASSWORD}@trix-redis-cluster-3:7002"
        ],
        env="REDIS_CLUSTER_URLS"
    )

    # RabbitMQ (específico do Tenant)
    RABBITMQ_URL: str = Field(
        default="amqp://tenant_user:${RABBITMQ_PASSWORD}@trix-rabbitmq:5672/",
        env="RABBITMQ_URL"
    )

    # Service Mesh URLs (específico do Tenant Service)
    AUTH_SERVICE_URL: str = Field(
        default="http://trix-core-auth.trix-system.svc.cluster.local:8001",
        env="AUTH_SERVICE_URL"
    )
    USER_SERVICE_URL: str = Field(
        default="http://trix-core-user.trix-system.svc.cluster.local:8002",
        env="USER_SERVICE_URL"
    )
    I18N_SERVICE_URL: str = Field(
        default="http://trix-core-i18n.trix-system.svc.cluster.local:8018",
        env="I18N_SERVICE_URL"
    )
    NOTIFICATION_SERVICE_URL: str = Field(
        default="http://trix-core-notification.trix-system.svc.cluster.local:8019",
        env="NOTIFICATION_SERVICE_URL"
    )
    COMMERCE_SERVICE_URL: str = Field(
        default="http://trix-core-commerce.trix-system.svc.cluster.local:8020",
        env="COMMERCE_SERVICE_URL"
    )
    CDN_SERVICE_URL: str = Field(
        default="http://trix-core-cdn.trix-system.svc.cluster.local:8021",
        env="CDN_SERVICE_URL"
    )

    # Observability (específico do Tenant)
    JAEGER_AGENT_HOST: str = Field(
        default="trix-jaeger-agent.trix-system.svc.cluster.local",
        env="JAEGER_AGENT_HOST"
    )
    JAEGER_AGENT_PORT: int = Field(default=6831, env="JAEGER_AGENT_PORT")
    PROMETHEUS_GATEWAY: str = Field(
        default="trix-prometheus-pushgateway.trix-system.svc.cluster.local:9091",
        env="PROMETHEUS_GATEWAY"
    )

    # Security (específico do Tenant)
    JWT_SECRET_KEY: str = Field(default="tenant_jwt_secret_key_development", env="JWT_SECRET_KEY")  # From Vault
    ENCRYPTION_KEY: str = Field(default="tenant_encryption_key_development_32chars", env="ENCRYPTION_KEY")  # From Vault

    # Gamification (específico do Tenant Service)
    GAMIFY_SECRET_KEY: str = Field(default="gamify_secret_key_development", env="GAMIFY_SECRET_KEY")  # From Vault
    GAMIFY_QR_ENCRYPTION_KEY: str = Field(default="gamify_qr_encryption_key_development", env="GAMIFY_QR_ENCRYPTION_KEY")  # From Vault
    GAMIFICATION_ENABLED: bool = Field(default=True, env="GAMIFICATION_ENABLED")

    # Performance (específico do Tenant)
    MAX_CONNECTIONS_PER_POOL: int = Field(default=100, env="MAX_CONNECTIONS_PER_POOL")
    CACHE_TTL_SECONDS: int = Field(default=300, env="CACHE_TTL_SECONDS")
    RATE_LIMIT_PER_MINUTE: int = Field(default=1000, env="RATE_LIMIT_PER_MINUTE")

    # Geo-Distribution (específico do Tenant)
    REGION: str = Field(default="us-east-1", env="REGION")
    AVAILABILITY_ZONE: str = Field(default="us-east-1a", env="AVAILABILITY_ZONE")

    # ===== CONFIGURAÇÕES COMPARTILHADAS (via shared_lib) =====

    @property
    def kafka_settings(self) -> KafkaSettings:
        """Retorna configurações do Kafka da shared_lib."""
        return KafkaSettings()

    def get_tenant_vault_paths(self) -> dict:
        """Retorna caminhos específicos do Vault para o Tenant Service."""
        return {
            "service": self.get_vault_path("service"),
            "database": self.get_vault_path("database"),
            "redis": f"redis/{self.VAULT_ENVIRONMENT}/tenant-service",
            "rabbitmq": f"messaging/{self.VAULT_ENVIRONMENT}/tenant-service",
            "gamification": f"gamification/{self.VAULT_ENVIRONMENT}/tenant-service",
            "citus": f"citus/{self.VAULT_ENVIRONMENT}/tenant-service"
        }

    class Config:
        env_file = ".env"
        case_sensitive = True


# Instância global das configurações
settings = TenantServiceSettings()
