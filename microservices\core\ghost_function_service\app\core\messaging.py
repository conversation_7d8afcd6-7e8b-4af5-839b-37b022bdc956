"""
Ghost Function Service - Messaging Configuration

Configuração e gerenciamento de conexões com sistemas de mensageria.
Suporta Kafka e RabbitMQ para diferentes padrões de comunicação.
Utiliza a shared_lib para configurações centralizadas.

Author: Trix Platform Team
"""

import asyncio
from typing import Optional

from app.core.config import get_settings
from microservices.core.shared_lib.config import (
    get_logger
)
from microservices.core.shared_lib.config.infrastructure.messaging.kafka_client import KafkaProducer
from microservices.core.shared_lib.config.infrastructure.messaging.rabbitmq_client import RabbitMQClient

# Configurações
settings = get_settings()

# Logger estruturado (usando shared_lib)
logger = get_logger("ghost-function-service")

# Instâncias globais dos clientes
kafka_client: Optional[KafkaProducer] = None
rabbitmq_client: Optional[RabbitMQClient] = None


async def init_messaging() -> None:
    """
    Inicializa as conexões com sistemas de mensageria usando shared_lib.

    Esta função deve ser chamada na inicialização da aplicação.
    """
    global kafka_client, rabbitmq_client

    try:
        logger.info("📡 Inicializando sistemas de mensageria...")

        # Inicializar Kafka Producer
        kafka_client = KafkaProducer("ghost-function-service")
        await kafka_client.initialize()

        # Inicializar RabbitMQ Client
        rabbitmq_client = RabbitMQClient("ghost-function-service")
        await rabbitmq_client.initialize()

        logger.info("✅ Sistemas de mensageria inicializados")

    except Exception as e:
        logger.error(f"❌ Erro ao inicializar messaging: {e}")
        raise


async def get_kafka_client() -> KafkaProducer:
    """
    Obtém o cliente Kafka inicializado.

    Returns:
        KafkaProducer: Cliente Kafka
    """
    if kafka_client is None:
        raise RuntimeError("Kafka client não inicializado. Chame init_messaging() primeiro.")
    return kafka_client


async def get_rabbitmq_client() -> RabbitMQClient:
    """
    Obtém o cliente RabbitMQ inicializado.

    Returns:
        RabbitMQClient: Cliente RabbitMQ
    """
    if rabbitmq_client is None:
        raise RuntimeError("RabbitMQ client não inicializado. Chame init_messaging() primeiro.")
    return rabbitmq_client


async def close_messaging() -> None:
    """
    Fecha as conexões com sistemas de mensageria usando shared_lib.

    Esta função deve ser chamada na finalização da aplicação.
    """
    global kafka_client, rabbitmq_client

    logger.info("🔄 Fechando conexões de mensageria...")

    try:
        # Fechar Kafka Client
        if kafka_client:
            await kafka_client.close()
            kafka_client = None

        # Fechar RabbitMQ Client
        if rabbitmq_client:
            await rabbitmq_client.close()
            rabbitmq_client = None

        logger.info("✅ Conexões de mensageria fechadas")

    except Exception as e:
        logger.error(f"❌ Erro ao fechar messaging: {e}")


class MessagingHealthCheck:
    """
    Classe para verificar a saúde dos sistemas de mensageria usando shared_lib.
    """

    @staticmethod
    async def check_kafka() -> bool:
        """
        Verifica se o Kafka está funcionando.

        Returns:
            bool: True se Kafka estiver OK
        """
        try:
            if not kafka_client:
                return False

            return await kafka_client.health_check()

        except Exception as e:
            logger.error(f"Kafka health check falhou: {e}")
            return False

    @staticmethod
    async def check_rabbitmq() -> bool:
        """
        Verifica se o RabbitMQ está funcionando.

        Returns:
            bool: True se RabbitMQ estiver OK
        """
        try:
            if not rabbitmq_client:
                return False

            return await rabbitmq_client.health_check()

        except Exception as e:
            logger.error(f"RabbitMQ health check falhou: {e}")
            return False

    @staticmethod
    async def get_status() -> dict:
        """
        Retorna o status dos sistemas de mensageria.

        Returns:
            dict: Status dos sistemas
        """
        kafka_ok = await MessagingHealthCheck.check_kafka()
        rabbitmq_ok = await MessagingHealthCheck.check_rabbitmq()

        return {
            "kafka": {
                "status": "UP" if kafka_ok else "DOWN",
                "connected": kafka_ok,
                "client_ready": kafka_client is not None,
            },
            "rabbitmq": {
                "status": "UP" if rabbitmq_ok else "DOWN",
                "connected": rabbitmq_ok,
                "client_ready": rabbitmq_client is not None,
            },
            "overall_status": "UP" if (kafka_ok and rabbitmq_ok) else "DEGRADED"
        }


# Instância global do health check
messaging_health = MessagingHealthCheck()
