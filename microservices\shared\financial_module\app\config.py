"""Financial Module Configuration v3.0.0 - Fully Migrated to Shared Lib
=====================================================================

MIGRAÇÃO COMPLETA - USANDO APENAS SHARED_LIB

This configuration now uses shared_lib for ALL common configurations
and maintains only financial-module-specific settings.
"""

import os
from typing import List, Optional, Dict, Any
from pydantic import Field, validator
from pydantic_settings import BaseSettings

# Import ALL shared configurations from shared_lib
from microservices.core.shared_lib.config import (
    FinancialSettings,
    KafkaSettings,
    RedisSettings,
    RabbitMQSettings,
    VaultBaseSettings,
    ObservabilitySettings,
    SecuritySettings,
    get_financial_settings,
    get_kafka_settings,
    get_redis_settings,
    get_rabbitmq_settings,
    get_security_settings,
    get_observability_settings,
    get_vault_config
)


class CitusConfig(BaseSettings):
    """Citus Data configuration for horizontal sharding."""

    enabled: bool = Field(default=True, env="CITUS_ENABLED")
    coordinator_url: str = Field(
        default="postgresql+asyncpg://financial_user:password@trix-citus-coordinator:5432/financial_db",
        env="CITUS_COORDINATOR_URL"
    )
    worker_urls: str = Field(
        default="postgresql+asyncpg://financial_user:password@trix-citus-worker-1:5432/financial_db,postgresql+asyncpg://financial_user:password@trix-citus-worker-2:5432/financial_db",
        env="CITUS_WORKER_URLS"
    )
    shard_count: int = Field(default=32, env="CITUS_SHARD_COUNT")
    replication_factor: int = Field(default=2, env="CITUS_REPLICATION_FACTOR")
    connection_pool_size: int = Field(default=20, env="CITUS_POOL_SIZE")
    max_overflow: int = Field(default=30, env="CITUS_MAX_OVERFLOW")
    pool_timeout: int = Field(default=30, env="CITUS_POOL_TIMEOUT")
    pool_recycle: int = Field(default=3600, env="CITUS_POOL_RECYCLE")
    echo: bool = Field(default=False, env="CITUS_ECHO")

    @property
    def worker_urls_list(self) -> List[str]:
        """Get worker URLs as list."""
        if isinstance(self.worker_urls, str):
            return [url.strip() for url in self.worker_urls.split(",")]
        return self.worker_urls

    class Config:
        env_prefix = "CITUS_"


# All messaging, observability and security configurations
# are now imported from shared_lib - no duplication needed


class ServiceIntegrationConfig(BaseSettings):
    """Configuration for integration with other microservices."""

    # Service discovery
    service_discovery_enabled: bool = Field(default=True, env="SERVICE_DISCOVERY_ENABLED")
    consul_url: str = Field(default="http://trix-consul:8500", env="CONSUL_URL")

    # Service URLs (with service mesh internal routing)
    auth_service_url: str = Field(default="http://auth-service.trix-system.svc.cluster.local", env="AUTH_SERVICE_URL")
    user_service_url: str = Field(default="http://user-service.trix-system.svc.cluster.local", env="USER_SERVICE_URL")
    tenant_service_url: str = Field(default="http://tenant-service.trix-system.svc.cluster.local", env="TENANT_SERVICE_URL")
    supplier_service_url: str = Field(default="http://supplier-service.trix-system.svc.cluster.local", env="SUPPLIER_SERVICE_URL")
    payment_service_url: str = Field(default="http://payment-service.trix-system.svc.cluster.local", env="PAYMENT_SERVICE_URL")
    notification_service_url: str = Field(default="http://notification-service.trix-system.svc.cluster.local", env="NOTIFICATION_SERVICE_URL")
    media_service_url: str = Field(default="http://media-service.trix-system.svc.cluster.local", env="MEDIA_SERVICE_URL")
    commerce_service_url: str = Field(default="http://commerce-service.trix-system.svc.cluster.local", env="COMMERCE_SERVICE_URL")
    crm_service_url: str = Field(default="http://crm-service.trix-system.svc.cluster.local", env="CRM_SERVICE_URL")
    hr_service_url: str = Field(default="http://hr-service.trix-system.svc.cluster.local", env="HR_SERVICE_URL")
    i18n_service_url: str = Field(default="http://i18n-service.trix-system.svc.cluster.local", env="I18N_SERVICE_URL")

    # Circuit breaker configuration
    circuit_breaker_enabled: bool = Field(default=True, env="CIRCUIT_BREAKER_ENABLED")
    circuit_breaker_failure_threshold: int = Field(default=5, env="CIRCUIT_BREAKER_FAILURE_THRESHOLD")
    circuit_breaker_recovery_timeout: int = Field(default=60, env="CIRCUIT_BREAKER_RECOVERY_TIMEOUT")
    circuit_breaker_expected_exception: str = Field(default="requests.RequestException", env="CIRCUIT_BREAKER_EXPECTED_EXCEPTION")

    # Retry configuration
    retry_enabled: bool = Field(default=True, env="RETRY_ENABLED")
    retry_max_attempts: int = Field(default=3, env="RETRY_MAX_ATTEMPTS")
    retry_backoff_factor: float = Field(default=0.5, env="RETRY_BACKOFF_FACTOR")
    retry_max_delay: int = Field(default=60, env="RETRY_MAX_DELAY")

    # Timeout configuration
    default_timeout: int = Field(default=30, env="DEFAULT_TIMEOUT")
    long_running_timeout: int = Field(default=300, env="LONG_RUNNING_TIMEOUT")

    class Config:
        env_prefix = "SERVICE_INTEGRATION_"


class PerformanceConfig(BaseSettings):
    """Performance and optimization configuration."""

    # Connection pooling
    http_pool_connections: int = Field(default=100, env="HTTP_POOL_CONNECTIONS")
    http_pool_maxsize: int = Field(default=100, env="HTTP_POOL_MAXSIZE")
    http_max_retries: int = Field(default=3, env="HTTP_MAX_RETRIES")

    # Caching
    cache_enabled: bool = Field(default=True, env="CACHE_ENABLED")
    cache_default_ttl: int = Field(default=3600, env="CACHE_DEFAULT_TTL")  # 1 hour
    cache_max_size: int = Field(default=10000, env="CACHE_MAX_SIZE")

    # Pagination
    default_page_size: int = Field(default=20, env="DEFAULT_PAGE_SIZE")
    max_page_size: int = Field(default=1000, env="MAX_PAGE_SIZE")

    # Bulk operations
    bulk_operation_max_size: int = Field(default=1000, env="BULK_OPERATION_MAX_SIZE")
    bulk_operation_batch_size: int = Field(default=100, env="BULK_OPERATION_BATCH_SIZE")

    # File uploads
    max_file_size: int = Field(default=100 * 1024 * 1024, env="MAX_FILE_SIZE")  # 100MB
    allowed_file_types: str = Field(
        default="image/jpeg,image/png,image/gif,image/webp,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,text/csv,application/json,application/xml",
        env="ALLOWED_FILE_TYPES"
    )

    # Background tasks
    background_task_queue_size: int = Field(default=1000, env="BACKGROUND_TASK_QUEUE_SIZE")
    background_task_workers: int = Field(default=4, env="BACKGROUND_TASK_WORKERS")

    class Config:
        env_prefix = "PERFORMANCE_"


# FinancialConfig is now imported from shared_lib
# No need to duplicate it here


class EnterpriseConfig(BaseSettings):
    """
    Simplified enterprise configuration for Financial Module.
    Uses shared_lib for all common configurations.
    """

    # Application metadata
    app_name: str = Field(default="Financial Service", env="APP_NAME")
    app_version: str = Field(default="3.0.0", env="APP_VERSION")
    service_name: str = Field(default="financial-service", env="SERVICE_NAME")
    environment: str = Field(default="production", env="ENVIRONMENT")
    debug: bool = Field(default=False, env="DEBUG")

    # Server configuration
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=8013, env="PORT")
    workers: int = Field(default=4, env="WORKERS")

    # Health check configuration
    health_check_enabled: bool = Field(default=True, env="HEALTH_CHECK_ENABLED")
    health_check_interval: int = Field(default=30, env="HEALTH_CHECK_INTERVAL")

    # Financial-module-specific configurations only
    citus: CitusConfig = CitusConfig()
    integrations: ServiceIntegrationConfig = ServiceIntegrationConfig()
    performance: PerformanceConfig = PerformanceConfig()

    # All other configurations come from shared_lib
    @property
    def vault(self):
        return get_vault_config()
    
    @property
    def kafka(self):
        return get_kafka_settings()
    
    @property
    def rabbitmq(self):
        return get_rabbitmq_settings()
    
    @property
    def redis(self):
        return get_redis_settings()
    
    @property
    def observability(self):
        return get_observability_settings()
    
    @property
    def security(self):
        return get_security_settings()
    
    @property
    def financial(self):
        return get_financial_settings()

    @validator('environment')
    def validate_environment(cls, v):
        allowed_environments = ['development', 'staging', 'production']
        if v not in allowed_environments:
            raise ValueError(f'Environment must be one of {allowed_environments}')
        return v

    @validator('debug')
    def validate_debug_in_production(cls, v, values):
        if values.get('environment') == 'production' and v:
            raise ValueError('Debug mode cannot be enabled in production')
        return v

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# Global configuration instance
enterprise_config = EnterpriseConfig()


def get_enterprise_config() -> EnterpriseConfig:
    """Get enterprise configuration instance."""
    return enterprise_config


def get_citus_config() -> CitusConfig:
    """Get Citus Data configuration."""
    return enterprise_config.citus


def get_messaging_config() -> Dict[str, Any]:
    """Get messaging configuration."""
    return {
        "kafka": enterprise_config.kafka.dict(),
        "rabbitmq": enterprise_config.rabbitmq.dict(),
        "redis": enterprise_config.redis.dict()
    }


def get_performance_config() -> PerformanceConfig:
    """Get performance configuration."""
    return enterprise_config.performance


def get_service_integration_config() -> ServiceIntegrationConfig:
    """Get service integration configuration."""
    return enterprise_config.integrations


def is_feature_enabled(feature_name: str) -> bool:
    """Check if a feature flag is enabled."""
    return enterprise_config.financial.is_feature_enabled(feature_name)


# Financial-module-specific utility functions
def get_database_url() -> str:
    """Get the primary database URL (Citus coordinator)."""
    return enterprise_config.citus.coordinator_url


# Legacy compatibility removed - use EnterpriseConfig and shared_lib
