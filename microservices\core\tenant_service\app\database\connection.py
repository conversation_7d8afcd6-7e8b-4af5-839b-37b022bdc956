"""
Database connection management for Tenant Service.
"""

import asyncio
from contextlib import asynccontextmanager
from typing import Async<PERSON><PERSON>ator, Optional
from sqlalchemy.ext.asyncio import AsyncSession, AsyncEngine, create_async_engine, async_sessionmaker
from sqlalchemy.pool import QueuePool
from sqlalchemy.orm import declarative_base

from app.core.config.settings import settings
from microservices.core.shared_lib.config.infrastructure.observability.logging import get_logger

logger = get_logger(__name__)

# Create declarative base
Base = declarative_base()


class DatabaseConnectionManager:
    """
    Manages database connections for the Tenant Service.
    """
    
    def __init__(self):
        self._engine: Optional[AsyncEngine] = None
        self._session_factory: Optional[async_sessionmaker] = None
        self._initialized = False
    
    async def initialize(self):
        """Initialize database connections."""
        if self._initialized:
            return
        
        try:
            # Create async engine
            self._engine = create_async_engine(
                settings.DATABASE_URL,
                poolclass=QueuePool,
                pool_size=10,
                max_overflow=20,
                pool_timeout=30,
                pool_recycle=3600,
                pool_pre_ping=True,
                echo=settings.DEBUG,
                connect_args={}
            )
            
            # Create session factory
            self._session_factory = async_sessionmaker(
                bind=self._engine,
                class_=AsyncSession,
                expire_on_commit=False,
                autoflush=False,
                autocommit=False
            )
            
            self._initialized = True
            logger.info("Database connection manager initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize database connection manager: {e}")
            raise
    
    async def close(self):
        """Close database connections."""
        if self._engine:
            await self._engine.dispose()
            self._engine = None
            self._session_factory = None
            self._initialized = False
            logger.info("Database connections closed")
    
    async def get_session(self) -> AsyncSession:
        """Get a new database session."""
        if not self._initialized:
            await self.initialize()
        
        if not self._session_factory:
            raise RuntimeError("Database connection manager not initialized")
        
        return self._session_factory()
    
    @asynccontextmanager
    async def get_session_context(self) -> AsyncGenerator[AsyncSession, None]:
        """Get database session with automatic cleanup."""
        session = await self.get_session()
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()
    
    async def get_tenant_session(self, tenant_id: str) -> AsyncSession:
        """Get session optimized for specific tenant (routes to correct shard)."""
        # With Citus Data, all sessions go through coordinator
        # The coordinator automatically routes queries to correct shards
        return await self.get_session()
    
    @asynccontextmanager
    async def get_tenant_session_context(self, tenant_id: str) -> AsyncGenerator[AsyncSession, None]:
        """Get tenant session with automatic cleanup."""
        session = await self.get_tenant_session(tenant_id)
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()
    
    @property
    def engine(self) -> AsyncEngine:
        """Get the database engine."""
        if not self._engine:
            raise RuntimeError("Database connection manager not initialized")
        return self._engine


# Global connection manager instance
connection_manager = DatabaseConnectionManager()


async def get_connection_manager() -> DatabaseConnectionManager:
    """Get the connection manager instance."""
    if not connection_manager._initialized:
        await connection_manager.initialize()
    return connection_manager


async def get_db() -> AsyncSession:
    """Get database session."""
    return await connection_manager.get_session()


@asynccontextmanager
async def get_db_session_context() -> AsyncGenerator[AsyncSession, None]:
    """Get database session with automatic cleanup."""
    async with connection_manager.get_session_context() as session:
        yield session


async def get_tenant_db_session(tenant_id: str) -> AsyncSession:
    """Get database session for specific tenant."""
    return await connection_manager.get_tenant_session(tenant_id)


@asynccontextmanager
async def get_tenant_db_session_context(tenant_id: str) -> AsyncGenerator[AsyncSession, None]:
    """Get tenant database session with automatic cleanup."""
    async with connection_manager.get_tenant_session_context(tenant_id) as session:
        yield session


async def initialize_connections():
    """Initialize database connections."""
    await connection_manager.initialize()


async def close_connections():
    """Close database connections."""
    await connection_manager.close()
