#!/usr/bin/env python3
"""
🌱 Trix Citus Seed Manager
Gerencia seeds distribuídos no cluster Citus por microserviço
"""

import asyncio
import asyncpg
import logging
import json
import uuid
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime
import bcrypt

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class SeedTask:
    """Tarefa de seed para um microserviço"""
    microservice: str
    coordinator: str
    database: str
    priority: int
    depends_on: List[str]

class CitusSeedManager:
    """Gerenciador de seeds distribuídos para Citus"""
    
    def __init__(self):
        self.coordinators_config = self._load_coordinators_config()
        self.seed_tasks = self._prepare_seed_tasks()
        self.executed_seeds = []
        self.failed_seeds = []
        
    def _load_coordinators_config(self) -> Dict[str, Dict[str, Any]]:
        """Carrega configuração dos coordenadores"""
        return {
            'core': {
                'host': 'trix-citus-core-coordinator',
                'port': 5432,
                'database': 'postgres'
            },
            'tenant': {
                'host': 'trix-citus-tenant-coordinator',
                'port': 5433,
                'database': 'postgres'
            },
            'shared': {
                'host': 'trix-citus-shared-coordinator',
                'port': 5434,
                'database': 'postgres'
            }
        }
    
    def _prepare_seed_tasks(self) -> List[SeedTask]:
        """Prepara tarefas de seed por microserviço"""
        return [
            # Core Services (Priority 1-5)
            SeedTask('auth_service', 'core', 'auth_db', 1, []),
            SeedTask('user_service', 'core', 'users_db', 2, ['auth_service']),
            SeedTask('tenant_service', 'core', 'tenants_db', 3, ['auth_service', 'user_service']),
            
            # Tenant Services (Priority 6-8)
            SeedTask('restaurant_module', 'tenant', 'restaurant_db', 6, ['tenant_service']),
            SeedTask('consultancy_module', 'tenant', 'consultancy_db', 7, ['tenant_service']),
            
            # Shared Services (Priority 9-11)
            SeedTask('hr_module', 'shared', 'hr_db', 9, ['tenant_service']),
            SeedTask('crm_module', 'shared', 'crm_db', 10, ['tenant_service']),
            SeedTask('email_module', 'shared', 'email_db', 11, ['tenant_service'])
        ]
    
    async def run_distributed_seeds(self):
        """Executa seeds distribuídos respeitando dependências"""
        logger.info("🌱 Iniciando seeds distribuídos no cluster Citus...")
        
        try:
            # 1. Verificar conectividade
            await self._check_coordinators_connectivity()
            
            # 2. Executar seeds em ordem de dependência
            await self._execute_seeds_by_priority()
            
            # 3. Verificar resultados
            await self._verify_seeds()
            
            # 4. Relatório final
            self._generate_seed_report()
            
            logger.info("✅ Seeds distribuídos executados com sucesso!")
            
        except Exception as e:
            logger.error(f"❌ Erro nos seeds distribuídos: {e}")
            raise
    
    async def _check_coordinators_connectivity(self):
        """Verifica conectividade com coordenadores"""
        logger.info("🔍 Verificando conectividade com coordenadores...")
        
        for coord_name, coord_config in self.coordinators_config.items():
            try:
                conn = await asyncpg.connect(
                    host=coord_config['host'],
                    port=coord_config['port'],
                    user='postgres',
                    password='TrixSuperSecure2024!',
                    database=coord_config['database']
                )
                await conn.execute('SELECT 1')
                await conn.close()
                logger.info(f"✅ Coordenador {coord_name} conectado")
            except Exception as e:
                logger.error(f"❌ Falha ao conectar coordenador {coord_name}: {e}")
                raise
    
    async def _execute_seeds_by_priority(self):
        """Executa seeds em ordem de prioridade"""
        logger.info("📊 Executando seeds por prioridade...")
        
        # Ordenar por prioridade
        sorted_tasks = sorted(self.seed_tasks, key=lambda x: x.priority)
        
        for task in sorted_tasks:
            # Verificar dependências
            if await self._check_dependencies(task):
                logger.info(f"🎯 Executando seed para {task.microservice}...")
                success = await self._execute_microservice_seed(task)
                
                if success:
                    self.executed_seeds.append(task.microservice)
                    logger.info(f"✅ Seed executado com sucesso: {task.microservice}")
                else:
                    self.failed_seeds.append(task.microservice)
                    logger.error(f"❌ Falha no seed: {task.microservice}")
            else:
                logger.warning(f"⚠️ Dependências não atendidas para {task.microservice}")
                self.failed_seeds.append(task.microservice)
    
    async def _check_dependencies(self, task: SeedTask) -> bool:
        """Verifica se dependências foram executadas"""
        for dependency in task.depends_on:
            if dependency not in self.executed_seeds:
                return False
        return True
    
    async def _execute_microservice_seed(self, task: SeedTask) -> bool:
        """Executa seed para um microserviço específico"""
        coord_config = self.coordinators_config[task.coordinator]
        
        try:
            conn = await asyncpg.connect(
                host=coord_config['host'],
                port=coord_config['port'],
                user='postgres',
                password='TrixSuperSecure2024!',
                database=task.database
            )
            
            # Executar seed específico do microserviço
            if task.microservice == 'auth_service':
                await self._seed_auth_service(conn)
            elif task.microservice == 'user_service':
                await self._seed_user_service(conn)
            elif task.microservice == 'tenant_service':
                await self._seed_tenant_service(conn)
            elif task.microservice == 'restaurant_module':
                await self._seed_restaurant_module(conn)
            elif task.microservice == 'consultancy_module':
                await self._seed_consultancy_module(conn)
            elif task.microservice == 'hr_module':
                await self._seed_hr_module(conn)
            elif task.microservice == 'crm_module':
                await self._seed_crm_module(conn)
            elif task.microservice == 'email_module':
                await self._seed_email_module(conn)
            
            await conn.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro no seed de {task.microservice}: {e}")
            return False
    
    async def _seed_auth_service(self, conn: asyncpg.Connection):
        """Seed para auth_service"""
        logger.info("🔐 Executando seed do auth_service...")
        
        # Verificar se usuários já existem
        existing_users = await conn.fetchval("SELECT COUNT(*) FROM users")
        if existing_users > 0:
            logger.info("ℹ️ Usuários já existem, pulando seed")
            return
        
        # Criar usuários padrão
        users_data = [
            {
                'id': str(uuid.uuid4()),
                'email': '<EMAIL>',
                'password_hash': self._hash_password('password'),
                'is_active': True
            },
            {
                'id': str(uuid.uuid4()),
                'email': '<EMAIL>',
                'password_hash': self._hash_password('password'),
                'is_active': True
            },
            {
                'id': str(uuid.uuid4()),
                'email': '<EMAIL>',
                'password_hash': self._hash_password('password'),
                'is_active': True
            }
        ]
        
        for user in users_data:
            await conn.execute(
                "INSERT INTO users (id, email, password_hash, is_active) VALUES ($1, $2, $3, $4)",
                user['id'], user['email'], user['password_hash'], user['is_active']
            )
        
        logger.info(f"✅ {len(users_data)} usuários criados no auth_service")
    
    async def _seed_user_service(self, conn: asyncpg.Connection):
        """Seed para user_service"""
        logger.info("👥 Executando seed do user_service...")
        
        # Verificar se perfis já existem
        existing_profiles = await conn.fetchval("SELECT COUNT(*) FROM user_profiles")
        if existing_profiles > 0:
            logger.info("ℹ️ Perfis já existem, pulando seed")
            return
        
        # Criar perfis de usuário (usando IDs fictícios para demonstração)
        profiles_data = [
            {
                'id': str(uuid.uuid4()),
                'user_id': str(uuid.uuid4()),
                'first_name': 'System',
                'last_name': 'Administrator',
                'phone': '+34 600 000 001'
            },
            {
                'id': str(uuid.uuid4()),
                'user_id': str(uuid.uuid4()),
                'first_name': 'Restaurant',
                'last_name': 'Owner',
                'phone': '+34 600 000 002'
            }
        ]
        
        for profile in profiles_data:
            await conn.execute(
                "INSERT INTO user_profiles (id, user_id, first_name, last_name, phone) VALUES ($1, $2, $3, $4, $5)",
                profile['id'], profile['user_id'], profile['first_name'], profile['last_name'], profile['phone']
            )
        
        logger.info(f"✅ {len(profiles_data)} perfis criados no user_service")
    
    async def _seed_tenant_service(self, conn: asyncpg.Connection):
        """Seed para tenant_service"""
        logger.info("🏢 Executando seed do tenant_service...")
        
        # Verificar se tenants já existem
        existing_tenants = await conn.fetchval("SELECT COUNT(*) FROM tenants")
        if existing_tenants > 0:
            logger.info("ℹ️ Tenants já existem, pulando seed")
            return
        
        # Criar tenants demo
        tenants_data = [
            {
                'id': str(uuid.uuid4()),
                'name': 'Test Restaurant',
                'domain': 'restaurant.trix.local',
                'is_active': True
            },
            {
                'id': str(uuid.uuid4()),
                'name': 'Test Consultancy',
                'domain': 'consultancy.trix.local',
                'is_active': True
            }
        ]
        
        for tenant in tenants_data:
            await conn.execute(
                "INSERT INTO tenants (id, name, domain, is_active) VALUES ($1, $2, $3, $4)",
                tenant['id'], tenant['name'], tenant['domain'], tenant['is_active']
            )
        
        logger.info(f"✅ {len(tenants_data)} tenants criados no tenant_service")
    
    async def _seed_restaurant_module(self, conn: asyncpg.Connection):
        """Seed para restaurant_module"""
        logger.info("🍽️ Executando seed do restaurant_module...")
        
        # Verificar se menus já existem
        existing_menus = await conn.fetchval("SELECT COUNT(*) FROM restaurant_menus")
        if existing_menus > 0:
            logger.info("ℹ️ Menus já existem, pulando seed")
            return
        
        # Criar itens de menu demo
        tenant_id = str(uuid.uuid4())  # ID fictício para demonstração
        menus_data = [
            {
                'id': str(uuid.uuid4()),
                'tenant_id': tenant_id,
                'name': 'Jamón Ibérico',
                'description': 'Jamón ibérico de bellota cortado a mano',
                'price': 18.50,
                'is_active': True
            },
            {
                'id': str(uuid.uuid4()),
                'tenant_id': tenant_id,
                'name': 'Paella Valenciana',
                'description': 'Paella tradicional valenciana con pollo y verduras',
                'price': 22.00,
                'is_active': True
            }
        ]
        
        for menu in menus_data:
            await conn.execute(
                "INSERT INTO restaurant_menus (id, tenant_id, name, description, price, is_active) VALUES ($1, $2, $3, $4, $5, $6)",
                menu['id'], menu['tenant_id'], menu['name'], menu['description'], menu['price'], menu['is_active']
            )
        
        logger.info(f"✅ {len(menus_data)} itens de menu criados no restaurant_module")
    
    async def _seed_consultancy_module(self, conn: asyncpg.Connection):
        """Seed para consultancy_module"""
        logger.info("💼 Executando seed do consultancy_module...")
        logger.info("ℹ️ Consultancy module seed - placeholder implementado")
    
    async def _seed_hr_module(self, conn: asyncpg.Connection):
        """Seed para hr_module"""
        logger.info("👔 Executando seed do hr_module...")
        logger.info("ℹ️ HR module seed - placeholder implementado")
    
    async def _seed_crm_module(self, conn: asyncpg.Connection):
        """Seed para crm_module"""
        logger.info("📊 Executando seed do crm_module...")
        logger.info("ℹ️ CRM module seed - placeholder implementado")
    
    async def _seed_email_module(self, conn: asyncpg.Connection):
        """Seed para email_module"""
        logger.info("📧 Executando seed do email_module...")
        logger.info("ℹ️ Email module seed - placeholder implementado")
    
    def _hash_password(self, password: str) -> str:
        """Gera hash bcrypt para senha"""
        salt = bcrypt.gensalt(rounds=12)
        hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
        return hashed.decode('utf-8')
    
    async def _verify_seeds(self):
        """Verifica resultados dos seeds"""
        logger.info("🔍 Verificando resultados dos seeds...")
        
        for task in self.seed_tasks:
            if task.microservice in self.executed_seeds:
                coord_config = self.coordinators_config[task.coordinator]
                
                try:
                    conn = await asyncpg.connect(
                        host=coord_config['host'],
                        port=coord_config['port'],
                        user='postgres',
                        password='TrixSuperSecure2024!',
                        database=task.database
                    )
                    
                    # Verificar dados criados (exemplo genérico)
                    tables = await conn.fetch(
                        "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'"
                    )
                    
                    logger.info(f"✅ {task.microservice}: {len(tables)} tabelas encontradas")
                    
                    await conn.close()
                    
                except Exception as e:
                    logger.error(f"❌ Erro na verificação de {task.microservice}: {e}")
    
    def _generate_seed_report(self):
        """Gera relatório final dos seeds"""
        total_tasks = len(self.seed_tasks)
        executed_count = len(self.executed_seeds)
        failed_count = len(self.failed_seeds)
        success_rate = (executed_count / total_tasks) * 100 if total_tasks > 0 else 0
        
        logger.info("=" * 60)
        logger.info("📊 RELATÓRIO DE SEEDS DISTRIBUÍDOS")
        logger.info("=" * 60)
        logger.info(f"🎯 Total de microserviços: {total_tasks}")
        logger.info(f"✅ Seeds executados: {executed_count}")
        logger.info(f"❌ Seeds falharam: {failed_count}")
        logger.info(f"📈 Taxa de sucesso: {success_rate:.1f}%")
        logger.info("=" * 60)
        
        if self.executed_seeds:
            logger.info("✅ Seeds executados com sucesso:")
            for service in self.executed_seeds:
                logger.info(f"   - {service}")
        
        if self.failed_seeds:
            logger.info("❌ Seeds que falharam:")
            for service in self.failed_seeds:
                logger.info(f"   - {service}")

if __name__ == "__main__":
    async def main():
        manager = CitusSeedManager()
        await manager.run_distributed_seeds()
    
    asyncio.run(main())
