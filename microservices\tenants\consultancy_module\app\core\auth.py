"""
Consultancy Module - Authentication Integration
==============================================
Authentication middleware and dependencies for consultancy operations.
Follows the pattern established by user_service for consistency.
"""

import logging
from typing import Dict, Any, Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

# Import shared auth utilities
try:
    from microservices.core.shared_lib.config.infrastructure.security.auth import (
        AuthMiddleware,
        get_current_user_from_token
    )
    SHARED_LIB_AVAILABLE = True
except ImportError:
    SHARED_LIB_AVAILABLE = False

from .integrations.auth_client import get_consultancy_auth

logger = logging.getLogger(__name__)
security = HTTPBearer()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> Dict[str, Any]:
    """
    Get current authenticated user context for consultancy operations.

    Args:
        credentials: HTTP authorization credentials containing the JWT token

    Returns:
        User context dictionary with consultancy-specific validation

    Raises:
        HTTPException: If token is invalid or user lacks consultancy access
    """
    try:
        token = credentials.credentials

        if SHARED_LIB_AVAILABLE:
            # Use shared auth utilities
            user_data = await get_current_user_from_token(token)
        else:
            # Use consultancy auth integration
            consultancy_auth = get_consultancy_auth()
            validation_result = await consultancy_auth.validate_consultant_token(token)

            if not validation_result.get("valid"):
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail=validation_result.get("error", "Invalid token"),
                    headers={"WWW-Authenticate": "Bearer"},
                )

            user_data = validation_result.get("user", {})

        logger.info(f"User authenticated: {user_data.get('email', 'unknown')}")
        return user_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Authentication failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_current_active_user(
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Get current active user.
    
    Args:
        current_user: Current user from get_current_user dependency
        
    Returns:
        User context if user is active
        
    Raises:
        HTTPException: If user is inactive
    """
    if not current_user.get("is_active", False):
        logger.warning(f"Inactive user attempted access: {current_user.get('email')}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    
    return current_user


async def verify_token(token: str) -> Optional[Dict[str, Any]]:
    """
    Verify JWT token and return payload.
    
    Args:
        token: JWT token to verify
        
    Returns:
        Token payload if valid, None otherwise
    """
    try:
        # TODO: Implement actual JWT verification
        # This is a placeholder implementation
        
        # Mock payload for development
        payload = {
            "sub": "consultant_001",
            "tenant_id": "consultancy_tenant_001",
            "user_type": "consultant",
            "permissions": [
                "consultancy:read",
                "consultancy:write",
                "cases:manage",
                "projects:manage",
                "billing:manage"
            ],
            "exp": 9999999999,  # Far future expiration for development
            "iat": 1640995200,
            "iss": "consultancy-service"
        }
        
        return payload
        
    except Exception as e:
        logger.error(f"Token verification failed: {e}")
        return None


def require_permission(permission: str):
    """
    Decorator to require specific permission for endpoint access.
    
    Args:
        permission: Required permission string
        
    Returns:
        Dependency function
    """
    async def permission_checker(
        current_user: Dict[str, Any] = Depends(get_current_active_user)
    ) -> Dict[str, Any]:
        user_permissions = current_user.get("permissions", [])
        
        if permission not in user_permissions:
            logger.warning(
                f"User {current_user.get('email')} lacks permission: {permission}"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Insufficient permissions. Required: {permission}"
            )
        
        return current_user
    
    return permission_checker


# Common permission requirements
require_consultancy_read = require_permission("consultancy:read")
require_consultancy_write = require_permission("consultancy:write")
require_cases_manage = require_permission("cases:manage")
require_projects_manage = require_permission("projects:manage")
require_billing_manage = require_permission("billing:manage")


__all__ = [
    "get_current_user",
    "get_current_active_user", 
    "verify_token",
    "require_permission",
    "require_consultancy_read",
    "require_consultancy_write",
    "require_cases_manage",
    "require_projects_manage",
    "require_billing_manage"
]
