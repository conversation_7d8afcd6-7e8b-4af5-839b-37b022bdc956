"""
Consultancy Service Database Dependencies
========================================
Database session management and dependencies for the Consultancy Service.
Follows the pattern established by user_service for consistency.
"""

from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import Depends

# Import shared database utilities
try:
    from microservices.core.shared_lib.config.infrastructure.database.connection import (
        get_database_session,
        DatabaseSessionManager
    )
    SHARED_LIB_AVAILABLE = True
except ImportError:
    SHARED_LIB_AVAILABLE = False

from .config.database import get_consultancy_db_session, consultancy_db_config


class ConsultancyDatabaseDependencies:
    """Consultancy Service database dependencies manager."""
    
    def __init__(self):
        self.db_config = consultancy_db_config
    
    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Get database session for consultancy operations."""
        if SHARED_LIB_AVAILABLE:
            # Use shared database session management
            async for session in get_database_session(
                engine=self.db_config.engine,
                service_name="consultancy-service"
            ):
                yield session
        else:
            # Fallback to local session management
            async for session in get_consultancy_db_session():
                yield session
    
    async def get_readonly_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Get read-only database session for consultancy operations."""
        # For now, use the same session. In production, this could use read replicas
        async for session in self.get_session():
            yield session


# Global database dependencies instance
consultancy_db_deps = ConsultancyDatabaseDependencies()


async def get_consultancy_session() -> AsyncGenerator[AsyncSession, None]:
    """
    FastAPI dependency to get consultancy database session.
    
    Usage:
        @app.get("/projects")
        async def get_projects(db: AsyncSession = Depends(get_consultancy_session)):
            # Use db session here
    """
    async for session in consultancy_db_deps.get_session():
        yield session


async def get_consultancy_readonly_session() -> AsyncGenerator[AsyncSession, None]:
    """
    FastAPI dependency to get read-only consultancy database session.
    
    Usage:
        @app.get("/projects")
        async def get_projects(db: AsyncSession = Depends(get_consultancy_readonly_session)):
            # Use read-only db session here
    """
    async for session in consultancy_db_deps.get_readonly_session():
        yield session


def get_consultancy_db_dependencies() -> ConsultancyDatabaseDependencies:
    """Get consultancy database dependencies manager."""
    return consultancy_db_deps


# Type aliases for convenience
ConsultancySession = Depends(get_consultancy_session)
ConsultancyReadOnlySession = Depends(get_consultancy_readonly_session)
