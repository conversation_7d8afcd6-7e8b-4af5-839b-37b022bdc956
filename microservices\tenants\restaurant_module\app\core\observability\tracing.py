"""
Restaurant Module - Distributed Tracing
=======================================

Jaeger distributed tracing for restaurant operations.
"""

import logging
from typing import Optional
from opentelemetry import trace
from opentelemetry.exporter.jaeger.thrift import <PERSON><PERSON><PERSON>Exporter
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from opentelemetry.sdk.resources import Resource
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from opentelemetry.instrumentation.sqlalchemy import SQLAlchemyInstrumentor
from opentelemetry.instrumentation.redis import RedisInstrumentor
from opentelemetry.instrumentation.kafka import KafkaInstrumentor
from ..config.settings import settings

logger = logging.getLogger(__name__)


def setup_tracing():
    """
    Setup Jaeger distributed tracing for the restaurant module using shared_lib settings.
    """
    observability_settings = settings.observability_settings
    service_config = settings.service_observability_config

    if not observability_settings.JAEGER_ENABLED:
        logger.info("Jaeger tracing disabled")
        return
    
    try:
        # Create resource with service information using shared_lib config
        resource = Resource.create({
            "service.name": service_config.get("jaeger_service_name", settings.service_name),
            "service.version": settings.service_version,
            "service.environment": settings.environment,
            "service.namespace": "restaurant",
        })
        
        # Create tracer provider
        tracer_provider = TracerProvider(resource=resource)
        trace.set_tracer_provider(tracer_provider)
        
        # Create Jaeger exporter using shared_lib settings
        jaeger_exporter = JaegerExporter(
            agent_host_name=observability_settings.JAEGER_AGENT_HOST,
            agent_port=observability_settings.JAEGER_AGENT_PORT,
        )
        
        # Create span processor
        span_processor = BatchSpanProcessor(jaeger_exporter)
        tracer_provider.add_span_processor(span_processor)
        
        # Instrument frameworks
        FastAPIInstrumentor().instrument()
        SQLAlchemyInstrumentor().instrument()
        RedisInstrumentor().instrument()
        
        # TODO: Add Kafka instrumentation when available
        # KafkaInstrumentor.instrument()
        
        logger.info("Jaeger tracing initialized successfully")
        
    except Exception as e:
        logger.error(f"Failed to setup Jaeger tracing: {e}")


def get_tracer(name: Optional[str] = None) -> trace.Tracer:
    """
    Get tracer instance for creating spans.
    
    Args:
        name: Optional tracer name
        
    Returns:
        Tracer instance
    """
    tracer_name = name or f"{settings.SERVICE_NAME}.tracer"
    return trace.get_tracer(tracer_name, settings.SERVICE_VERSION)


class RestaurantTracing:
    """
    Restaurant-specific tracing utilities.
    """
    
    def __init__(self):
        self.tracer = get_tracer("restaurant.operations")
    
    def trace_order_processing(self, order_id: str, tenant_id: str):
        """Create span for order processing."""
        return self.tracer.start_span(
            "restaurant.order.process",
            attributes={
                "order.id": order_id,
                "tenant.id": tenant_id,
                "operation.type": "order_processing"
            }
        )
    
    def trace_kitchen_operation(self, order_id: str, station_id: str, operation: str):
        """Create span for kitchen operations."""
        return self.tracer.start_span(
            f"restaurant.kitchen.{operation}",
            attributes={
                "order.id": order_id,
                "kitchen.station_id": station_id,
                "operation.type": operation
            }
        )
    
    def trace_menu_operation(self, menu_item_id: str, tenant_id: str, operation: str):
        """Create span for menu operations."""
        return self.tracer.start_span(
            f"restaurant.menu.{operation}",
            attributes={
                "menu.item_id": menu_item_id,
                "tenant.id": tenant_id,
                "operation.type": operation
            }
        )
    
    def trace_table_operation(self, table_id: str, tenant_id: str, operation: str):
        """Create span for table operations."""
        return self.tracer.start_span(
            f"restaurant.table.{operation}",
            attributes={
                "table.id": table_id,
                "tenant.id": tenant_id,
                "operation.type": operation
            }
        )
    
    def trace_delivery_operation(self, order_id: str, delivery_id: str, operation: str):
        """Create span for delivery operations."""
        return self.tracer.start_span(
            f"restaurant.delivery.{operation}",
            attributes={
                "order.id": order_id,
                "delivery.id": delivery_id,
                "operation.type": operation
            }
        )
    
    def trace_inventory_operation(self, ingredient_id: str, tenant_id: str, operation: str):
        """Create span for inventory operations."""
        return self.tracer.start_span(
            f"restaurant.inventory.{operation}",
            attributes={
                "inventory.ingredient_id": ingredient_id,
                "tenant.id": tenant_id,
                "operation.type": operation
            }
        )
    
    def trace_external_service_call(self, service_name: str, operation: str, tenant_id: str):
        """Create span for external service calls."""
        return self.tracer.start_span(
            f"restaurant.external.{service_name}.{operation}",
            attributes={
                "external.service": service_name,
                "tenant.id": tenant_id,
                "operation.type": "external_call"
            }
        )
    
    def trace_event_processing(self, event_type: str, event_id: str):
        """Create span for event processing."""
        return self.tracer.start_span(
            f"restaurant.event.{event_type}",
            attributes={
                "event.type": event_type,
                "event.id": event_id,
                "operation.type": "event_processing"
            }
        )


# Global tracing instance
restaurant_tracing = RestaurantTracing()
