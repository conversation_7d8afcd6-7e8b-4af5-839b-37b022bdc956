#!/usr/bin/env python3
"""
Script para atualizar todas as configurações do Vault nos microserviços
para usar a configuração padronizada.
"""

import os
import re
import sys
from pathlib import Path
from typing import List, <PERSON><PERSON>


def find_settings_files() -> List[Path]:
    """Encontra todos os arquivos settings.py que contêm configurações do Vault."""
    settings_files = []
    microservices_dir = Path("microservices")
    
    for settings_file in microservices_dir.rglob("settings.py"):
        try:
            with open(settings_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'VAULT' in content:
                    settings_files.append(settings_file)
        except Exception as e:
            print(f"Erro ao ler {settings_file}: {e}")
    
    return settings_files


def update_imports(content: str) -> str:
    """Atualiza as importações para incluir StandardVaultConfig."""
    
    # Verifica se já tem a importação
    if 'StandardVaultConfig' in content:
        return content
    
    # Encontra a linha das importações do pydantic_settings
    import_pattern = r'from pydantic_settings import BaseSettings'
    
    if re.search(import_pattern, content):
        # Adiciona as importações necessárias
        new_imports = """from pydantic_settings import BaseSettings
import sys
sys.path.append('/app')
from microservices.core.shared_lib.config.vault_config import StandardVaultConfig"""
        
        content = re.sub(import_pattern, new_imports, content)
    
    return content


def update_class_definition(content: str) -> str:
    """Atualiza a definição da classe para herdar de StandardVaultConfig."""
    
    # Padrão para encontrar a definição da classe Settings
    class_pattern = r'class Settings\(BaseSettings\):'
    
    if re.search(class_pattern, content):
        content = re.sub(
            class_pattern,
            'class Settings(BaseSettings, StandardVaultConfig):',
            content
        )
    
    return content


def remove_vault_configs(content: str) -> str:
    """Remove configurações duplicadas do Vault."""
    
    # Padrões para remover configurações do Vault
    vault_patterns = [
        r'\s*# Security - HashiCorp Vault.*?\n',
        r'\s*# HashiCorp Vault.*?\n',
        r'\s*# Vault.*?\n',
        r'\s*VAULT_ENABLED:.*?\n',
        r'\s*VAULT_URL:.*?\n',
        r'\s*VAULT_TOKEN:.*?\n',
        r'\s*VAULT_NAMESPACE:.*?\n',
        r'\s*VAULT_MOUNT_POINT:.*?\n',
        r'\s*VAULT_COMPANY_ID:.*?\n',
        r'\s*VAULT_API_ADDR:.*?\n',
        r'\s*VAULT_CLUSTER_ADDR:.*?\n',
        r'\s*VAULT_AUTH_METHOD:.*?\n',
        r'\s*VAULT_ROLE_ID:.*?\n',
        r'\s*VAULT_SECRET_ID:.*?\n',
        r'\s*VAULT_TIMEOUT:.*?\n',
        r'\s*VAULT_CACHE_TTL:.*?\n',
    ]
    
    # Remove configurações individuais do Vault
    for pattern in vault_patterns:
        content = re.sub(pattern, '', content, flags=re.MULTILINE | re.DOTALL)
    
    # Remove blocos completos de configuração do Vault
    vault_block_patterns = [
        r'\s*# Security - HashiCorp Vault.*?(?=\n\s*#|\n\s*class|\n\s*def|\Z)',
        r'\s*# HashiCorp Vault.*?(?=\n\s*#|\n\s*class|\n\s*def|\Z)',
        r'\s*# Vault.*?(?=\n\s*#|\n\s*class|\n\s*def|\Z)',
    ]
    
    for pattern in vault_block_patterns:
        content = re.sub(pattern, '', content, flags=re.MULTILINE | re.DOTALL)
    
    # Adiciona comentário sobre herança
    vault_comment = """    # Configurações do Vault herdadas de StandardVaultConfig
    # VAULT_URL, VAULT_TOKEN, VAULT_ENABLED, etc. já estão definidas
    
"""
    
    # Encontra um local apropriado para inserir o comentário
    # (após configurações de banco de dados ou antes de outras configurações)
    db_patterns = [
        r'(\s*DATABASE_URL:.*?\n)',
        r'(\s*POSTGRES_.*?\n)',
        r'(\s*REDIS_.*?\n)',
    ]
    
    for pattern in db_patterns:
        if re.search(pattern, content):
            content = re.sub(pattern, r'\1' + vault_comment, content, count=1)
            break
    else:
        # Se não encontrar padrões de DB, adiciona após configurações de serviço
        service_pattern = r'(\s*SERVICE_VERSION:.*?\n)'
        if re.search(service_pattern, content):
            content = re.sub(service_pattern, r'\1' + vault_comment, content, count=1)
    
    return content


def update_settings_file(file_path: Path) -> bool:
    """Atualiza um arquivo settings.py específico."""
    
    try:
        print(f"Atualizando {file_path}...")
        
        # Lê o conteúdo atual
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Aplica as transformações
        original_content = content
        content = update_imports(content)
        content = update_class_definition(content)
        content = remove_vault_configs(content)
        
        # Verifica se houve mudanças
        if content != original_content:
            # Salva o arquivo atualizado
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ {file_path} atualizado com sucesso!")
            return True
        else:
            print(f"ℹ️  {file_path} já está atualizado.")
            return False
            
    except Exception as e:
        print(f"❌ Erro ao atualizar {file_path}: {e}")
        return False


def main():
    """Função principal."""
    
    print("🔧 Iniciando atualização das configurações do Vault...")
    print("=" * 60)
    
    # Muda para o diretório raiz do projeto
    os.chdir(Path(__file__).parent.parent)
    
    # Encontra todos os arquivos settings.py com configurações do Vault
    settings_files = find_settings_files()
    
    if not settings_files:
        print("❌ Nenhum arquivo settings.py com configurações do Vault encontrado.")
        return
    
    print(f"📁 Encontrados {len(settings_files)} arquivos para atualizar:")
    for file_path in settings_files:
        print(f"   - {file_path}")
    
    print("\n🚀 Iniciando atualizações...")
    print("-" * 40)
    
    # Atualiza cada arquivo
    updated_count = 0
    for file_path in settings_files:
        if update_settings_file(file_path):
            updated_count += 1
    
    print("\n" + "=" * 60)
    print(f"✅ Atualização concluída!")
    print(f"📊 Arquivos processados: {len(settings_files)}")
    print(f"📊 Arquivos atualizados: {updated_count}")
    print(f"📊 Arquivos já atualizados: {len(settings_files) - updated_count}")
    
    if updated_count > 0:
        print("\n🔄 Próximos passos:")
        print("1. Reconstruir os containers Docker")
        print("2. Reiniciar os serviços")
        print("3. Verificar logs para confirmar funcionamento")


if __name__ == "__main__":
    main()
