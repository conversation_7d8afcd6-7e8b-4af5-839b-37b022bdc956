"""
Ghost Function Service - Cache Management

Sistema de cache distribuído usando Redis para alta performance.
Suporta cache de dados, sessões e fallback responses.
Utiliza a shared_lib para configurações centralizadas.

Author: Trix Platform Team
"""

import json
import time
from typing import Any, Dict, Optional, Union
from functools import lru_cache

from app.core.config import get_settings
from microservices.core.shared_lib.config import (
    CacheClient,
    get_cache_client,
    initialize_cache,
    get_logger
)

# Configurações
settings = get_settings()

# Logger estruturado (usando shared_lib)
logger = get_logger("ghost-function-service")

# Cache client global
_cache_client: Optional[CacheClient] = None


async def init_cache() -> CacheClient:
    """
    Inicializa o cache usando a shared_lib.

    Returns:
        CacheClient: Instância do cache client
    """
    global _cache_client

    if _cache_client is None:
        try:
            _cache_client = CacheClient("ghost-function-service")
            await _cache_client.initialize()
            logger.info("✅ Cache inicializado com sucesso")

        except Exception as e:
            logger.error(f"❌ Erro ao inicializar cache: {e}")
            raise

    return _cache_client


async def get_cache() -> CacheClient:
    """
    Obtém a instância do cache client.

    Returns:
        CacheClient: Instância do cache client
    """
    if _cache_client is None:
        return await init_cache()
    return _cache_client


async def close_cache():
    """Fecha a conexão com cache."""
    global _cache_client

    if _cache_client:
        await _cache_client.close()
        _cache_client = None
        logger.info("Cache connection closed")


class CacheManager:
    """
    Gerenciador de cache para o Ghost Function Service.

    Fornece métodos de alto nível para cache de dados,
    fallback responses e invalidação inteligente.
    Utiliza a shared_lib para operações de cache.
    """

    def __init__(self):
        self.cache_client: Optional[CacheClient] = None

    async def _get_cache(self) -> CacheClient:
        """Obtém instância do cache client."""
        if self.cache_client is None:
            self.cache_client = await get_cache()
        return self.cache_client
    
    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        namespace: str = "ghost"
    ) -> bool:
        """
        Define um valor no cache.

        Args:
            key: Chave do cache
            value: Valor a ser armazenado
            ttl: Time to live em segundos
            namespace: Namespace para organização

        Returns:
            bool: True se sucesso
        """
        try:
            cache = await self._get_cache()
            full_key = f"{namespace}:{key}"

            result = await cache.set(full_key, value, ttl)

            logger.debug(
                "Cache set",
                key=full_key,
                ttl=ttl,
                value_type=type(value).__name__,
                success=result
            )

            return result

        except Exception as e:
            logger.error(
                "Erro ao definir cache",
                key=key,
                namespace=namespace,
                error=str(e)
            )
            return False
    
    async def get(
        self,
        key: str,
        namespace: str = "ghost",
        default: Any = None
    ) -> Any:
        """
        Obtém um valor do cache.

        Args:
            key: Chave do cache
            namespace: Namespace para organização
            default: Valor padrão se não encontrado

        Returns:
            Any: Valor do cache ou default
        """
        try:
            cache = await self._get_cache()
            full_key = f"{namespace}:{key}"

            value = await cache.get(full_key, default)

            logger.debug(
                "Cache get",
                key=full_key,
                found=value is not default
            )

            return value

        except Exception as e:
            logger.error(
                "Erro ao obter cache",
                key=key,
                namespace=namespace,
                error=str(e)
            )
            return default
    
    async def delete(self, key: str, namespace: str = "ghost") -> bool:
        """
        Remove um valor do cache.

        Args:
            key: Chave do cache
            namespace: Namespace para organização

        Returns:
            bool: True se removido
        """
        try:
            cache = await self._get_cache()
            full_key = f"{namespace}:{key}"

            result = await cache.delete(full_key)

            logger.debug(
                "Cache deleted",
                key=full_key,
                deleted_count=result
            )
            
            return bool(result)
            
        except Exception as e:
            logger.error(
                "Erro ao deletar cache",
                key=key,
                namespace=namespace,
                error=str(e)
            )
            return False
    
    async def exists(self, key: str, namespace: str = "ghost") -> bool:
        """
        Verifica se uma chave existe no cache.

        Args:
            key: Chave do cache
            namespace: Namespace para organização

        Returns:
            bool: True se existe
        """
        try:
            cache = await self._get_cache()
            full_key = f"{namespace}:{key}"

            result = await cache.exists(full_key)
            return result

        except Exception as e:
            logger.error(
                "Erro ao verificar existência no cache",
                key=key,
                namespace=namespace,
                error=str(e)
            )
            return False
    
    async def set_fallback_response(
        self,
        service_name: str,
        path: str,
        method: str,
        response_data: Dict,
        ttl: int = 300  # 5 minutos
    ) -> bool:
        """
        Armazena uma resposta de fallback.
        
        Args:
            service_name: Nome do serviço
            path: Caminho da requisição
            method: Método HTTP
            response_data: Dados da resposta
            ttl: Time to live em segundos
            
        Returns:
            bool: True se sucesso
        """
        key = f"fallback:{service_name}:{path}:{method}"
        
        # Adicionar timestamp para controle de idade
        response_data["cached_at"] = time.time()
        
        return await self.set(key, response_data, ttl, namespace="ghost")
    
    async def get_fallback_response(
        self,
        service_name: str,
        path: str,
        method: str
    ) -> Optional[Dict]:
        """
        Obtém uma resposta de fallback.
        
        Args:
            service_name: Nome do serviço
            path: Caminho da requisição
            method: Método HTTP
            
        Returns:
            Optional[Dict]: Dados da resposta ou None
        """
        key = f"fallback:{service_name}:{path}:{method}"
        
        response_data = await self.get(key, namespace="ghost")
        if response_data and isinstance(response_data, dict):
            # Calcular idade do cache
            cached_at = response_data.get("cached_at", time.time())
            response_data["age"] = int(time.time() - cached_at)
            
        return response_data


# Instância global do cache manager
cache_manager = CacheManager()
