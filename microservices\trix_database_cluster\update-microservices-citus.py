#!/usr/bin/env python3
"""
🔄 Atualizador de Microserviços para Citus
Atualiza configurações dos microserviços para usar coordenadores Citus
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, List, Any

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MicroservicesCitusUpdater:
    """Atualizador de configurações para Citus"""
    
    def __init__(self):
        self.microservices_mapping = self._load_microservices_mapping()
        self.coordinators_config = self._load_coordinators_config()
        
    def _load_microservices_mapping(self) -> Dict[str, Dict[str, Any]]:
        """Mapeamento de microserviços para coordenadores Citus"""
        return {
            # Core Services (Coordinator: localhost:5432)
            'auth_service': {
                'coordinator': 'core',
                'host': 'trix-citus-core-coordinator',
                'port': 5432,
                'database': 'auth_db',
                'path': 'microservices/core/auth_service'
            },
            'user_service': {
                'coordinator': 'core',
                'host': 'trix-citus-core-coordinator',
                'port': 5432,
                'database': 'users_db',
                'path': 'microservices/core/user_service'
            },
            'tenant_service': {
                'coordinator': 'core',
                'host': 'trix-citus-core-coordinator',
                'port': 5432,
                'database': 'tenants_db',
                'path': 'microservices/core/tenant_service'
            },
            'core_service': {
                'coordinator': 'core',
                'host': 'trix-citus-core-coordinator',
                'port': 5432,
                'database': 'core_db',
                'path': 'microservices/core/core_service'
            },
            'i18n_service': {
                'coordinator': 'core',
                'host': 'trix-citus-core-coordinator',
                'port': 5432,
                'database': 'i18n_db',
                'path': 'microservices/core/i18n_service'
            },
            
            # Tenant Services (Coordinator: localhost:5433)
            'restaurant_module': {
                'coordinator': 'tenant',
                'host': 'trix-citus-tenant-coordinator',
                'port': 5433,
                'database': 'restaurant_db',
                'path': 'microservices/tenants/restaurant_module'
            },
            'consultancy_module': {
                'coordinator': 'tenant',
                'host': 'trix-citus-tenant-coordinator',
                'port': 5433,
                'database': 'consultancy_db',
                'path': 'microservices/tenants/consultancy_module'
            },
            
            # Shared Services (Coordinator: localhost:5434)
            'hr_module': {
                'coordinator': 'shared',
                'host': 'trix-citus-shared-coordinator',
                'port': 5434,
                'database': 'hr_db',
                'path': 'microservices/shared/hr_module'
            },
            'crm_module': {
                'coordinator': 'shared',
                'host': 'trix-citus-shared-coordinator',
                'port': 5434,
                'database': 'crm_db',
                'path': 'microservices/shared/crm_module'
            },
            'email_module': {
                'coordinator': 'shared',
                'host': 'trix-citus-shared-coordinator',
                'port': 5434,
                'database': 'email_db',
                'path': 'microservices/shared/email_module'
            },
            'financial_module': {
                'coordinator': 'shared',
                'host': 'trix-citus-shared-coordinator',
                'port': 5434,
                'database': 'financial_db',
                'path': 'microservices/shared/financial_module'
            }
        }
    
    def _load_coordinators_config(self) -> Dict[str, Dict[str, Any]]:
        """Configuração dos coordenadores"""
        return {
            'core': {
                'host': 'trix-citus-core-coordinator',
                'port': 5432,
                'external_port': 5432,
                'description': 'Core Services Coordinator'
            },
            'tenant': {
                'host': 'trix-citus-tenant-coordinator',
                'port': 5433,
                'external_port': 5433,
                'description': 'Tenant Services Coordinator'
            },
            'shared': {
                'host': 'trix-citus-shared-coordinator',
                'port': 5434,
                'external_port': 5434,
                'description': 'Shared Services Coordinator'
            }
        }
    
    def update_all_microservices(self):
        """Atualiza todos os microserviços para usar Citus"""
        logger.info("🔄 Iniciando atualização dos microserviços para Citus...")
        
        updated_count = 0
        failed_count = 0
        
        for service_name, config in self.microservices_mapping.items():
            try:
                logger.info(f"📋 Atualizando {service_name}...")
                
                if self._update_microservice(service_name, config):
                    updated_count += 1
                    logger.info(f"✅ {service_name} atualizado com sucesso")
                else:
                    failed_count += 1
                    logger.warning(f"⚠️ {service_name} não foi atualizado")
                    
            except Exception as e:
                failed_count += 1
                logger.error(f"❌ Erro ao atualizar {service_name}: {e}")
        
        # Relatório final
        logger.info("=" * 60)
        logger.info("📊 RELATÓRIO DE ATUALIZAÇÃO PARA CITUS")
        logger.info("=" * 60)
        logger.info(f"🎯 Total de microserviços: {len(self.microservices_mapping)}")
        logger.info(f"✅ Atualizados com sucesso: {updated_count}")
        logger.info(f"❌ Falharam: {failed_count}")
        logger.info(f"📈 Taxa de sucesso: {(updated_count / len(self.microservices_mapping)) * 100:.1f}%")
        logger.info("=" * 60)
    
    def _update_microservice(self, service_name: str, config: Dict[str, Any]) -> bool:
        """Atualiza um microserviço específico"""
        service_path = Path(config['path'])
        
        if not service_path.exists():
            logger.warning(f"⚠️ Caminho não encontrado: {service_path}")
            return False
        
        # Atualizar configurações de banco
        success = True
        
        # 1. Atualizar .env se existir
        if self._update_env_file(service_path, config):
            logger.info(f"   ✅ .env atualizado para {service_name}")
        else:
            logger.warning(f"   ⚠️ .env não encontrado para {service_name}")
        
        # 2. Atualizar docker-compose.yml se existir
        if self._update_docker_compose(service_path, config):
            logger.info(f"   ✅ docker-compose.yml atualizado para {service_name}")
        else:
            logger.warning(f"   ⚠️ docker-compose.yml não encontrado para {service_name}")
        
        # 3. Atualizar configurações de aplicação
        if self._update_app_config(service_path, config):
            logger.info(f"   ✅ Configurações de app atualizadas para {service_name}")
        
        # 4. Criar arquivo de configuração Citus específico
        if self._create_citus_config(service_path, service_name, config):
            logger.info(f"   ✅ Configuração Citus criada para {service_name}")
        
        return success
    
    def _update_env_file(self, service_path: Path, config: Dict[str, Any]) -> bool:
        """Atualiza arquivo .env"""
        env_file = service_path / '.env'
        
        if not env_file.exists():
            return False
        
        try:
            # Ler arquivo atual
            with open(env_file, 'r') as f:
                lines = f.readlines()
            
            # Atualizar linhas de banco de dados
            updated_lines = []
            database_url_updated = False
            
            for line in lines:
                if line.startswith('DATABASE_URL=') or line.startswith('POSTGRES_HOST='):
                    # Substituir por configuração Citus
                    if not database_url_updated:
                        updated_lines.append(f"# Citus Configuration - {config['coordinator'].upper()} Coordinator\n")
                        updated_lines.append(f"POSTGRES_HOST={config['host']}\n")
                        updated_lines.append(f"POSTGRES_PORT={config['port']}\n")
                        updated_lines.append(f"POSTGRES_DB={config['database']}\n")
                        updated_lines.append(f"POSTGRES_USER=postgres\n")
                        updated_lines.append(f"POSTGRES_PASSWORD=TrixSuperSecure2024!\n")
                        updated_lines.append(f"DATABASE_URL=postgresql+asyncpg://postgres:TrixSuperSecure2024!@{config['host']}:{config['port']}/{config['database']}\n")
                        database_url_updated = True
                elif not any(line.startswith(prefix) for prefix in ['POSTGRES_HOST=', 'POSTGRES_PORT=', 'POSTGRES_DB=']):
                    updated_lines.append(line)
            
            # Se não encontrou DATABASE_URL, adicionar
            if not database_url_updated:
                updated_lines.append(f"\n# Citus Configuration - {config['coordinator'].upper()} Coordinator\n")
                updated_lines.append(f"POSTGRES_HOST={config['host']}\n")
                updated_lines.append(f"POSTGRES_PORT={config['port']}\n")
                updated_lines.append(f"POSTGRES_DB={config['database']}\n")
                updated_lines.append(f"POSTGRES_USER=postgres\n")
                updated_lines.append(f"POSTGRES_PASSWORD=TrixSuperSecure2024!\n")
                updated_lines.append(f"DATABASE_URL=postgresql+asyncpg://postgres:TrixSuperSecure2024!@{config['host']}:{config['port']}/{config['database']}\n")
            
            # Escrever arquivo atualizado
            with open(env_file, 'w') as f:
                f.writelines(updated_lines)
            
            return True
            
        except Exception as e:
            logger.error(f"Erro ao atualizar .env: {e}")
            return False
    
    def _update_docker_compose(self, service_path: Path, config: Dict[str, Any]) -> bool:
        """Atualiza docker-compose.yml"""
        compose_file = service_path / 'docker-compose.yml'
        
        if not compose_file.exists():
            return False
        
        try:
            # Ler arquivo atual
            with open(compose_file, 'r') as f:
                content = f.read()
            
            # Substituir referências ao banco antigo
            content = content.replace('trix-postgres-primary', config['host'])
            content = content.replace('trix-database-cluster-primary', config['host'])
            content = content.replace('5432:5432', f"{config['port']}:5432")
            
            # Adicionar dependência do coordenador Citus
            if 'depends_on:' in content and config['host'] not in content:
                content = content.replace(
                    'depends_on:',
                    f"depends_on:\n      {config['host']}:\n        condition: service_healthy"
                )
            
            # Escrever arquivo atualizado
            with open(compose_file, 'w') as f:
                f.write(content)
            
            return True
            
        except Exception as e:
            logger.error(f"Erro ao atualizar docker-compose.yml: {e}")
            return False
    
    def _update_app_config(self, service_path: Path, config: Dict[str, Any]) -> bool:
        """Atualiza configurações da aplicação"""
        # Procurar por arquivos de configuração comuns
        config_files = [
            service_path / 'app' / 'config.py',
            service_path / 'config' / 'database.py',
            service_path / 'app' / 'core' / 'config.py',
            service_path / 'src' / 'config.py'
        ]
        
        updated = False
        
        for config_file in config_files:
            if config_file.exists():
                try:
                    with open(config_file, 'r') as f:
                        content = f.read()
                    
                    # Substituir configurações de banco
                    if 'DATABASE_URL' in content or 'POSTGRES_HOST' in content:
                        content = content.replace('trix-postgres-primary', config['host'])
                        content = content.replace('trix-database-cluster-primary', config['host'])
                        
                        with open(config_file, 'w') as f:
                            f.write(content)
                        
                        updated = True
                        
                except Exception as e:
                    logger.error(f"Erro ao atualizar {config_file}: {e}")
        
        return updated
    
    def _create_citus_config(self, service_path: Path, service_name: str, config: Dict[str, Any]) -> bool:
        """Cria arquivo de configuração específico do Citus"""
        try:
            citus_config = {
                'service_name': service_name,
                'coordinator': {
                    'name': config['coordinator'],
                    'host': config['host'],
                    'port': config['port'],
                    'database': config['database']
                },
                'citus_settings': {
                    'distribution_key': 'tenant_id' if 'tenant' in service_name else 'user_id',
                    'shard_count': 64 if config['coordinator'] == 'tenant' else 32,
                    'replication_factor': 2
                },
                'connection_settings': {
                    'pool_size': 10,
                    'max_overflow': 20,
                    'pool_timeout': 30,
                    'pool_recycle': 3600
                }
            }
            
            # Criar diretório config se não existir
            config_dir = service_path / 'config'
            config_dir.mkdir(exist_ok=True)
            
            # Escrever configuração Citus
            citus_config_file = config_dir / 'citus.json'
            with open(citus_config_file, 'w') as f:
                json.dump(citus_config, f, indent=2)
            
            return True
            
        except Exception as e:
            logger.error(f"Erro ao criar configuração Citus: {e}")
            return False
    
    def generate_connection_summary(self):
        """Gera resumo das conexões Citus"""
        logger.info("📋 Resumo das Conexões Citus:")
        logger.info("=" * 60)
        
        for coord_name, coord_config in self.coordinators_config.items():
            logger.info(f"🎯 {coord_config['description']}")
            logger.info(f"   Host: {coord_config['host']}")
            logger.info(f"   Porta Externa: {coord_config['external_port']}")
            
            # Listar microserviços deste coordenador
            services = [name for name, config in self.microservices_mapping.items() 
                       if config['coordinator'] == coord_name]
            
            logger.info(f"   Microserviços ({len(services)}):")
            for service in services:
                db_name = self.microservices_mapping[service]['database']
                logger.info(f"     - {service} → {db_name}")
            
            logger.info("")

if __name__ == "__main__":
    updater = MicroservicesCitusUpdater()
    updater.update_all_microservices()
    updater.generate_connection_summary()
