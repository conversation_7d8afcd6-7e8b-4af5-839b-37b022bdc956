# ============================================================================
# FINANCIAL SERVICE - Microserviço Financeiro
# ============================================================================
# Porta: 8013 | Database: postgres-financial (5446) | Redis: DB 13
# Submódulos: budgets, categories, consultancy, control, documents, invoices, reports, settings, transactions
# ============================================================================

services:
  # ============================================================================
  # FINANCIAL SERVICE - Serviço Principal
  # ============================================================================
  trix-shared-financial:
    build:
      context: ../
      dockerfile: docker/Dockerfile
    container_name: trix-shared-financial
    ports:
      - "8013:8000"
    environment:
      - DATABASE_URL=postgresql+asyncpg://trixuser:trixpass@db:5432/trixdb
      - REDIS_URL=redis://:${REDIS_PASSWORD}@trix-redis:6379/13
      - AUTH_SERVICE_URL=http://trix-core-auth:8001
      - TENANT_SERVICE_URL=http://trix-core-tenant:8003
      - PAYMENT_SERVICE_URL=http://trix-infrastructure-payments:8020
      - SECRET_KEY=${JWT_SECRET_KEY}
      - DEBUG=${DEBUG}
      - SERVICE_NAME=financial-service
      - SERVICE_VERSION=1.0.0
      - CELERY_BROKER_URL=redis://:${REDIS_PASSWORD}@trix-redis:6379/13
      - CELERY_RESULT_BACKEND=redis://:${REDIS_PASSWORD}@trix-redis:6379/13
      - ACCOUNTING_INTEGRATION_ENABLED=true
      - BANK_RECONCILIATION_ENABLED=true
      - MULTI_CURRENCY_ENABLED=true
      - DEFAULT_CURRENCY=BRL
    depends_on:
      - trix-redis
    external_links:
      - db:db
    volumes:
      - ../:/app
      - trix_financial_documents:/app/documents
      - trix_financial_reports:/app/reports
      - trix_financial_backups:/app/backups
    networks:
      - trix-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    labels:
      - "trix.category=shared"
      - "trix.service=microservice"
      - "trix.module=financial"
      - "trix.port=8013"

  # ============================================================================
  # FINANCIAL DATABASE - Usando banco principal consolidado
  # ============================================================================
  # Banco removido - usando banco principal 'db' do docker-compose.yml raiz

networks:
  trix-network:
    external: true
