"""
Media System Configuration
==========================

Configurações específicas para o Media System incluindo storage,
processamento, CDN e integração com serviços de IA.
"""

import os
from typing import List, Dict, Any, Optional
from pydantic import Field
from pydantic_settings import BaseSettings

from .vault_config import VaultBaseSettings


class MediaStorageSettings(BaseSettings):
    """Configurações de armazenamento de mídia."""
    
    # Storage Backend
    STORAGE_BACKEND: str = Field(default="minio", env="MEDIA_STORAGE_BACKEND")
    
    # MinIO/S3 Configuration
    STORAGE_ENDPOINT: str = Field(default="minio:9000", env="MEDIA_STORAGE_ENDPOINT")
    STORAGE_ACCESS_KEY: str = Field(default="", env="MEDIA_STORAGE_ACCESS_KEY")
    STORAGE_SECRET_KEY: str = Field(default="", env="MEDIA_STORAGE_SECRET_KEY")
    STORAGE_SECURE: bool = Field(default=False, env="MEDIA_STORAGE_SECURE")
    STORAGE_REGION: str = Field(default="us-east-1", env="MEDIA_STORAGE_REGION")
    
    # Bucket Configuration
    BUCKET_PREFIX: str = Field(default="trix-media", env="MEDIA_BUCKET_PREFIX")
    BUCKET_VERSIONING: bool = Field(default=True, env="MEDIA_BUCKET_VERSIONING")
    
    # Multi-region Configuration
    STORAGE_REGIONS: List[str] = Field(
        default=["us-east-1", "eu-west-1", "ap-southeast-1"],
        env="MEDIA_STORAGE_REGIONS"
    )
    REPLICATION_FACTOR: int = Field(default=3, env="MEDIA_REPLICATION_FACTOR")
    
    class Config:
        env_file = ".env"
        case_sensitive = True


class MediaProcessingSettings(BaseSettings):
    """Configurações de processamento de mídia."""
    
    # File Size Limits
    MAX_UPLOAD_SIZE: int = Field(default=1024*1024*1024, env="MEDIA_MAX_UPLOAD_SIZE")  # 1GB
    MAX_IMAGE_SIZE: int = Field(default=50*1024*1024, env="MEDIA_MAX_IMAGE_SIZE")  # 50MB
    MAX_VIDEO_SIZE: int = Field(default=500*1024*1024, env="MEDIA_MAX_VIDEO_SIZE")  # 500MB
    MAX_DOCUMENT_SIZE: int = Field(default=100*1024*1024, env="MEDIA_MAX_DOCUMENT_SIZE")  # 100MB
    
    # Allowed MIME Types
    ALLOWED_IMAGE_TYPES: List[str] = Field(
        default=["image/jpeg", "image/png", "image/webp", "image/gif", "image/svg+xml"],
        env="MEDIA_ALLOWED_IMAGE_TYPES"
    )
    ALLOWED_VIDEO_TYPES: List[str] = Field(
        default=["video/mp4", "video/avi", "video/mov", "video/webm"],
        env="MEDIA_ALLOWED_VIDEO_TYPES"
    )
    ALLOWED_AUDIO_TYPES: List[str] = Field(
        default=["audio/mp3", "audio/wav", "audio/flac", "audio/ogg"],
        env="MEDIA_ALLOWED_AUDIO_TYPES"
    )
    ALLOWED_DOCUMENT_TYPES: List[str] = Field(
        default=[
            "application/pdf",
            "application/msword",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/vnd.ms-excel",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        ],
        env="MEDIA_ALLOWED_DOCUMENT_TYPES"
    )
    
    # Processing Configuration
    AUTO_THUMBNAIL: bool = Field(default=True, env="MEDIA_AUTO_THUMBNAIL")
    AUTO_COMPRESSION: bool = Field(default=True, env="MEDIA_AUTO_COMPRESSION")
    THUMBNAIL_SIZES: List[int] = Field(default=[150, 300, 600], env="MEDIA_THUMBNAIL_SIZES")
    COMPRESSION_QUALITY: int = Field(default=85, env="MEDIA_COMPRESSION_QUALITY")
    
    # Background Processing
    PROCESSING_WORKERS: int = Field(default=4, env="MEDIA_PROCESSING_WORKERS")
    PROCESSING_TIMEOUT: int = Field(default=300, env="MEDIA_PROCESSING_TIMEOUT")  # 5 minutes
    
    class Config:
        env_file = ".env"
        case_sensitive = True


class MediaAISettings(BaseSettings):
    """Configurações de IA para processamento de mídia."""
    
    # AI Processing
    AI_PROCESSING_ENABLED: bool = Field(default=True, env="MEDIA_AI_PROCESSING_ENABLED")
    
    # OCR Configuration
    OCR_ENABLED: bool = Field(default=True, env="MEDIA_OCR_ENABLED")
    OCR_LANGUAGES: List[str] = Field(default=["por", "eng", "spa"], env="MEDIA_OCR_LANGUAGES")
    OCR_CONFIDENCE_THRESHOLD: int = Field(default=60, env="MEDIA_OCR_CONFIDENCE_THRESHOLD")
    
    # Content Moderation
    CONTENT_MODERATION_ENABLED: bool = Field(default=True, env="MEDIA_CONTENT_MODERATION_ENABLED")
    MODERATION_THRESHOLD: float = Field(default=0.8, env="MEDIA_MODERATION_THRESHOLD")
    
    # Auto-tagging
    AUTO_TAGGING_ENABLED: bool = Field(default=True, env="MEDIA_AUTO_TAGGING_ENABLED")
    MAX_TAGS_PER_MEDIA: int = Field(default=10, env="MEDIA_MAX_TAGS_PER_MEDIA")
    
    # Content Classification
    CONTENT_CLASSIFICATION_ENABLED: bool = Field(default=True, env="MEDIA_CONTENT_CLASSIFICATION_ENABLED")
    CLASSIFICATION_CONFIDENCE_THRESHOLD: float = Field(default=0.7, env="MEDIA_CLASSIFICATION_CONFIDENCE_THRESHOLD")
    
    class Config:
        env_file = ".env"
        case_sensitive = True


class MediaCDNSettings(BaseSettings):
    """Configurações de CDN para mídia."""
    
    # CDN Configuration
    CDN_ENABLED: bool = Field(default=True, env="MEDIA_CDN_ENABLED")
    CDN_BASE_URL: str = Field(default="https://cdn.trix.com", env="MEDIA_CDN_BASE_URL")
    
    # Cache Configuration
    CACHE_TTL: int = Field(default=3600, env="MEDIA_CACHE_TTL")  # 1 hour
    CACHE_MAX_AGE: int = Field(default=86400, env="MEDIA_CACHE_MAX_AGE")  # 24 hours
    
    # CDN Regions
    CDN_REGIONS: List[str] = Field(
        default=["us-east", "eu-west", "asia-pacific"],
        env="MEDIA_CDN_REGIONS"
    )
    
    # Optimization
    AUTO_WEBP_CONVERSION: bool = Field(default=True, env="MEDIA_AUTO_WEBP_CONVERSION")
    PROGRESSIVE_JPEG: bool = Field(default=True, env="MEDIA_PROGRESSIVE_JPEG")
    
    class Config:
        env_file = ".env"
        case_sensitive = True


class MediaSecuritySettings(BaseSettings):
    """Configurações de segurança para mídia."""
    
    # Antivirus
    ANTIVIRUS_ENABLED: bool = Field(default=True, env="MEDIA_ANTIVIRUS_ENABLED")
    ANTIVIRUS_TIMEOUT: int = Field(default=30, env="MEDIA_ANTIVIRUS_TIMEOUT")
    
    # File Validation
    STRICT_MIME_VALIDATION: bool = Field(default=True, env="MEDIA_STRICT_MIME_VALIDATION")
    SCAN_FILE_HEADERS: bool = Field(default=True, env="MEDIA_SCAN_FILE_HEADERS")
    
    # Access Control
    SIGNED_URLS_ENABLED: bool = Field(default=True, env="MEDIA_SIGNED_URLS_ENABLED")
    SIGNED_URL_EXPIRY: int = Field(default=3600, env="MEDIA_SIGNED_URL_EXPIRY")  # 1 hour
    
    # Rate Limiting
    UPLOAD_RATE_LIMIT: int = Field(default=100, env="MEDIA_UPLOAD_RATE_LIMIT")  # per minute
    DOWNLOAD_RATE_LIMIT: int = Field(default=1000, env="MEDIA_DOWNLOAD_RATE_LIMIT")  # per minute
    
    class Config:
        env_file = ".env"
        case_sensitive = True


class MediaServiceSettings(VaultBaseSettings):
    """
    Configurações principais do Media System.
    
    Herda configurações comuns de VaultBaseSettings e adiciona
    configurações específicas do Media System.
    """
    
    # Service Identity
    SERVICE_NAME: str = Field(default="media-system", env="SERVICE_NAME")
    SERVICE_PORT: int = Field(default=8007, env="SERVICE_PORT")
    
    # Database Configuration
    DATABASE_URL: str = Field(
        default="postgresql+asyncpg://media_user:MediaSecure2024!#$@trix-postgres-primary:5432/media_db",
        env="MEDIA_DATABASE_URL"
    )
    DATABASE_POOL_SIZE: int = Field(default=20, env="MEDIA_DATABASE_POOL_SIZE")
    DATABASE_MAX_OVERFLOW: int = Field(default=30, env="MEDIA_DATABASE_MAX_OVERFLOW")
    
    # Service URLs
    USER_SERVICE_URL: str = Field(
        default="http://user-service.trix.svc.cluster.local:8002",
        env="USER_SERVICE_URL"
    )
    TENANT_SERVICE_URL: str = Field(
        default="http://tenant-service.trix.svc.cluster.local:8003",
        env="TENANT_SERVICE_URL"
    )
    CDN_SERVICE_URL: str = Field(
        default="http://cdn-service.trix.svc.cluster.local:8009",
        env="CDN_SERVICE_URL"
    )
    
    # Nested Settings
    @property
    def storage_settings(self) -> MediaStorageSettings:
        """Retorna configurações de storage."""
        return MediaStorageSettings()
    
    @property
    def processing_settings(self) -> MediaProcessingSettings:
        """Retorna configurações de processamento."""
        return MediaProcessingSettings()
    
    @property
    def ai_settings(self) -> MediaAISettings:
        """Retorna configurações de IA."""
        return MediaAISettings()
    
    @property
    def cdn_settings(self) -> MediaCDNSettings:
        """Retorna configurações de CDN."""
        return MediaCDNSettings()
    
    @property
    def security_settings(self) -> MediaSecuritySettings:
        """Retorna configurações de segurança."""
        return MediaSecuritySettings()
    
    def get_media_vault_paths(self) -> Dict[str, str]:
        """Retorna caminhos específicos do Vault para o Media System."""
        return {
            "service": self.get_vault_path("service"),
            "database": self.get_vault_path("database"),
            "storage": f"storage/{self.VAULT_ENVIRONMENT}/media-system",
            "ai": f"ai/{self.VAULT_ENVIRONMENT}/media-system",
            "cdn": f"cdn/{self.VAULT_ENVIRONMENT}/media-system"
        }
    
    def get_allowed_mime_types(self) -> List[str]:
        """Retorna todos os tipos MIME permitidos."""
        processing = self.processing_settings
        return (
            processing.ALLOWED_IMAGE_TYPES +
            processing.ALLOWED_VIDEO_TYPES +
            processing.ALLOWED_AUDIO_TYPES +
            processing.ALLOWED_DOCUMENT_TYPES
        )
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Instância global das configurações
media_settings = MediaServiceSettings()
