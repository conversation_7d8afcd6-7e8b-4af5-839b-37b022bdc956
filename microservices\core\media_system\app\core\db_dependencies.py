"""
Database Dependencies for Media System
======================================

Gerenciamento de dependências de banco de dados para o Media System,
seguindo o padrão estabelecido no User Service.

✅ MIGRAÇÃO CONCLUÍDA - Usando shared_lib para configurações comuns
"""

from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.pool import NullPool

from .config.settings import settings


class DatabaseManager:
    """Gerenciador de banco de dados para o Media System."""
    
    def __init__(self):
        self.engine = None
        self.session_factory = None
        self._initialized = False
    
    async def initialize(self):
        """Inicializa o engine e session factory."""
        if self._initialized:
            return
        
        # Criar engine com configurações otimizadas para Citus Data
        self.engine = create_async_engine(
            settings.DATABASE_URL,
            poolclass=NullPool,  # Para Citus Data
            pool_size=settings.DATABASE_POOL_SIZE,
            max_overflow=settings.DATABASE_MAX_OVERFLOW,
            echo=settings.DEBUG,
            future=True
        )
        
        # Criar session factory
        self.session_factory = async_sessionmaker(
            bind=self.engine,
            class_=AsyncSession,
            expire_on_commit=False,
            autoflush=False,
            autocommit=False
        )
        
        self._initialized = True
    
    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Obtém uma sessão de banco de dados."""
        if not self._initialized:
            await self.initialize()
        
        async with self.session_factory() as session:
            try:
                yield session
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()
    
    async def close(self):
        """Fecha as conexões do banco."""
        if self.engine:
            await self.engine.dispose()
        self._initialized = False


# Instância global do gerenciador de banco
db_manager = DatabaseManager()


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency para obter sessão de banco de dados.
    
    Uso:
        @app.get("/media")
        async def get_media(db: AsyncSession = Depends(get_db)):
            # usar db aqui
    """
    async for session in db_manager.get_session():
        yield session


async def init_db():
    """Inicializa o banco de dados."""
    await db_manager.initialize()


async def close_db():
    """Fecha as conexões do banco."""
    await db_manager.close()
