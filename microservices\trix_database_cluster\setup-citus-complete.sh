#!/bin/bash
# 🚀 Script Completo de Configuração Citus Distribuído - Trix
# Configura cluster Citus coordenado por microserviço

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para logging
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Verificar se Docker está rodando
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        error "Docker não está rodando. Por favor, inicie o Docker primeiro."
        exit 1
    fi
    log "Docker está rodando ✅"
}

# Verificar se rede existe
check_network() {
    if ! docker network ls | grep -q "trix-network"; then
        log "Criando rede trix-network..."
        docker network create trix-network
    else
        log "Rede trix-network já existe ✅"
    fi
}

# Criar diretórios necessários
create_directories() {
    log "Criando estrutura de diretórios..."
    
    mkdir -p citus-config
    mkdir -p citus-init-scripts/{core,tenant,shared}
    mkdir -p citus-scripts
    mkdir -p citus-backups
    
    log "Diretórios criados ✅"
}

# Criar arquivos de configuração
create_config_files() {
    log "Criando arquivos de configuração Citus..."
    
    # pg_hba.conf
    cat > citus-config/pg_hba.conf << 'EOF'
# TYPE  DATABASE        USER            ADDRESS                 METHOD
local   all             all                                     trust
host    all             all             127.0.0.1/32            md5
host    all             all             ::1/128                 md5
host    all             all             0.0.0.0/0               md5
host    replication     all             0.0.0.0/0               md5
EOF

    # Core Coordinator Config
    cat > citus-config/core-coordinator.conf << 'EOF'
# Core Services Coordinator Configuration
shared_preload_libraries = 'citus'
max_connections = 200
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB

# Citus Configuration
citus.shard_count = 32
citus.shard_replication_factor = 2
citus.use_secondary_nodes = 'never'
citus.cluster_name = 'trix-core'

# Replication
wal_level = replica
max_wal_senders = 10
max_replication_slots = 10
hot_standby = on

# Performance for critical data
synchronous_commit = on
fsync = on
full_page_writes = on

# Logging
log_destination = 'stderr'
logging_collector = on
log_directory = 'log'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_min_messages = info
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
EOF

    # Tenant Coordinator Config
    cat > citus-config/tenant-coordinator.conf << 'EOF'
# Tenant Services Coordinator Configuration
shared_preload_libraries = 'citus'
max_connections = 300
shared_buffers = 512MB
effective_cache_size = 2GB
work_mem = 8MB
maintenance_work_mem = 128MB

# Citus Configuration for multi-tenant
citus.shard_count = 64
citus.shard_replication_factor = 2
citus.use_secondary_nodes = 'always'
citus.cluster_name = 'trix-tenant'

# Optimizations for tenant partitioning
citus.enable_repartition_joins = on
citus.enable_fast_path_router_planner = on
citus.multi_shard_modify_mode = 'parallel'

# Replication
wal_level = replica
max_wal_senders = 10
max_replication_slots = 10

# Logging
log_destination = 'stderr'
logging_collector = on
log_directory = 'log'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_min_messages = info
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
EOF

    # Shared Coordinator Config
    cat > citus-config/shared-coordinator.conf << 'EOF'
# Shared Services Coordinator Configuration
shared_preload_libraries = 'citus'
max_connections = 250
shared_buffers = 384MB
effective_cache_size = 1.5GB
work_mem = 6MB
maintenance_work_mem = 96MB

# Citus Configuration
citus.shard_count = 32
citus.shard_replication_factor = 2
citus.use_secondary_nodes = 'always'
citus.cluster_name = 'trix-shared'

# Replication
wal_level = replica
max_wal_senders = 10
max_replication_slots = 10

# Logging
log_destination = 'stderr'
logging_collector = on
log_directory = 'log'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_min_messages = info
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
EOF

    # Worker Config
    cat > citus-config/worker.conf << 'EOF'
# Citus Worker Configuration
shared_preload_libraries = 'citus'
max_connections = 100
shared_buffers = 512MB
effective_cache_size = 2GB
work_mem = 8MB
maintenance_work_mem = 64MB

# Replication
wal_level = replica
max_wal_senders = 5
max_replication_slots = 5

# Logging
log_destination = 'stderr'
logging_collector = on
log_directory = 'log'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_min_messages = info
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
EOF

    log "Arquivos de configuração criados ✅"
}

# Criar scripts de inicialização
create_init_scripts() {
    log "Criando scripts de inicialização..."
    
    # Core Services Init
    cat > citus-init-scripts/core/01-create-databases.sql << 'EOF'
-- Core Services Databases
CREATE DATABASE auth_db;
CREATE DATABASE users_db;
CREATE DATABASE tenants_db;
CREATE DATABASE core_db;
CREATE DATABASE i18n_db;
CREATE DATABASE ghost_function_db;
EOF

    # Tenant Services Init
    cat > citus-init-scripts/tenant/01-create-databases.sql << 'EOF'
-- Tenant Services Databases
CREATE DATABASE restaurant_db;
CREATE DATABASE consultancy_db;
CREATE DATABASE shop_db;
EOF

    # Shared Services Init
    cat > citus-init-scripts/shared/01-create-databases.sql << 'EOF'
-- Shared Services Databases
CREATE DATABASE hr_db;
CREATE DATABASE crm_db;
CREATE DATABASE email_db;
CREATE DATABASE financial_db;
CREATE DATABASE notifications_db;
CREATE DATABASE media_db;
EOF

    log "Scripts de inicialização criados ✅"
}

# Criar configuração do PgAdmin para Citus
create_pgadmin_config() {
    log "Criando configuração do PgAdmin..."
    
    cat > citus-config/citus-servers.json << 'EOF'
{
    "Servers": {
        "1": {
            "Name": "Trix Core Coordinator",
            "Group": "Citus Coordinators",
            "Host": "trix-citus-core-coordinator",
            "Port": 5432,
            "MaintenanceDB": "postgres",
            "Username": "postgres",
            "SSLMode": "prefer",
            "SSLCert": "<STORAGE_DIR>/.postgresql/postgresql.crt",
            "SSLKey": "<STORAGE_DIR>/.postgresql/postgresql.key",
            "SSLCompression": 0,
            "Timeout": 10,
            "UseSSHTunnel": 0,
            "TunnelPort": "22",
            "TunnelAuthentication": 0
        },
        "2": {
            "Name": "Trix Tenant Coordinator",
            "Group": "Citus Coordinators",
            "Host": "trix-citus-tenant-coordinator",
            "Port": 5432,
            "MaintenanceDB": "postgres",
            "Username": "postgres",
            "SSLMode": "prefer",
            "Timeout": 10
        },
        "3": {
            "Name": "Trix Shared Coordinator",
            "Group": "Citus Coordinators",
            "Host": "trix-citus-shared-coordinator",
            "Port": 5432,
            "MaintenanceDB": "postgres",
            "Username": "postgres",
            "SSLMode": "prefer",
            "Timeout": 10
        },
        "4": {
            "Name": "Trix Worker 1",
            "Group": "Citus Workers",
            "Host": "trix-citus-worker-1",
            "Port": 5432,
            "MaintenanceDB": "postgres",
            "Username": "postgres",
            "SSLMode": "prefer",
            "Timeout": 10
        },
        "5": {
            "Name": "Trix Worker 2",
            "Group": "Citus Workers",
            "Host": "trix-citus-worker-2",
            "Port": 5432,
            "MaintenanceDB": "postgres",
            "Username": "postgres",
            "SSLMode": "prefer",
            "Timeout": 10
        }
    }
}
EOF

    log "Configuração do PgAdmin criada ✅"
}

# Iniciar cluster Citus
start_citus_cluster() {
    log "Iniciando cluster Citus..."
    
    # Parar containers existentes se houver
    docker-compose -f docker-compose.citus.yml down 2>/dev/null || true
    
    # Iniciar cluster
    docker-compose -f docker-compose.citus.yml up -d
    
    log "Aguardando inicialização dos serviços..."
    sleep 30
    
    # Verificar status
    docker-compose -f docker-compose.citus.yml ps
    
    log "Cluster Citus iniciado ✅"
}

# Configurar cluster Citus
configure_citus_cluster() {
    log "Configurando cluster Citus..."
    
    # Executar script de configuração Python
    if [ -f "setup-citus-cluster.py" ]; then
        python3 setup-citus-cluster.py
        log "Configuração do cluster executada ✅"
    else
        warn "Script setup-citus-cluster.py não encontrado"
    fi
}

# Executar migrations
run_migrations() {
    log "Executando migrations distribuídas..."
    
    if [ -f "citus-migration-manager.py" ]; then
        python3 citus-migration-manager.py
        log "Migrations executadas ✅"
    else
        warn "Script citus-migration-manager.py não encontrado"
    fi
}

# Executar seeds
run_seeds() {
    log "Executando seeds distribuídos..."
    
    if [ -f "citus-seed-manager.py" ]; then
        python3 citus-seed-manager.py
        log "Seeds executados ✅"
    else
        warn "Script citus-seed-manager.py não encontrado"
    fi
}

# Verificar cluster
verify_cluster() {
    log "Verificando status do cluster..."
    
    # Verificar coordenadores
    info "Verificando coordenadores..."
    docker exec trix-citus-core-coordinator pg_isready -U postgres && log "Core Coordinator: OK" || error "Core Coordinator: FAIL"
    docker exec trix-citus-tenant-coordinator pg_isready -U postgres && log "Tenant Coordinator: OK" || error "Tenant Coordinator: FAIL"
    docker exec trix-citus-shared-coordinator pg_isready -U postgres && log "Shared Coordinator: OK" || error "Shared Coordinator: FAIL"
    
    # Verificar workers
    info "Verificando workers..."
    docker exec trix-citus-worker-1 pg_isready -U postgres && log "Worker 1: OK" || error "Worker 1: FAIL"
    docker exec trix-citus-worker-2 pg_isready -U postgres && log "Worker 2: OK" || error "Worker 2: FAIL"
    
    log "Verificação do cluster concluída ✅"
}

# Função principal
main() {
    log "🚀 Iniciando configuração completa do Citus Distribuído - Trix"
    
    check_docker
    check_network
    create_directories
    create_config_files
    create_init_scripts
    create_pgadmin_config
    start_citus_cluster
    configure_citus_cluster
    run_migrations
    run_seeds
    verify_cluster
    
    log "✅ Configuração do Citus Distribuído concluída com sucesso!"
    info "🌐 PgAdmin disponível em: http://localhost:8082"
    info "📊 Core Coordinator: localhost:5432"
    info "🏢 Tenant Coordinator: localhost:5433"
    info "🔗 Shared Coordinator: localhost:5434"
    info "⚙️ Worker 1: localhost:5435"
    info "⚙️ Worker 2: localhost:5436"
}

# Executar função principal
main "$@"
