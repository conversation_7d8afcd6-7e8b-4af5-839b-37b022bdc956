"""
Circuit breaker implementation for service integrations.
This module now imports from shared_lib for consistency.
"""

# Import from shared_lib
from microservices.core.shared_lib.config.infrastructure.integration import (
    CircuitBreaker as SharedCircuitBreaker,
    CircuitBreakerError as SharedCircuitBreakerError,
    CircuitBreakerState,
    CircuitBreakerManager as SharedCircuitBreakerManager,
    circuit_breaker_manager as shared_circuit_breaker_manager,
    with_circuit_breaker as shared_with_circuit_breaker
)

# Re-export for backward compatibility
CircuitBreaker = SharedCircuitBreaker
CircuitBreakerError = SharedCircuitBreakerError
CircuitBreakerManager = SharedCircuitBreakerManager
circuit_breaker_manager = shared_circuit_breaker_manager
with_circuit_breaker = shared_with_circuit_breaker


