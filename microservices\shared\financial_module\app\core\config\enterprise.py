"""
Enterprise configuration aggregator for Financial Service.
Re-exports the main configuration from the parent config module.
"""

# Import from the main config module
from ...config import (
    EnterpriseConfig,
    enterprise_config,
    get_citus_config,
    get_messaging_config,
    get_performance_config,
    get_service_integration_config,
    is_feature_enabled,
    get_database_url
)

# Import shared configurations directly from shared_lib
from microservices.core.shared_lib.config import (
    get_vault_config,
    get_observability_settings,
    get_security_settings,
    get_financial_settings
)

__all__ = [
    "EnterpriseConfig",
    "enterprise_config",
    "get_citus_config", 
    "get_messaging_config",
    "get_performance_config",
    "get_service_integration_config",
    "is_feature_enabled",
    "get_database_url",
    "get_vault_config",
    "get_observability_settings",
    "get_security_settings",
    "get_financial_settings"
]
