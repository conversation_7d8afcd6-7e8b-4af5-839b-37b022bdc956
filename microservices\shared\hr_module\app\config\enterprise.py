"""
Enterprise Configuration for HR Module.
Centralized configuration for all enterprise components.
"""

import os
from typing import Dict, Any, List, Optional
from microservices.core.shared_lib.config.hr_config import (
    get_hr_settings,
    HRServiceSettings,
    HR_GAMIFICATION_CONFIG,
    HR_DEPARTMENT_TEMPLATES
)
from microservices.core.shared_lib.config.vault_config import VaultBaseSettings
from microservices.core.shared_lib.config.kafka_config import KafkaSettings
from microservices.core.shared_lib.config.security_config import SecuritySettings
from microservices.core.shared_lib.config.database_config import DatabaseSettings
from pydantic import Field
from pydantic_settings import BaseSettings


# Vault configuration is now handled by shared_lib
# Use: vault_settings = VaultBaseSettings() or hr_settings.vault_settings


# Database configuration is now handled by shared_lib
# Use: database_settings = DatabaseSettings() or hr_settings.database_settings


# Kafka configuration is now handled by shared_lib
# Use: kafka_settings = KafkaSettings() or hr_settings.kafka_settings


class RabbitMQConfig(BaseSettings):
    """RabbitMQ configuration."""
    
    connection_url: str = Field(
        default="amqp://hr-module:password@rabbitmq:5672/",
        env="RABBITMQ_CONNECTION_URL"
    )
    exchange_name: str = Field(default="hr.events", env="RABBITMQ_EXCHANGE_NAME")
    queue_name: str = Field(default="hr.notifications", env="RABBITMQ_QUEUE_NAME")
    max_retries: int = Field(default=3, env="RABBITMQ_MAX_RETRIES")
    retry_delay: float = Field(default=1.0, env="RABBITMQ_RETRY_DELAY")


class RedisConfig(BaseSettings):
    """Redis configuration."""
    
    url: str = Field(default="redis://redis:6379/14", env="REDIS_URL")
    stream_prefix: str = Field(default="hr:stream:", env="REDIS_STREAM_PREFIX")
    consumer_group: str = Field(default="hr-module", env="REDIS_CONSUMER_GROUP")
    consumer_name: str = Field(default="hr-consumer-1", env="REDIS_CONSUMER_NAME")
    max_len: int = Field(default=10000, env="REDIS_STREAM_MAX_LEN")
    block_time: int = Field(default=1000, env="REDIS_BLOCK_TIME")


class ObservabilityConfig(BaseSettings):
    """Observability stack configuration."""
    
    # Jaeger
    jaeger_endpoint: str = Field(
        default="http://jaeger:14268/api/traces",
        env="JAEGER_ENDPOINT"
    )
    service_name: str = Field(default="hr-module", env="SERVICE_NAME")
    service_version: str = Field(default="2.0.0", env="SERVICE_VERSION")
    environment: str = Field(default="production", env="ENVIRONMENT")
    
    # Prometheus
    metrics_port: int = Field(default=9090, env="METRICS_PORT")
    metrics_path: str = Field(default="/metrics", env="METRICS_PATH")
    
    # Logging
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    structured_logging: bool = Field(default=True, env="STRUCTURED_LOGGING")
    elk_endpoint: str = Field(default="http://elasticsearch:9200", env="ELK_ENDPOINT")


class ServiceIntegrationConfig(BaseSettings):
    """Service integration configuration."""
    
    # Auth Service
    auth_service_url: str = Field(
        default="http://auth-service:8001",
        env="AUTH_SERVICE_URL"
    )
    auth_service_api_key: str = Field(default="", env="AUTH_SERVICE_API_KEY")
    
    # I18n Service
    i18n_service_url: str = Field(
        default="http://i18n-service:8006",
        env="I18N_SERVICE_URL"
    )
    i18n_service_api_key: str = Field(default="", env="I18N_SERVICE_API_KEY")
    
    # Notification Service
    notification_service_url: str = Field(
        default="http://notification-service:8007",
        env="NOTIFICATION_SERVICE_URL"
    )
    notification_service_api_key: str = Field(default="", env="NOTIFICATION_SERVICE_API_KEY")
    
    # CDN Service
    cdn_service_url: str = Field(
        default="http://cdn-service:8009",
        env="CDN_SERVICE_URL"
    )
    cdn_service_api_key: str = Field(default="", env="CDN_SERVICE_API_KEY")
    
    # Payment Service
    payment_service_url: str = Field(
        default="http://payment-service:8011",
        env="PAYMENT_SERVICE_URL"
    )
    payment_service_api_key: str = Field(default="", env="PAYMENT_SERVICE_API_KEY")
    
    # Domain Service
    domain_service_url: str = Field(
        default="http://domain-service:8012",
        env="DOMAIN_SERVICE_URL"
    )
    domain_service_api_key: str = Field(default="", env="DOMAIN_SERVICE_API_KEY")
    
    # Email Module
    email_module_url: str = Field(
        default="http://email-module:8015",
        env="EMAIL_MODULE_URL"
    )
    email_module_api_key: str = Field(default="", env="EMAIL_MODULE_API_KEY")


# Security configuration is now handled by shared_lib
# Use: security_settings = SecuritySettings() or hr_settings.security_settings


class PerformanceConfig(BaseSettings):
    """Performance and scaling configuration."""
    
    # Connection Pooling
    db_pool_size: int = Field(default=20, env="DB_POOL_SIZE")
    db_max_overflow: int = Field(default=30, env="DB_MAX_OVERFLOW")
    db_pool_timeout: int = Field(default=30, env="DB_POOL_TIMEOUT")
    
    # Caching
    cache_ttl: int = Field(default=3600, env="CACHE_TTL")
    cache_max_size: int = Field(default=10000, env="CACHE_MAX_SIZE")
    
    # Auto-scaling
    min_replicas: int = Field(default=3, env="MIN_REPLICAS")
    max_replicas: int = Field(default=100, env="MAX_REPLICAS")
    target_cpu_utilization: int = Field(default=70, env="TARGET_CPU_UTILIZATION")
    target_memory_utilization: int = Field(default=80, env="TARGET_MEMORY_UTILIZATION")


class EnterpriseConfig(BaseSettings):
    """Enterprise-level configuration for HR Module using shared_lib."""
    
    # Service Info
    service_name: str = Field(default="hr-module", env="SERVICE_NAME")
    service_version: str = Field(default="2.0.0", env="SERVICE_VERSION")
    service_port: int = Field(default=8014, env="SERVICE_PORT")
    
    # Feature Flags
    enable_event_sourcing: bool = Field(default=True, env="ENABLE_EVENT_SOURCING")
    enable_citus_sharding: bool = Field(default=True, env="ENABLE_CITUS_SHARDING")
    enable_vault_integration: bool = Field(default=True, env="ENABLE_VAULT_INTEGRATION")
    enable_distributed_tracing: bool = Field(default=True, env="ENABLE_DISTRIBUTED_TRACING")
    enable_service_mesh: bool = Field(default=True, env="ENABLE_SERVICE_MESH")
    enable_auto_scaling: bool = Field(default=True, env="ENABLE_AUTO_SCALING")
    
    # HR-specific configurations from shared_lib
    hr_settings: HRServiceSettings = Field(default_factory=get_hr_settings)
    
    # Legacy configurations (to be migrated)
    rabbitmq: RabbitMQConfig = RabbitMQConfig()
    redis: RedisConfig = RedisConfig()
    observability: ObservabilityConfig = ObservabilityConfig()
    integrations: ServiceIntegrationConfig = ServiceIntegrationConfig()
    performance: PerformanceConfig = PerformanceConfig()
    
    @property
    def vault_settings(self) -> VaultBaseSettings:
        """Get Vault configuration from shared_lib."""
        return self.hr_settings.vault_settings
    
    @property
    def kafka_settings(self) -> KafkaSettings:
        """Get Kafka configuration from shared_lib."""
        return self.hr_settings.kafka_settings
    
    @property
    def security_settings(self) -> SecuritySettings:
        """Get Security configuration from shared_lib."""
        return self.hr_settings.security_settings
    
    @property
    def database_settings(self) -> DatabaseSettings:
        """Get Database configuration from shared_lib."""
        return self.hr_settings.database_settings
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# Global configuration instance
enterprise_config = EnterpriseConfig()


def get_enterprise_config() -> EnterpriseConfig:
    """Get enterprise configuration instance."""
    return enterprise_config


def get_vault_config() -> VaultBaseSettings:
    """Get Vault configuration from shared_lib."""
    return enterprise_config.vault_settings


def get_database_config() -> DatabaseSettings:
    """Get Database configuration from shared_lib."""
    return enterprise_config.database_settings


def get_kafka_config() -> KafkaSettings:
    """Get Kafka configuration from shared_lib."""
    return enterprise_config.kafka_settings


def get_messaging_config() -> Dict[str, Any]:
    """Get messaging configuration."""
    return {
        "kafka": enterprise_config.kafka_settings.dict(),
        "rabbitmq": enterprise_config.rabbitmq.dict(),
        "redis": enterprise_config.redis.dict()
    }


def get_observability_config() -> ObservabilityConfig:
    """Get observability configuration."""
    return enterprise_config.observability


def get_security_config() -> SecuritySettings:
    """Get security configuration from shared_lib."""
    return enterprise_config.security_settings


def get_hr_config() -> HRServiceSettings:
    """Get HR-specific configuration from shared_lib."""
    return enterprise_config.hr_settings
