"""Database configuration for Financial Service"""

from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
import redis
from .config import enterprise_config
from microservices.core.shared_lib.config import get_redis_settings

# SQLAlchemy setup
engine = create_engine(
    enterprise_config.citus.coordinator_url,
    echo=enterprise_config.citus.echo,
    poolclass=StaticPool,
    connect_args={
        "check_same_thread": False
    } if "sqlite" in enterprise_config.citus.coordinator_url else {}
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()

# Redis setup
redis_settings = get_redis_settings()
redis_client = redis.from_url(redis_settings.url, decode_responses=True)


def get_db():
    """Get database session."""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def get_redis():
    """Get Redis client."""
    return redis_client


# Database dependency
async def get_database():
    """Async database dependency."""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


# Redis dependency
async def get_redis_client():
    """Async Redis dependency."""
    return redis_client
