"""
Ghost Function Service - Settings Configuration

Configurações específicas do Ghost Function Service que estendem as configurações
da shared_lib para funcionalidades específicas de proxy e resiliência.

Author: Trix Platform Team
"""

from typing import List, Optional
from pydantic import Field
from microservices.core.shared_lib.config import (
    VaultBaseSettings,
    GhostSettings,
    get_ghost_settings,
    get_service_timeout_config,
    DatabaseSettings,
    RedisSettings,
    KafkaSettings,
    SecuritySettings,
    LoggingSettings
)


class GhostFunctionSettings(VaultBaseSettings):
    """Ghost Function Service specific settings."""
    
    # Service identification (override base)
    SERVICE_NAME: str = Field(default="ghost-function-service", env="SERVICE_NAME")
    SERVICE_VERSION: str = Field(default="3.0.0", env="SERVICE_VERSION")
    SERVICE_PORT: int = Field(default=8026, env="SERVICE_PORT")
    
    # Ghost Function specific settings
    max_concurrent_ghosts: int = Field(
        default=50,
        env="GHOST_MAX_CONCURRENT_GHOSTS",
        description="Maximum number of concurrent ghost functions"
    )
    
    ghost_instance_ttl_seconds: int = Field(
        default=300,
        env="GHOST_INSTANCE_TTL_SECONDS", 
        description="Time to live for ghost instances"
    )
    
    proxy_timeout_ms: int = Field(
        default=30000,
        env="GHOST_PROXY_TIMEOUT_MS",
        description="Proxy timeout in milliseconds"
    )
    
    # Failsafe settings
    enable_distributed_backup: bool = Field(
        default=True,
        env="GHOST_ENABLE_DISTRIBUTED_BACKUP",
        description="Enable distributed backup instances"
    )
    
    backup_regions: List[str] = Field(
        default=["us-east", "eu-west", "asia-pacific"],
        env="GHOST_BACKUP_REGIONS",
        description="Backup regions for distributed instances"
    )
    
    # Self-healing settings
    enable_self_healing: bool = Field(
        default=True,
        env="GHOST_ENABLE_SELF_HEALING",
        description="Enable self-healing capabilities"
    )
    
    self_healing_check_interval_seconds: int = Field(
        default=60,
        env="GHOST_SELF_HEALING_CHECK_INTERVAL_SECONDS",
        description="Self-healing check interval"
    )

    # Health check settings
    health_check_timeout_seconds: int = Field(
        default=30,
        env="GHOST_HEALTH_CHECK_TIMEOUT_SECONDS",
        description="Health check timeout in seconds"
    )
    
    # Shared configurations (from shared_lib)
    ghost_config: GhostSettings = Field(default_factory=get_ghost_settings)
    database_config: DatabaseSettings = Field(default_factory=DatabaseSettings)
    redis_config: RedisSettings = Field(default_factory=RedisSettings)
    kafka_config: KafkaSettings = Field(default_factory=KafkaSettings)
    security_config: SecuritySettings = Field(default_factory=SecuritySettings)
    logging_config: LoggingSettings = Field(default_factory=LoggingSettings)
    
    def get_service_timeout(self, service_name: str) -> int:
        """Get timeout configuration for a specific service."""
        config = get_service_timeout_config(service_name)
        return config.get("primary_timeout_ms", self.ghost_config.default_timeout_ms)
    
    def is_critical_service(self, service_name: str) -> bool:
        """Check if a service is marked as critical."""
        config = get_service_timeout_config(service_name)
        return config.get("priority") == "critical"
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Global settings instance
settings = GhostFunctionSettings()


def get_settings() -> GhostFunctionSettings:
    """Get the global settings instance."""
    return settings
