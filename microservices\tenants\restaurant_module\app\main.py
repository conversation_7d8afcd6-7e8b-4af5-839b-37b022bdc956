"""
Restaurant Module - Enterprise Microservice v2.0
================================================

Enterprise-grade restaurant management microservice with event-driven architecture,
Citus Data sharding, service mesh integration, and comprehensive observability.

Features:
- Digital Menu Management with real-time sync
- Kitchen Display System (KDS) with WebSocket updates
- Order Management with event sourcing
- Table Management & Reservations
- Delivery Management with route optimization
- Inventory Management with predictive analytics
- Point of Sale (POS) integration
- Restaurant Analytics & Reporting
- Multi-tenant isolation with Citus Data sharding
- Event-driven integration with 16+ microservices
"""

import asyncio
import logging
from contextlib import asynccontextmanager
# Force reload
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from prometheus_fastapi_instrumentator import Instrumentator

# Core enterprise components
from .core.config.settings import settings
from .core.messaging.kafka import kafka_manager
from .core.observability.metrics import metrics_manager
from .core.observability.tracing import setup_tracing
from .core.observability.logging import setup_logging

# Shared Library Integration
from .core.shared_lib_integration import get_shared_lib_status

# API routers
from .api.v1 import (
    menu_router, orders_router, tables_router, kitchen_router,
    delivery_router, inventory_router, reservations_router,
    pos_router, analytics_router, health_router
)

# Integration services
from .services.integrations import (
    user_integration, tenant_integration,
    auth_integration, notification_integration, payment_integration,
    supplier_integration, media_integration, i18n_integration,
    hr_integration, crm_integration
)

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan manager for startup and shutdown events.
    Optimized for fast startup with graceful degradation.
    """
    # Startup
    logger.info(f"Starting {settings.service_name} v{settings.service_version}")

    try:
        # Setup observability first (lightweight)
        setup_tracing()
        setup_logging()

        # Initialize core components with reduced timeouts
        core_tasks = []

        # Initialize metrics (lightweight)
        core_tasks.append(asyncio.create_task(
            _safe_initialize(metrics_manager, "Metrics Manager")
        ))

        # Initialize Kafka with timeout (can be slow)
        core_tasks.append(asyncio.create_task(
            _safe_initialize(kafka_manager, "Kafka Manager", timeout=10)
        ))

        # Wait for core components with timeout
        await asyncio.wait_for(asyncio.gather(*core_tasks, return_exceptions=True), timeout=15)

        # Initialize integrations in background (non-blocking)
        asyncio.create_task(_initialize_integrations_background())

        logger.info("Restaurant Module core infrastructure initialized - starting in fast mode")
        yield

    except Exception as e:
        logger.warning(f"Some components failed to initialize, starting in degraded mode: {e}")
        # Continue startup even if some components fail
        yield
    finally:
        # Shutdown
        logger.info("Shutting down Restaurant Module...")
        await _safe_shutdown(kafka_manager, "Kafka Manager")
        await _safe_shutdown(metrics_manager, "Metrics Manager")
        logger.info("Restaurant Module shutdown complete")


async def _safe_initialize(component, name: str, timeout: int = 5):
    """Safely initialize a component with timeout and error handling."""
    try:
        await asyncio.wait_for(component.initialize(), timeout=timeout)
        logger.info(f"{name} initialized successfully")
    except asyncio.TimeoutError:
        logger.warning(f"{name} initialization timed out after {timeout}s")
    except Exception as e:
        logger.warning(f"{name} initialization failed: {e}")


async def _safe_shutdown(component, name: str):
    """Safely shutdown a component with error handling."""
    try:
        await component.shutdown()
        logger.info(f"{name} shutdown successfully")
    except Exception as e:
        logger.warning(f"{name} shutdown failed: {e}")


async def _initialize_integrations_background():
    """Initialize integrations in background without blocking startup."""
    logger.info("Starting background initialization of integrations...")

    integrations = [
        ("User", user_integration),
        ("Tenant", tenant_integration),
        ("Auth", auth_integration),
        ("Notification", notification_integration),
        ("Payment", payment_integration),
        ("Supplier", supplier_integration),
        ("Media", media_integration),
        ("I18n", i18n_integration),
        ("HR", hr_integration),
        ("CRM", crm_integration)
    ]

    # Initialize integrations with individual timeouts
    for name, integration in integrations:
        try:
            await asyncio.wait_for(integration.initialize(), timeout=5)
            logger.info(f"{name} integration initialized")
        except asyncio.TimeoutError:
            logger.warning(f"{name} integration timed out - will retry later")
        except Exception as e:
            logger.warning(f"{name} integration failed: {e} - will retry later")

        # Small delay between initializations to avoid overwhelming services
        await asyncio.sleep(0.1)

    logger.info("Background integration initialization completed")


# FastAPI application with enterprise configuration
app = FastAPI(
    title="Restaurant Module",
    description="Enterprise restaurant management microservice with event-driven architecture",
    version=settings.service_version,
    docs_url="/docs" if getattr(settings, 'environment', 'development') != "production" else None,
    redoc_url="/redoc" if getattr(settings, 'environment', 'development') != "production" else None,
    lifespan=lifespan,
    openapi_tags=[
        {"name": "Menu", "description": "Digital menu management"},
        {"name": "Orders", "description": "Order processing and tracking"},
        {"name": "Tables", "description": "Table management and reservations"},
        {"name": "Kitchen", "description": "Kitchen Display System (KDS)"},
        {"name": "Delivery", "description": "Delivery management"},
        {"name": "Inventory", "description": "Ingredient inventory management"},
        {"name": "POS", "description": "Point of Sale operations"},
        {"name": "Analytics", "description": "Restaurant analytics and reporting"},
        {"name": "Health", "description": "Service health and monitoring"},
    ]
)

# Enterprise middleware stack
environment = getattr(settings, 'environment', getattr(settings, 'ENVIRONMENT', 'development'))
if environment == "production":
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["*.trix.com", "localhost"]
    )

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"] if getattr(settings, 'environment', 'development') == "development" else ["https://*.trix.com"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "PATCH"],
    allow_headers=["*"],
)

# Prometheus metrics instrumentation
if settings.prometheus_enabled:
    instrumentator = Instrumentator(
        should_group_status_codes=False,
        should_ignore_untemplated=True,
        should_respect_env_var=True,
        should_instrument_requests_inprogress=True,
        excluded_handlers=["/health", "/metrics"],
        env_var_name="ENABLE_METRICS",
        inprogress_name="restaurant_requests_inprogress",
        inprogress_labels=True,
    )
    instrumentator.instrument(app).expose(app)

# API v1 routers (enterprise versioned APIs)
app.include_router(health_router, prefix="/api/v1/restaurant", tags=["Health"])
app.include_router(menu_router, prefix="/api/v1/restaurant", tags=["Menu"])
app.include_router(orders_router, prefix="/api/v1/restaurant", tags=["Orders"])
app.include_router(tables_router, prefix="/api/v1/restaurant", tags=["Tables"])
app.include_router(kitchen_router, prefix="/api/v1/restaurant", tags=["Kitchen"])
app.include_router(delivery_router, prefix="/api/v1/restaurant", tags=["Delivery"])
app.include_router(inventory_router, prefix="/api/v1/restaurant", tags=["Inventory"])
app.include_router(reservations_router, prefix="/api/v1/restaurant", tags=["Tables"])
app.include_router(pos_router, prefix="/api/v1/restaurant", tags=["POS"])
app.include_router(analytics_router, prefix="/api/v1/restaurant", tags=["Analytics"])

# Shared Library Integration - Status Check Only
# Following core services pattern - no dynamic router inclusion
shared_lib_status = get_shared_lib_status()

# Legacy API support (temporary)
from .api import restaurants_api_router
app.include_router(
    restaurants_api_router,
    prefix="/api/v1",
    tags=["Restaurant Management (Legacy)"]
)


@app.middleware("http")
async def add_enterprise_headers(request: Request, call_next):
    """Add enterprise headers to all responses."""
    response = await call_next(request)
    response.headers["X-Service-Name"] = settings.service_name
    response.headers["X-Service-Version"] = settings.service_version
    response.headers["X-Request-ID"] = str(request.headers.get("X-Request-ID", ""))
    return response


@app.get("/health", tags=["Health"])
async def health_check():
    """
    Enterprise health check endpoint with dependency status.
    """
    # Get shared library integration status
    shared_lib_health = get_shared_lib_status()

    health_status = {
        "status": "healthy",
        "service": settings.service_name,
        "version": settings.service_version,
        "environment": settings.environment,
        "timestamp": "2025-01-16T10:00:00Z",
        "dependencies": {
            "database": "healthy",  # TODO: Implement actual health checks
            "kafka": "healthy" if kafka_manager.is_initialized else "unhealthy",
            "redis": "healthy",
            "vault": "healthy"
        },
        "shared_lib_integration": shared_lib_health,
        "metrics": {
            "uptime_seconds": 0,  # TODO: Implement uptime tracking
            "memory_usage_mb": 0,  # TODO: Implement memory tracking
            "active_connections": 0  # TODO: Implement connection tracking
        }
    }

    # Determine overall status
    dependency_statuses = list(health_status["dependencies"].values())
    if "unhealthy" in dependency_statuses:
        health_status["status"] = "degraded"

    return health_status


@app.get("/", tags=["Health"])
async def root():
    """Root endpoint with service information."""
    return {
        "message": f"{settings.service_name} is running",
        "service": settings.service_name,
        "version": settings.service_version,
        "environment": settings.environment,
        "documentation": "/docs" if settings.environment != "production" else None,
        "health": "/health",
        "metrics": "/metrics" if settings.prometheus_enabled else None
    }


@app.get("/info", tags=["Health"])
async def service_info():
    """Get comprehensive service information and capabilities."""
    return {
        "service": settings.service_name,
        "description": "Enterprise restaurant management microservice with event-driven architecture",
        "version": settings.service_version,
        "environment": settings.environment,
        "port": settings.service_port,
        "architecture": {
            "pattern": "Event-Driven Microservices",
            "database": "PostgreSQL + Citus Data (Sharded)",
            "messaging": ["Apache Kafka", "RabbitMQ", "Redis Streams"],
            "service_mesh": "Istio/Linkerd with mTLS",
            "secrets": "HashiCorp Vault",
            "monitoring": ["Prometheus", "Grafana", "Jaeger", "ELK"],
            "orchestration": "Kubernetes + Helm + ArgoCD"
        },
        "capabilities": {
            "menu_management": "Digital menu with real-time sync",
            "order_processing": "Event-sourced order management",
            "kitchen_display": "Real-time KDS with WebSocket updates",
            "table_management": "Smart table allocation and reservations",
            "delivery_management": "Route optimization and tracking",
            "inventory_management": "Predictive analytics and alerts",
            "pos_integration": "Multi-payment POS system",
            "analytics": "Real-time restaurant analytics",
            "multi_tenancy": "Citus Data sharded isolation"
        },
        "integrations": {
            "core_services": [
                "Auth Service", "User Service", "Tenant Service",
                "Payment Service", "Notification Service",
                "CDN Service", "Media System", "Domain Service", "I18n Service"
            ],
            "shared_modules": [
                "HR Module", "CRM Module", "Financial Module", "Email Module"
            ],
            "tenant_modules": [
                "Consultancy Module"
            ]
        },
        "api_endpoints": {
            "v1": {
                "menu": "/api/v1/restaurant/menu",
                "orders": "/api/v1/restaurant/orders",
                "tables": "/api/v1/restaurant/tables",
                "kitchen": "/api/v1/restaurant/kitchen",
                "delivery": "/api/v1/restaurant/delivery",
                "inventory": "/api/v1/restaurant/inventory",
                "reservations": "/api/v1/restaurant/reservations",
                "pos": "/api/v1/restaurant/pos",
                "analytics": "/api/v1/restaurant/analytics"
            }
        },
        "event_topics": {
            "kafka": [
                "restaurant.order.created", "restaurant.order.updated",
                "restaurant.menu.updated", "restaurant.table.status.changed",
                "restaurant.kitchen.order.ready", "restaurant.delivery.assigned"
            ],
            "rabbitmq": [
                "restaurant.notifications", "restaurant.kitchen.alerts",
                "restaurant.table.alerts", "restaurant.inventory.alerts"
            ]
        },
        "supported_tenant_types": ["restaurant"],
        "user_roles": [
            "owner", "manager", "chef", "waiter", "cashier", "delivery", "customer"
        ],
        "compliance": {
            "data_protection": "GDPR/LGPD compliant",
            "security": "SOC 2 Type II",
            "availability": "99.9% SLA"
        }
    }


# Development server
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=settings.service_port,
        reload=settings.environment == "development",
        log_level=settings.log_level.lower()
    )
