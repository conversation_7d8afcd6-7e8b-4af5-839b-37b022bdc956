import logging
from fastapi import Fast<PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import PlainTextResponse

# Setup basic logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


app = FastAPI(
    title="Supplier Service",
    description="Enterprise-grade supplier and vendor management service for Trix platform",
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Health check endpoints
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "service": "supplier-service", "port": 8017}

@app.get("/health/ready")
async def readiness_check():
    """Readiness check endpoint for Kubernetes."""
    return {"status": "ready", "service": "supplier-service"}

@app.get("/health/live")
async def liveness_check():
    """Liveness check endpoint for Kubernetes."""
    return {"status": "alive", "service": "supplier-service"}

@app.get("/")
async def root():
    """Root endpoint."""
    return {"message": "Supplier Service is running", "version": "2.0.0", "port": 8017}

@app.get("/info")
async def service_info():
    """Get service information."""
    return {
        "service": "supplier-service",
        "version": "2.0.0",
        "port": 8017,
        "description": "Supplier and vendor management service",
        "features": [
            "Supplier Management",
            "Tenant Supplier Relationships",
            "TVendor Supplier Authorization",
            "Purchase Order Management",
            "Supplier Performance Analytics",
            "Inventory Integration",
            "Multi-tenant Support",
            "B2B Marketplace Integration"
        ],
        "supplier_types": [
            "Tenant Supplier",
            "TVendor Supplier"
        ],
        "supported_operations": [
            "Supplier Registration",
            "Product Catalog Management",
            "Purchase Orders",
            "Inventory Sync",
            "Performance Tracking",
            "Authorization Management"
        ]
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8017)
