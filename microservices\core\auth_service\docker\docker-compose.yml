# ============================================================================
# AUTH SERVICE - Microserviço de Autenticação
# ============================================================================
# Porta: 8001 | Database: postgres-auth (5432) | Redis: DB 0
# Submódulos: Autenticação JWT, Sessions Management, Login/Logout, Password Reset
# Configurações: Busca variáveis do docker-compose.yml principal
# ============================================================================

# Importa variáveis globais do arquivo principal
x-environment: &global-environment
  # Core Configuration
  SECRET_KEY: ${SECRET_KEY}
  DEBUG: ${DEBUG}
  ENVIRONMENT: ${ENVIRONMENT:-development}
  SERVICE_NAME: auth-service
  SERVICE_VERSION: 2.0.0
  SERVICE_PORT: 8001
  LOG_LEVEL: ${LOG_LEVEL:-INFO}

  # Database Configuration (Citus Core Coordinator)
  AUTH_DB_NAME: auth_db
  AUTH_DB_USER: postgres
  AUTH_DB_PASSWORD: TrixSuperSecure2024!
  AUTH_DB_HOST: trix-citus-core-coordinator
  AUTH_DB_PORT: 5432
  DATABASE_POOL_SIZE: ${DATABASE_POOL_SIZE:-20}
  DATABASE_MAX_OVERFLOW: ${DATABASE_MAX_OVERFLOW:-30}

  # Redis Cluster Configuration
  REDIS_PASSWORD: ${REDIS_PASSWORD}
  REDIS_CLUSTER_NODES: ${REDIS_CLUSTER_NODES:-trix-redis-cluster-node-1:7000,trix-redis-cluster-node-2:7001,trix-redis-cluster-node-3:7002}

  # JWT Configuration
  JWT_SECRET_KEY: ${JWT_SECRET_KEY}
  JWT_ALGORITHM: ${JWT_ALGORITHM:-RS256}
  JWT_EXPIRATION_HOURS: ${JWT_EXPIRATION_HOURS:-0.5}
  ACCESS_TOKEN_EXPIRE_MINUTES: ${ACCESS_TOKEN_EXPIRE_MINUTES:-30}
  REFRESH_TOKEN_EXPIRE_DAYS: ${REFRESH_TOKEN_EXPIRE_DAYS:-7}
  TOKEN_ROTATION_INTERVAL: ${TOKEN_ROTATION_INTERVAL:-24}

  # HashiCorp Vault Configuration
  VAULT_URL: ${VAULT_URL:-https://trix-vault:8200}
  VAULT_TOKEN: ${VAULT_TOKEN}
  VAULT_MOUNT_POINT: ${VAULT_MOUNT_POINT:-auth-service}

  # Service Mesh URLs
  USER_SERVICE_URL: ${USER_SERVICE_URL:-http://trix-user-service:8002}
  TENANT_SERVICE_URL: ${TENANT_SERVICE_URL:-http://trix-tenant-service:8003}
  NOTIFICATION_SERVICE_URL: ${NOTIFICATION_SERVICE_URL:-http://trix-notification-service:8019}

  # Messaging Configuration
  KAFKA_BOOTSTRAP_SERVERS: ${KAFKA_BOOTSTRAP_SERVERS:-trix-kafka-broker-1:9092,trix-kafka-broker-2:9092,trix-kafka-broker-3:9092}
  RABBITMQ_URL: ${RABBITMQ_URL:-amqp://auth_user:auth_pass@trix-rabbitmq-cluster:5672/auth}

  # Observability Configuration
  JAEGER_AGENT_HOST: ${JAEGER_AGENT_HOST:-trix-jaeger-agent}
  JAEGER_AGENT_PORT: ${JAEGER_AGENT_PORT:-6831}
  PROMETHEUS_METRICS_PORT: ${PROMETHEUS_METRICS_PORT:-9090}

  # Security Configuration
  RATE_LIMIT_PER_MINUTE: ${RATE_LIMIT_PER_MINUTE:-60}
  RATE_LIMIT_BURST: ${RATE_LIMIT_BURST:-10}
  MFA_ISSUER: ${MFA_ISSUER:-Trix Platform}
  PASSWORD_MIN_LENGTH: ${PASSWORD_MIN_LENGTH:-12}

  # CORS Configuration
  CORS_ORIGINS: ${CORS_ORIGINS:-http://localhost:3000,https://app.trix.local}

services:
  # ============================================================================
  # AUTH SERVICE - Serviço Principal Enterprise
  # ============================================================================
  trix-core-auth:
    build:
      context: ../
      dockerfile: docker/Dockerfile
      target: production
    container_name: trix-core-auth
    ports:
      - "8001:8001"
    environment:
      <<: *global-environment
      # Database URL with Citus Core Coordinator
      DATABASE_URL: postgresql+asyncpg://postgres:TrixSuperSecure2024!@trix-citus-core-coordinator:5432/auth_db
      # Redis Cluster URL
      REDIS_URL: redis://:${REDIS_PASSWORD}@trix-redis-cluster-node-1:7000/0
    depends_on:
      - trix-redis-cluster-node-1
      - trix-vault
    external_links:
      - trix-citus-core-coordinator:trix-citus-core-coordinator
    volumes:
      - ../app:/app/app:ro
      - vault-secrets:/vault/secrets:ro
      - auth-logs:/app/logs
    networks:
      - trix-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # ============================================================================
  # DATABASE - Using Citus Core Coordinator (External)
  # ============================================================================
  # Database removed - using external Citus Core Coordinator
  # Connection: trix-citus-core-coordinator:5432/auth_db

  # ============================================================================
  # REDIS CLUSTER - Distributed Cache
  # ============================================================================
  trix-redis-cluster-node-1:
    image: redis:7-alpine
    container_name: trix-redis-cluster-node-1
    command: redis-server --port 7000 --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000 --appendonly yes --requirepass ${REDIS_PASSWORD}
    ports:
      - "7000:7000"
    volumes:
      - redis-node-1-data:/data
    networks:
      - trix-network
    restart: unless-stopped

  trix-redis-cluster-node-2:
    image: redis:7-alpine
    container_name: trix-redis-cluster-node-2
    command: redis-server --port 7001 --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000 --appendonly yes --requirepass ${REDIS_PASSWORD}
    ports:
      - "7001:7001"
    volumes:
      - redis-node-2-data:/data
    networks:
      - trix-network
    restart: unless-stopped

  trix-redis-cluster-node-3:
    image: redis:7-alpine
    container_name: trix-redis-cluster-node-3
    command: redis-server --port 7002 --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000 --appendonly yes --requirepass ${REDIS_PASSWORD}
    ports:
      - "7002:7002"
    volumes:
      - redis-node-3-data:/data
    networks:
      - trix-network
    restart: unless-stopped

  # ============================================================================
  # HASHICORP VAULT - Secrets Management
  # ============================================================================
  trix-vault:
    image: hashicorp/vault:latest
    container_name: trix-vault
    cap_add:
      - IPC_LOCK
    environment:
      VAULT_DEV_ROOT_TOKEN_ID: ${VAULT_ROOT_TOKEN:-dev-root-token}
      VAULT_DEV_LISTEN_ADDRESS: 0.0.0.0:8200
    ports:
      - "8200:8200"
    volumes:
      - vault-data:/vault/data
      - vault-secrets:/vault/secrets
      - ./vault-config:/vault/config
    networks:
      - trix-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "vault", "status"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  redis-node-1-data:
  redis-node-2-data:
  redis-node-3-data:
  vault-data:
  vault-secrets:
  auth-logs:

networks:
  trix-network:
    external: true
    
