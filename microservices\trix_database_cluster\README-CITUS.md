# 🚀 Trix Citus Cluster Distribuído

## 📋 Visão Geral

Este sistema implementa um cluster Citus distribuído onde cada domínio de microserviços tem seu próprio coordenador, permitindo escalabilidade horizontal e isolamento de dados por domínio de negócio.

## 🏗️ Arquitetura

### Coordenadores por Domínio

- **Core Coordinator (5432)**: auth, user, tenant, core, i18n services
- **Tenant Coordinator (5433)**: restaurant, consultancy, shop modules  
- **Shared Coordinator (5434)**: hr, crm, email, financial modules

### Workers Compartilhados

- **Worker 1 (5435)**: Processa dados de todos os coordenadores
- **Worker 2 (5436)**: Processa dados de todos os coordenadores

## 🚀 Deploy Rápido

### Opção 1: Deploy Automático (Recomendado)

```bash
# Navegar para o diretório
cd microservices/trix_database_cluster

# Executar deploy completo
python3 deploy-citus-complete.py
```

### Opção 2: Deploy Manual

```bash
# 1. Configurar infraestrutura
bash setup-citus-complete.sh

# 2. Configurar cluster
python3 setup-citus-cluster.py

# 3. Executar migrations
python3 citus-migration-manager.py

# 4. Executar seeds
python3 citus-seed-manager.py

# 5. Atualizar microserviços
python3 update-microservices-citus.py
```

## 📦 Pré-requisitos

### Software Necessário

```bash
# Docker e Docker Compose
docker --version
docker-compose --version

# Python 3.8+
python3 --version

# Dependências Python
pip install asyncpg bcrypt
```

### Verificar Ambiente

```bash
# Verificar se rede existe
docker network ls | grep trix-network

# Criar rede se necessário
docker network create trix-network
```

## 🔧 Configuração Manual

### 1. Iniciar Cluster

```bash
# Iniciar todos os serviços
docker-compose -f docker-compose.citus.yml up -d

# Verificar status
docker-compose -f docker-compose.citus.yml ps
```

### 2. Configurar Coordenadores

```bash
# Configurar cluster Citus
python3 setup-citus-cluster.py
```

### 3. Executar Migrations

```bash
# Migrations distribuídas
python3 citus-migration-manager.py
```

### 4. Executar Seeds

```bash
# Seeds distribuídos
python3 citus-seed-manager.py
```

## 📊 Monitoramento

### Verificar Status do Cluster

```bash
# Status dos containers
docker-compose -f docker-compose.citus.yml ps

# Logs dos coordenadores
docker logs trix-citus-core-coordinator
docker logs trix-citus-tenant-coordinator
docker logs trix-citus-shared-coordinator

# Logs dos workers
docker logs trix-citus-worker-1
docker logs trix-citus-worker-2
```

### Conectar aos Coordenadores

```bash
# Core Coordinator
docker exec -it trix-citus-core-coordinator psql -U postgres -d postgres

# Tenant Coordinator  
docker exec -it trix-citus-tenant-coordinator psql -U postgres -d postgres

# Shared Coordinator
docker exec -it trix-citus-shared-coordinator psql -U postgres -d postgres
```

### Verificar Distribuição Citus

```sql
-- Verificar nós do cluster
SELECT * FROM pg_dist_node;

-- Verificar tabelas distribuídas
SELECT * FROM pg_dist_partition;

-- Verificar shards
SELECT * FROM pg_dist_shard;
```

## 🌐 Pontos de Acesso

### Coordenadores

- **Core Services**: `localhost:5432`
- **Tenant Services**: `localhost:5433`
- **Shared Services**: `localhost:5434`

### Workers

- **Worker 1**: `localhost:5435`
- **Worker 2**: `localhost:5436`

### Administração

- **PgAdmin**: `http://localhost:8082`
  - Email: `<EMAIL>`
  - Senha: `TrixAdmin2024!`

## 📋 Mapeamento de Microserviços

### Core Services (localhost:5432)

```
auth_service     → auth_db
user_service     → users_db
tenant_service   → tenants_db
core_service     → core_db
i18n_service     → i18n_db
```

### Tenant Services (localhost:5433)

```
restaurant_module   → restaurant_db
consultancy_module  → consultancy_db
shop_module        → shop_db
```

### Shared Services (localhost:5434)

```
hr_module          → hr_db
crm_module         → crm_db
email_module       → email_db
financial_module   → financial_db
```

## 🔄 Comandos Úteis

### Gerenciamento do Cluster

```bash
# Parar cluster
docker-compose -f docker-compose.citus.yml down

# Reiniciar cluster
docker-compose -f docker-compose.citus.yml restart

# Remover volumes (CUIDADO!)
docker-compose -f docker-compose.citus.yml down -v
```

### Backup e Restore

```bash
# Backup de um coordenador
docker exec trix-citus-core-coordinator pg_dump -U postgres -d auth_db > backup_auth.sql

# Restore
docker exec -i trix-citus-core-coordinator psql -U postgres -d auth_db < backup_auth.sql
```

### Logs e Debug

```bash
# Logs em tempo real
docker-compose -f docker-compose.citus.yml logs -f

# Logs de um serviço específico
docker-compose -f docker-compose.citus.yml logs -f trix-citus-core-coordinator
```

## 🛠️ Troubleshooting

### Problemas Comuns

#### 1. Containers não iniciam

```bash
# Verificar logs
docker-compose -f docker-compose.citus.yml logs

# Verificar recursos
docker system df
docker system prune
```

#### 2. Erro de conectividade

```bash
# Verificar rede
docker network inspect trix-network

# Verificar portas
netstat -tulpn | grep -E '543[2-6]'
```

#### 3. Erro nas migrations

```bash
# Verificar bancos criados
docker exec trix-citus-core-coordinator psql -U postgres -l

# Recriar bancos se necessário
docker exec trix-citus-core-coordinator psql -U postgres -c "DROP DATABASE IF EXISTS auth_db; CREATE DATABASE auth_db;"
```

#### 4. Workers não conectam

```bash
# Verificar workers no coordenador
docker exec trix-citus-core-coordinator psql -U postgres -c "SELECT * FROM pg_dist_node;"

# Readicionar worker se necessário
docker exec trix-citus-core-coordinator psql -U postgres -c "SELECT citus_add_node('trix-citus-worker-1', 5432);"
```

## 📈 Performance

### Configurações Otimizadas

- **Core Coordinator**: 256MB shared_buffers, 32 shards
- **Tenant Coordinator**: 512MB shared_buffers, 64 shards  
- **Shared Coordinator**: 384MB shared_buffers, 32 shards
- **Workers**: 512MB shared_buffers cada

### Monitoramento de Performance

```sql
-- Verificar distribuição de dados
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- Verificar queries ativas
SELECT * FROM pg_stat_activity WHERE state = 'active';
```

## 🔐 Segurança

### Credenciais Padrão

- **Usuário**: `postgres`
- **Senha**: `TrixSuperSecure2024!`

### Recomendações

1. Alterar senhas padrão em produção
2. Configurar SSL/TLS
3. Implementar firewall
4. Monitorar logs de acesso

## 📚 Documentação Adicional

- [Configuração Detalhada](citus-cluster-config.md)
- [Documentação Citus](https://docs.citusdata.com/)
- [PostgreSQL Docs](https://www.postgresql.org/docs/)

## 🎯 Próximos Passos

1. ✅ Cluster Citus configurado
2. ✅ Migrations distribuídas
3. ✅ Seeds distribuídos
4. 🔄 Configurar monitoramento avançado
5. 🔄 Implementar backup automático
6. 🔄 Configurar SSL/TLS
7. 🔄 Otimizar performance

---

**🎉 Cluster Citus Distribuído Trix está pronto para uso!**
