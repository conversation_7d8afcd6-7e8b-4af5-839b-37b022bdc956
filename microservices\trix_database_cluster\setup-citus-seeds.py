#!/usr/bin/env python3
"""
🌱 Setup Citus Seeds
Configura e executa seeds distribuídos no cluster Citus
"""

import asyncio
import asyncpg
import logging
import uuid
import bcrypt
from typing import Dict, List

# Configuração de logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Mapeamento de coordenadores e bancos
COORDINATORS_CONFIG = {
    'core': {
        'host': 'localhost',
        'port': 5432,
        'databases': {
            'auth_db': 'auth',
            'users_db': 'users', 
            'tenants_db': 'tenants'
        }
    },
    'shared': {
        'host': 'localhost',
        'port': 5433,
        'databases': {
            'crm_db': 'crm',
            'hr_db': 'hr'
        }
    },
    'tenant': {
        'host': 'localhost',
        'port': 5434,
        'databases': {
            'restaurant_db': 'restaurant',
            'consultancy_db': 'consultancy'
        }
    }
}

class CitusSeedSetup:
    """Configurador de seeds para Citus"""
    
    def __init__(self):
        self.user = 'postgres'
        self.password = 'TrixSuperSecure2024!'
        
    async def setup_citus_seeds(self):
        """Configura e executa seeds distribuídos"""
        logger.info("🌱 Iniciando configuração de seeds distribuídos...")
        
        try:
            # Executar seeds por ordem de dependência
            await self._seed_auth_service()
            await self._seed_users_service()
            await self._seed_tenants_service()
            await self._seed_restaurant_service()
            await self._seed_consultancy_service()
            await self._seed_crm_service()
            await self._seed_hr_service()
            
            logger.info("✅ Seeds distribuídos configurados com sucesso!")
            
        except Exception as e:
            logger.error(f"❌ Erro na configuração de seeds: {e}")
            raise
    
    async def _seed_auth_service(self):
        """Seeds para auth_service"""
        logger.info("🔐 Executando seeds do auth_service...")
        
        try:
            conn = await asyncpg.connect(
                host='localhost',
                port=5432,
                user=self.user,
                password=self.password,
                database='auth_db'
            )
            
            # Verificar se usuários já existem
            existing_users = await conn.fetchval("SELECT COUNT(*) FROM users WHERE email = '<EMAIL>'")
            if existing_users > 0:
                logger.info("ℹ️ Usuários já existem no auth_service")
                await conn.close()
                return
            
            # Criar usuários padrão conforme especificação
            users_data = [
                {
                    'id': str(uuid.uuid4()),
                    'email': '<EMAIL>',
                    'password_hash': self._hash_password('password')
                },
                {
                    'id': str(uuid.uuid4()),
                    'email': '<EMAIL>',
                    'password_hash': self._hash_password('password')
                },
                {
                    'id': str(uuid.uuid4()),
                    'email': '<EMAIL>',
                    'password_hash': self._hash_password('password')
                },
                {
                    'id': str(uuid.uuid4()),
                    'email': '<EMAIL>',
                    'password_hash': self._hash_password('password')
                },
                {
                    'id': str(uuid.uuid4()),
                    'email': '<EMAIL>',
                    'password_hash': self._hash_password('password')
                }
            ]
            
            for user in users_data:
                await conn.execute(
                    "INSERT INTO users (id, email, password_hash, is_active) VALUES ($1, $2, $3, $4)",
                    user['id'], user['email'], user['password_hash'], True
                )
            
            logger.info(f"✅ {len(users_data)} usuários criados no auth_service")
            await conn.close()
            
        except Exception as e:
            logger.error(f"❌ Erro no seed do auth_service: {e}")
    
    async def _seed_users_service(self):
        """Seeds para users_service"""
        logger.info("👥 Executando seeds do users_service...")
        
        try:
            conn = await asyncpg.connect(
                host='localhost',
                port=5432,
                user=self.user,
                password=self.password,
                database='users_db'
            )
            
            # Verificar se perfis já existem
            existing_profiles = await conn.fetchval("SELECT COUNT(*) FROM user_profiles")
            if existing_profiles > 0:
                logger.info("ℹ️ Perfis já existem no users_service")
                await conn.close()
                return
            
            # Criar perfis de usuário
            profiles_data = [
                {
                    'id': str(uuid.uuid4()),
                    'user_id': str(uuid.uuid4()),
                    'first_name': 'System',
                    'last_name': 'Administrator',
                    'phone': '+34 600 000 001'
                },
                {
                    'id': str(uuid.uuid4()),
                    'user_id': str(uuid.uuid4()),
                    'first_name': 'Restaurant',
                    'last_name': 'Owner',
                    'phone': '+34 600 000 002'
                },
                {
                    'id': str(uuid.uuid4()),
                    'user_id': str(uuid.uuid4()),
                    'first_name': 'Consultancy',
                    'last_name': 'Manager',
                    'phone': '+34 600 000 003'
                }
            ]
            
            for profile in profiles_data:
                await conn.execute(
                    "INSERT INTO user_profiles (id, user_id, first_name, last_name, phone) VALUES ($1, $2, $3, $4, $5)",
                    profile['id'], profile['user_id'], profile['first_name'], profile['last_name'], profile['phone']
                )
            
            logger.info(f"✅ {len(profiles_data)} perfis criados no users_service")
            await conn.close()
            
        except Exception as e:
            logger.error(f"❌ Erro no seed do users_service: {e}")
    
    async def _seed_tenants_service(self):
        """Seeds para tenants_service"""
        logger.info("🏢 Executando seeds do tenants_service...")
        
        try:
            conn = await asyncpg.connect(
                host='localhost',
                port=5432,
                user=self.user,
                password=self.password,
                database='tenants_db'
            )
            
            # Verificar se tenants já existem
            existing_tenants = await conn.fetchval("SELECT COUNT(*) FROM tenants")
            if existing_tenants > 0:
                logger.info("ℹ️ Tenants já existem no tenants_service")
                await conn.close()
                return
            
            # Criar tenants demo
            tenants_data = [
                {
                    'id': str(uuid.uuid4()),
                    'name': 'Test Restaurant',
                    'domain': 'restaurant.trix.local'
                },
                {
                    'id': str(uuid.uuid4()),
                    'name': 'Test Consultancy',
                    'domain': 'consultancy.trix.local'
                }
            ]
            
            for tenant in tenants_data:
                await conn.execute(
                    "INSERT INTO tenants (id, name, domain, is_active) VALUES ($1, $2, $3, $4)",
                    tenant['id'], tenant['name'], tenant['domain'], True
                )
            
            logger.info(f"✅ {len(tenants_data)} tenants criados no tenants_service")
            await conn.close()
            
        except Exception as e:
            logger.error(f"❌ Erro no seed do tenants_service: {e}")
    
    async def _seed_restaurant_service(self):
        """Seeds para restaurant_service"""
        logger.info("🍽️ Executando seeds do restaurant_service...")
        
        try:
            conn = await asyncpg.connect(
                host='localhost',
                port=5434,
                user=self.user,
                password=self.password,
                database='restaurant_db'
            )
            
            # Verificar se menus já existem
            existing_menus = await conn.fetchval("SELECT COUNT(*) FROM restaurant_menus")
            if existing_menus > 0:
                logger.info("ℹ️ Menus já existem no restaurant_service")
                await conn.close()
                return
            
            # Criar itens de menu demo
            tenant_id = str(uuid.uuid4())
            menus_data = [
                {
                    'id': str(uuid.uuid4()),
                    'tenant_id': tenant_id,
                    'name': 'Jamón Ibérico',
                    'price': 18.50
                },
                {
                    'id': str(uuid.uuid4()),
                    'tenant_id': tenant_id,
                    'name': 'Paella Valenciana',
                    'price': 22.00
                },
                {
                    'id': str(uuid.uuid4()),
                    'tenant_id': tenant_id,
                    'name': 'Gazpacho Andaluz',
                    'price': 8.50
                }
            ]
            
            for menu in menus_data:
                await conn.execute(
                    "INSERT INTO restaurant_menus (id, tenant_id, name, price, is_active) VALUES ($1, $2, $3, $4, $5)",
                    menu['id'], menu['tenant_id'], menu['name'], menu['price'], True
                )
            
            logger.info(f"✅ {len(menus_data)} itens de menu criados no restaurant_service")
            await conn.close()
            
        except Exception as e:
            logger.error(f"❌ Erro no seed do restaurant_service: {e}")
    
    async def _seed_consultancy_service(self):
        """Seeds para consultancy_service"""
        logger.info("💼 Executando seeds do consultancy_service...")
        logger.info("ℹ️ Consultancy service seeds - placeholder implementado")
    
    async def _seed_crm_service(self):
        """Seeds para crm_service"""
        logger.info("📊 Executando seeds do crm_service...")
        logger.info("ℹ️ CRM service seeds - placeholder implementado")
    
    async def _seed_hr_service(self):
        """Seeds para hr_service"""
        logger.info("👔 Executando seeds do hr_service...")
        logger.info("ℹ️ HR service seeds - placeholder implementado")
    
    def _hash_password(self, password: str) -> str:
        """Gera hash bcrypt para senha"""
        salt = bcrypt.gensalt(rounds=12)
        hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
        return hashed.decode('utf-8')

async def main():
    """Função principal"""
    setup = CitusSeedSetup()
    await setup.setup_citus_seeds()

if __name__ == "__main__":
    asyncio.run(main())
