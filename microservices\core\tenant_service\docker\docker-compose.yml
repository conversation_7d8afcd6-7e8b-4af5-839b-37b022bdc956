# ============================================================================
# TENANT SERVICE - Microserviço de Tenants
# ============================================================================
# Porta: 8003 | Database: postgres-tenants (5434) | Redis: DB 2
# Submódulos: tenant_gamify, Tenant Management, Tenant Types (Restaurant, Consultancy, Shop)
# ============================================================================

services:
  # ============================================================================
  # TENANT SERVICE - Serviço Principal
  # ============================================================================
  trix-core-tenant:
    build:
      context: ../
      dockerfile: docker/Dockerfile
    container_name: trix-core-tenant
    ports:
      - "8003:8000"
    environment:
      - DATABASE_URL=postgresql+asyncpg://postgres:TrixSuperSecure2024!@trix-citus-core-coordinator:5432/tenants_db
      - REDIS_URL=redis://:${REDIS_PASSWORD}@trix-redis:6379/2
      - AUTH_SERVICE_URL=http://trix-core-auth:8001
      - USER_SERVICE_URL=http://trix-core-user:8002
      - SECRET_KEY=${JWT_SECRET_KEY}
      - DEBUG=${DEBUG}
      - SERVICE_NAME=tenant-service
      - SERVICE_VERSION=1.0.0
    external_links:
      - trix-citus-core-coordinator:trix-citus-core-coordinator
    volumes:
      - ../:/app
      - ../../shared_lib:/app/shared_lib
    networks:
      - trix-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    labels:
      - "trix.category=core"
      - "trix.service=microservice"
      - "trix.module=tenant"
      - "trix.port=8003"

  # ============================================================================
  # TENANT DATABASE - Usando banco principal consolidado
  # ============================================================================
  # Banco removido - usando banco principal 'db' do docker-compose.yml raiz

networks:
  trix-network:
    external: true
