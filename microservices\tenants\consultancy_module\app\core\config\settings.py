"""
Consultancy Service Settings Configuration
==========================================
Consultancy Service specific settings that extend shared_lib configurations.
Follows the pattern established by user_service for consistency.
"""

from typing import List, Optional, Dict, Any
from pydantic import Field, field_validator
from pydantic_settings import SettingsConfigDict

# Import shared configurations from shared_lib
try:
    from microservices.core.shared_lib.config.vault_config import VaultBaseSettings
    from microservices.core.shared_lib.config.kafka_config import KafkaSettings
    from microservices.core.shared_lib.config.database_config import DatabaseSettings
    from microservices.core.shared_lib.config.redis_config import RedisSettings
    from microservices.core.shared_lib.config.security_config import SecuritySettings
    SHARED_LIB_AVAILABLE = True
    BaseSettingsClass = VaultBaseSettings
except ImportError:
    from pydantic_settings import BaseSettings
    SHARED_LIB_AVAILABLE = False
    BaseSettingsClass = BaseSettings


class ConsultancyServiceSettings(BaseSettingsClass):
    """
    Consultancy Service specific settings extending shared_lib configurations.

    Inherits common configurations from:
    - VaultBaseSettings: Vault configuration and environment settings
    - Uses shared Kafka, Database, Redis, Security configurations from shared_lib
    """

    # ===== CONSULTANCY SERVICE SPECIFIC SETTINGS =====

    # Service Identity
    SERVICE_NAME: str = Field(default="consultancy-service", env="SERVICE_NAME")
    SERVICE_PORT: int = Field(default=8016, env="SERVICE_PORT")
    SERVICE_VERSION: str = Field(default="2.0.0", env="SERVICE_VERSION")

    # API Configuration
    API_V1_PREFIX: str = Field(default="/api/v1", env="API_V1_PREFIX")
    
    # Database Configuration - Consultancy Service specific
    DATABASE_URL: str = Field(
        default="postgresql+asyncpg://consultancy_user:consultancy_pass@postgres-consultancy-cluster:5432/consultancy_db",
        env="DATABASE_URL"
    )

    # Consultancy Business Logic Settings
    MAX_PROJECTS_PER_CLIENT: int = Field(default=20, env="MAX_PROJECTS_PER_CLIENT")
    DEFAULT_HOURLY_RATE: float = Field(default=100.0, env="DEFAULT_HOURLY_RATE")
    PROJECT_TRACKING_ENABLED: bool = Field(default=True, env="PROJECT_TRACKING_ENABLED")
    TIME_TRACKING_ENABLED: bool = Field(default=True, env="TIME_TRACKING_ENABLED")
    INVOICE_GENERATION_ENABLED: bool = Field(default=True, env="INVOICE_GENERATION_ENABLED")
    
    MAX_CASES_PER_CONSULTANT: int = Field(default=50, env="MAX_CASES_PER_CONSULTANT")
    DEFAULT_CASE_DURATION_DAYS: int = Field(default=30, env="DEFAULT_CASE_DURATION_DAYS")
    MAX_TASKS_PER_CASE: int = Field(default=100, env="MAX_TASKS_PER_CASE")
    DEFAULT_BILLING_RATE: float = Field(default=150.0, env="DEFAULT_BILLING_RATE")
    MAX_CLIENTS_PER_CONSULTANT: int = Field(default=25, env="MAX_CLIENTS_PER_CONSULTANT")
    DEFAULT_PROJECT_DURATION_MONTHS: int = Field(default=6, env="DEFAULT_PROJECT_DURATION_MONTHS")

    # Consultancy Compliance Settings
    COMPLIANCE_CHECK_INTERVAL_DAYS: int = Field(default=7, env="COMPLIANCE_CHECK_INTERVAL_DAYS")
    AUDIT_LOG_RETENTION_DAYS: int = Field(default=365, env="AUDIT_LOG_RETENTION_DAYS")
    DOCUMENT_RETENTION_YEARS: int = Field(default=7, env="DOCUMENT_RETENTION_YEARS")

    # Service Integration URLs
    AUTH_SERVICE_URL: str = Field(default="http://auth-service:8001", env="AUTH_SERVICE_URL")
    USER_SERVICE_URL: str = Field(default="http://user-service:8002", env="USER_SERVICE_URL")
    TENANT_SERVICE_URL: str = Field(default="http://tenant-service:8003", env="TENANT_SERVICE_URL")
    FINANCIAL_SERVICE_URL: str = Field(default="http://financial-service:8013", env="FINANCIAL_SERVICE_URL")
    HR_SERVICE_URL: str = Field(default="http://hr-service:8014", env="HR_SERVICE_URL")

    # ===== SHARED CONFIGURATIONS (via shared_lib) =====

    @property
    def kafka_settings(self):
        """Returns Kafka settings from shared_lib."""
        if SHARED_LIB_AVAILABLE:
            return KafkaSettings()
        return None

    @property
    def database_settings(self):
        """Returns Database settings from shared_lib."""
        if SHARED_LIB_AVAILABLE:
            return DatabaseSettings()
        return None

    @property
    def redis_settings(self):
        """Returns Redis settings from shared_lib."""
        if SHARED_LIB_AVAILABLE:
            return RedisSettings()
        return None

    @property
    def security_settings(self):
        """Returns Security settings from shared_lib."""
        if SHARED_LIB_AVAILABLE:
            return SecuritySettings()
        return None

    def get_consultancy_vault_paths(self) -> dict:
        """Returns Consultancy Service specific Vault paths."""
        if SHARED_LIB_AVAILABLE and hasattr(self, 'get_vault_path'):
            return {
                "service": self.get_vault_path("service"),
                "database": self.get_vault_path("database"),
                "jwt": self.get_vault_path("jwt"),
                "redis": f"redis/{getattr(self, 'VAULT_ENVIRONMENT', 'development')}/consultancy-service",
                "rabbitmq": f"messaging/{getattr(self, 'VAULT_ENVIRONMENT', 'development')}/consultancy-service",
                "billing": f"billing/{getattr(self, 'VAULT_ENVIRONMENT', 'development')}/consultancy-service",
                "compliance": f"compliance/{getattr(self, 'VAULT_ENVIRONMENT', 'development')}/consultancy-service"
            }
        else:
            return {
                "database": "secret/consultancy-service/database",
                "kafka": "secret/consultancy-service/kafka",
                "redis": "secret/consultancy-service/redis",
                "external_apis": "secret/consultancy-service/external-apis",
                "billing": "secret/consultancy-service/billing",
                "compliance": "secret/consultancy-service/compliance"
            }

    class Config:
        env_file = ".env"
        case_sensitive = True


# Global settings instance
settings = ConsultancyServiceSettings()


def get_settings() -> ConsultancyServiceSettings:
    """Get cached settings instance."""
    return settings
