-- <PERSON><PERSON><PERSON> ex<PERSON>ão UUID se não existir
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- <PERSON><PERSON><PERSON> tabela alembic_version se não existir
CREATE TABLE IF NOT EXISTS alembic_version (
    version_num VARCHAR(32) NOT NULL,
    CONSTRAINT alembic_version_pkc PRIMARY KEY (version_num)
);

-- <PERSON><PERSON>r tabela users
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    system_role VARCHAR(50) DEFAULT 'user',
    failed_login_attempts INTEGER DEFAULT 0,
    account_locked_until TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP
);

-- <PERSON><PERSON><PERSON> tabela tenants
CREATE TABLE IF NOT EXISTS tenants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    tenant_type VARCHAR(100) NOT NULL,
    business_type VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Criar tabela tenant_user_associations
CREATE TABLE IF NOT EXISTS tenant_user_associations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id),
    tenant_id UUID NOT NULL REFERENCES tenants(id),
    role VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, tenant_id)
);

-- Inserir usuários padrão com senhas hasheadas (senha: admin123)
INSERT INTO users (email, password_hash, full_name, system_role) VALUES
('<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/SJHZJUcjO', 'System Administrator', 'admin'),
('<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/SJHZJUcjO', 'Restaurant Owner', 'user'),
('<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/SJHZJUcjO', 'Consultancy Owner', 'user'),
('<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/SJHZJUcjO', 'Test User', 'user'),
('<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/SJHZJUcjO', 'Test Supplier', 'user')
ON CONFLICT (email) DO NOTHING;

-- Inserir tenants padrão
INSERT INTO tenants (name, slug, tenant_type, business_type) VALUES
('Test Restaurant', 'test-restaurant', 'restaurant', 'Restaurant'),
('Test Accounting Firm', 'test-accounting', 'accounting', 'Accounting Firm')
ON CONFLICT (slug) DO NOTHING;

-- Criar associações user-tenant
WITH user_ids AS (
    SELECT id, email FROM users WHERE email IN (
        '<EMAIL>', 
        '<EMAIL>', 
        '<EMAIL>', 
        '<EMAIL>'
    )
),
tenant_ids AS (
    SELECT id, slug FROM tenants WHERE slug IN ('test-restaurant', 'test-accounting')
)
INSERT INTO tenant_user_associations (user_id, tenant_id, role)
SELECT 
    u.id,
    t.id,
    CASE 
        WHEN u.email = '<EMAIL>' AND t.slug = 'test-restaurant' THEN 'owner'
        WHEN u.email = '<EMAIL>' AND t.slug = 'test-accounting' THEN 'owner'
        WHEN u.email = '<EMAIL>' AND t.slug = 'test-restaurant' THEN 'customer'
        WHEN u.email = '<EMAIL>' AND t.slug = 'test-restaurant' THEN 'supplier'
    END as role
FROM user_ids u
CROSS JOIN tenant_ids t
WHERE (
    (u.email = '<EMAIL>' AND t.slug = 'test-restaurant') OR
    (u.email = '<EMAIL>' AND t.slug = 'test-accounting') OR
    (u.email = '<EMAIL>' AND t.slug = 'test-restaurant') OR
    (u.email = '<EMAIL>' AND t.slug = 'test-restaurant')
)
ON CONFLICT (user_id, tenant_id) DO NOTHING;

-- Marcar como executada
INSERT INTO alembic_version (version_num) 
VALUES ('manual_basic_tables')
ON CONFLICT (version_num) DO NOTHING;
