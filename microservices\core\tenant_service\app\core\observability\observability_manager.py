"""
Enterprise observability manager that coordinates Prometheus, Jaeger, and Elasticsearch.
"""

import logging
from typing import Dict, Any, Optional
import asyncio
from datetime import datetime

from microservices.core.shared_lib.config.infrastructure.observability.metrics import MetricsCollector
from microservices.core.shared_lib.config.infrastructure.observability.logging import get_logger
from app.core.config import get_settings


logger = logging.getLogger(__name__)


class ObservabilityManager:
    """Enterprise observability manager for coordinating all observability components."""
    
    def __init__(self):
        self.settings = get_settings()
        self.metrics_collector = MetricsCollector(service_name="tenant-service")
        self.logger = get_logger("tenant-service")
        self.is_initialized = False
    
    async def initialize(self) -> bool:
        """Initialize all observability components."""
        try:
            initialization_results = {}
            
            # Initialize Elasticsearch templates
            if self.elasticsearch.is_enabled:
                elasticsearch_init = await self.elasticsearch.create_index_templates()
                initialization_results["elasticsearch"] = elasticsearch_init
            else:
                initialization_results["elasticsearch"] = False
            
            # Prometheus is initialized on import
            initialization_results["prometheus"] = self.prometheus.is_enabled
            
            # Jaeger is initialized on import
            initialization_results["jaeger"] = self.jaeger.is_enabled
            
            self.is_initialized = True
            
            # Log initialization status
            await self.log_system_event(
                "observability_initialized",
                {
                    "components": initialization_results,
                    "enabled_components": [k for k, v in initialization_results.items() if v]
                }
            )
            
            logger.info(
                "Observability manager initialized",
                extra={
                    "components": initialization_results,
                    "enabled_count": sum(1 for v in initialization_results.values() if v)
                }
            )
            
            return any(initialization_results.values())
            
        except Exception as e:
            logger.error(f"Failed to initialize observability manager: {e}", exc_info=True)
            return False
    
    async def log_tenant_operation(
        self,
        operation: str,
        tenant_id: str,
        duration_seconds: Optional[float] = None,
        status: str = "success",
        user_id: Optional[str] = None,
        additional_data: Optional[Dict[str, Any]] = None
    ):
        """
        Log tenant operation across all observability components.
        
        Args:
            operation: Operation name
            tenant_id: Tenant ID
            duration_seconds: Operation duration
            status: Operation status
            user_id: Optional user ID
            additional_data: Additional data to log
        """
        try:
            # Prometheus metrics
            if self.prometheus.is_enabled:
                labels = {
                    "operation": operation,
                    "status": status,
                    "tenant_id": tenant_id
                }
                
                self.prometheus.increment_counter(
                    "tenant_operations_total",
                    labels
                )
                
                if duration_seconds is not None:
                    self.prometheus.observe_histogram(
                        "tenant_operations_duration_seconds",
                        duration_seconds,
                        {"operation": operation, "tenant_id": tenant_id}
                    )
            
            # Jaeger tracing (add attributes to current span if exists)
            if self.jaeger.is_enabled:
                self.jaeger.add_span_attribute("tenant.operation", operation)
                self.jaeger.add_span_attribute("tenant.id", tenant_id)
                self.jaeger.add_span_attribute("operation.status", status)
                
                if user_id:
                    self.jaeger.add_span_attribute("user.id", user_id)
                
                self.jaeger.add_span_event(
                    f"tenant_operation_{status}",
                    {
                        "operation": operation,
                        "tenant_id": tenant_id,
                        "duration_seconds": duration_seconds
                    }
                )
            
            # Elasticsearch logging
            if self.elasticsearch.is_enabled:
                event_data = {
                    "operation": operation,
                    "status": status,
                    "duration_seconds": duration_seconds
                }
                
                if additional_data:
                    event_data.update(additional_data)
                
                await self.elasticsearch.log_tenant_event(
                    event_type=f"tenant_operation_{operation}",
                    tenant_id=tenant_id,
                    event_data=event_data,
                    user_id=user_id
                )
            
        except Exception as e:
            logger.error(f"Error logging tenant operation: {e}")
    
    async def log_api_request(
        self,
        method: str,
        path: str,
        status_code: int,
        duration_seconds: float,
        tenant_id: Optional[str] = None,
        user_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ):
        """Log API request across all observability components."""
        try:
            # Prometheus metrics
            if self.prometheus.is_enabled:
                labels = {
                    "method": method,
                    "endpoint": path,
                    "status_code": str(status_code)
                }
                
                if tenant_id:
                    labels["tenant_id"] = tenant_id
                
                self.prometheus.increment_counter("http_requests_total", labels)
                self.prometheus.observe_histogram(
                    "http_request_duration_seconds",
                    duration_seconds,
                    {"method": method, "endpoint": path, "tenant_id": tenant_id or "unknown"}
                )
            
            # Elasticsearch logging
            if self.elasticsearch.is_enabled:
                await self.elasticsearch.log_api_request(
                    method=method,
                    path=path,
                    status_code=status_code,
                    duration_ms=duration_seconds * 1000,
                    user_id=user_id,
                    tenant_id=tenant_id,
                    ip_address=ip_address,
                    user_agent=user_agent
                )
            
        except Exception as e:
            logger.error(f"Error logging API request: {e}")
    
    async def log_error(
        self,
        error: Exception,
        context: Optional[Dict[str, Any]] = None,
        tenant_id: Optional[str] = None,
        user_id: Optional[str] = None
    ):
        """Log error across all observability components."""
        try:
            # Prometheus metrics
            if self.prometheus.is_enabled:
                labels = {
                    "error_type": type(error).__name__,
                    "service": "tenant-service"
                }
                
                if tenant_id:
                    labels["tenant_id"] = tenant_id
                
                self.prometheus.increment_counter("errors_total", labels)
            
            # Jaeger tracing
            if self.jaeger.is_enabled:
                self.jaeger.add_span_attribute("error.type", type(error).__name__)
                self.jaeger.add_span_attribute("error.message", str(error))
                
                if tenant_id:
                    self.jaeger.add_span_attribute("tenant.id", tenant_id)
                if user_id:
                    self.jaeger.add_span_attribute("user.id", user_id)
            
            # Elasticsearch logging
            if self.elasticsearch.is_enabled:
                await self.elasticsearch.log_error(
                    error=error,
                    context=context,
                    tenant_id=tenant_id,
                    user_id=user_id
                )
            
        except Exception as e:
            logger.error(f"Error logging error: {e}")
    
    async def log_security_event(
        self,
        event_type: str,
        severity: str,
        details: Dict[str, Any],
        tenant_id: Optional[str] = None,
        user_id: Optional[str] = None,
        ip_address: Optional[str] = None
    ):
        """Log security event across all observability components."""
        try:
            # Prometheus metrics
            if self.prometheus.is_enabled:
                labels = {
                    "event_type": event_type,
                    "severity": severity
                }
                
                if tenant_id:
                    labels["tenant_id"] = tenant_id
                
                self.prometheus.increment_counter("security_events_total", labels)
            
            # Jaeger tracing
            if self.jaeger.is_enabled:
                self.jaeger.add_span_event(
                    "security_event",
                    {
                        "event_type": event_type,
                        "severity": severity,
                        "tenant_id": tenant_id,
                        "user_id": user_id
                    }
                )
            
            # Elasticsearch logging
            if self.elasticsearch.is_enabled:
                await self.elasticsearch.log_security_event(
                    event_type=event_type,
                    severity=severity,
                    details=details,
                    tenant_id=tenant_id,
                    user_id=user_id,
                    ip_address=ip_address
                )
            
        except Exception as e:
            logger.error(f"Error logging security event: {e}")
    
    async def log_system_event(
        self,
        event_type: str,
        data: Dict[str, Any],
        level: str = "INFO"
    ):
        """Log system event."""
        try:
            if self.elasticsearch.is_enabled:
                await self.elasticsearch.log_structured(
                    level=level,
                    message=f"System event: {event_type}",
                    extra_data={
                        "event_type": event_type,
                        "system_data": data
                    },
                    index_suffix="system"
                )
        except Exception as e:
            logger.error(f"Error logging system event: {e}")
    
    async def health_check(self) -> Dict[str, bool]:
        """Check health of all observability components."""
        try:
            health_results = {
                "prometheus": self.prometheus.health_check(),
                "jaeger": self.jaeger.health_check(),
                "elasticsearch": await self.elasticsearch.health_check()
            }
            
            return health_results
            
        except Exception as e:
            logger.error(f"Observability health check failed: {e}")
            return {
                "prometheus": False,
                "jaeger": False,
                "elasticsearch": False
            }
    
    async def shutdown(self):
        """Shutdown all observability components."""
        try:
            if self.elasticsearch.is_enabled:
                await self.elasticsearch.close()
            
            logger.info("Observability manager shutdown completed")
            
        except Exception as e:
            logger.error(f"Error during observability shutdown: {e}")


# Global instance
observability_manager = ObservabilityManager()
