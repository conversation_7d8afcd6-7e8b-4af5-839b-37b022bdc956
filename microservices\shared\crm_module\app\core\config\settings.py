"""
Settings configuration for CRM service - Enterprise Grade v2.0
=============================================================

Centralized configuration management with environment-specific settings.
Integrates with HashiCorp Vault for secrets management.
"""

import os
from typing import Optional, List, Dict, Any
from pydantic import Field, field_validator
from pydantic_settings import BaseSettings
import sys
sys.path.append('/app')
from microservices.core.shared_lib.config.vault_config import StandardVaultConfig
from functools import lru_cache


class DatabaseSettings(BaseSettings):
    """Database configuration with Citus Data support."""
    
    # Primary database (Citus coordinator)
    DATABASE_URL: str = Field(
    # Configurações do Vault herdadas de StandardVaultConfig
    # VAULT_URL, VAULT_TOKEN, VAULT_ENABLED, etc. j<PERSON> estão definidas
    
        default="postgresql+asyncpg://crm_user:crm_pass@localhost:5432/crm_db",
        env="DATABASE_URL"
    )
    
    # Citus Data configuration
    CITUS_COORDINATOR_HOST: str = Field(default="localhost", env="CITUS_COORDINATOR_HOST")
    CITUS_COORDINATOR_PORT: int = Field(default=5432, env="CITUS_COORDINATOR_PORT")
    CITUS_WORKER_NODES: List[str] = Field(default=[], env="CITUS_WORKER_NODES")
    
    # Connection pooling with PgBouncer
    PGBOUNCER_URL: Optional[str] = Field(default=None, env="PGBOUNCER_URL")
    DATABASE_POOL_SIZE: int = Field(default=20, env="DATABASE_POOL_SIZE")
    DATABASE_MAX_OVERFLOW: int = Field(default=30, env="DATABASE_MAX_OVERFLOW")
    DATABASE_POOL_TIMEOUT: int = Field(default=30, env="DATABASE_POOL_TIMEOUT")
    
    # Migration settings
    ALEMBIC_CONFIG_PATH: str = Field(default="alembic.ini", env="ALEMBIC_CONFIG_PATH")
    AUTO_MIGRATE: bool = Field(default=False, env="AUTO_MIGRATE")
    
    class Config:
        env_prefix = "CRM_DB_"


class MessagingSettings(BaseSettings):
    """Messaging configuration for event-driven architecture."""
    
    # Apache Kafka configuration
    KAFKA_BOOTSTRAP_SERVERS: List[str] = Field(
        default=["localhost:9092"], 
        env="KAFKA_BOOTSTRAP_SERVERS"
    )
    KAFKA_TOPIC_PREFIX: str = Field(default="crm", env="KAFKA_TOPIC_PREFIX")
    KAFKA_CONSUMER_GROUP: str = Field(default="crm-service", env="KAFKA_CONSUMER_GROUP")
    KAFKA_AUTO_OFFSET_RESET: str = Field(default="earliest", env="KAFKA_AUTO_OFFSET_RESET")
    KAFKA_ENABLE_AUTO_COMMIT: bool = Field(default=True, env="KAFKA_ENABLE_AUTO_COMMIT")
    KAFKA_COMPRESSION_TYPE: str = Field(default="gzip", env="KAFKA_COMPRESSION_TYPE")
    
    # RabbitMQ configuration
    RABBITMQ_URL: str = Field(
        default="amqp://guest:guest@localhost:5672/", 
        env="RABBITMQ_URL"
    )
    RABBITMQ_EXCHANGE_CRM: str = Field(default="crm.events", env="RABBITMQ_EXCHANGE_CRM")
    RABBITMQ_EXCHANGE_NOTIFICATIONS: str = Field(
        default="crm.notifications", 
        env="RABBITMQ_EXCHANGE_NOTIFICATIONS"
    )
    RABBITMQ_QUEUE_PREFIX: str = Field(default="crm", env="RABBITMQ_QUEUE_PREFIX")
    
    # Redis Streams configuration
    REDIS_URL: str = Field(default="redis://localhost:6379/11", env="REDIS_URL")
    REDIS_STREAM_PREFIX: str = Field(default="crm:stream", env="REDIS_STREAM_PREFIX")
    REDIS_CONSUMER_GROUP: str = Field(default="crm-consumers", env="REDIS_CONSUMER_GROUP")
    
    class Config:
        env_prefix = "CRM_MESSAGING_"


class SecuritySettings(BaseSettings):
    """Security configuration with HashiCorp Vault integration."""    
    # JWT configuration
    JWT_SECRET_KEY: str = Field(default="your-secret-key", env="JWT_SECRET_KEY")
    JWT_ALGORITHM: str = Field(default="HS256", env="JWT_ALGORITHM")
    JWT_EXPIRATION_HOURS: int = Field(default=24, env="JWT_EXPIRATION_HOURS")
    
    # OPA (Open Policy Agent) configuration
    OPA_URL: str = Field(default="http://localhost:8181", env="OPA_URL")
    OPA_POLICY_PACKAGE: str = Field(default="crm.authz", env="OPA_POLICY_PACKAGE")
    
    # mTLS configuration
    MTLS_ENABLED: bool = Field(default=False, env="MTLS_ENABLED")
    MTLS_CERT_PATH: Optional[str] = Field(default=None, env="MTLS_CERT_PATH")
    MTLS_KEY_PATH: Optional[str] = Field(default=None, env="MTLS_KEY_PATH")
    MTLS_CA_PATH: Optional[str] = Field(default=None, env="MTLS_CA_PATH")
    
    class Config:
        env_prefix = "CRM_SECURITY_"


class ObservabilitySettings(BaseSettings):
    """Observability configuration for monitoring and tracing."""
    
    # Prometheus metrics
    PROMETHEUS_ENABLED: bool = Field(default=True, env="PROMETHEUS_ENABLED")
    PROMETHEUS_PORT: int = Field(default=9090, env="PROMETHEUS_PORT")
    PROMETHEUS_METRICS_PATH: str = Field(default="/metrics", env="PROMETHEUS_METRICS_PATH")
    
    # Jaeger tracing
    JAEGER_ENABLED: bool = Field(default=True, env="JAEGER_ENABLED")
    JAEGER_AGENT_HOST: str = Field(default="localhost", env="JAEGER_AGENT_HOST")
    JAEGER_AGENT_PORT: int = Field(default=6831, env="JAEGER_AGENT_PORT")
    JAEGER_SERVICE_NAME: str = Field(default="crm-service", env="JAEGER_SERVICE_NAME")
    
    # Structured logging
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    LOG_FORMAT: str = Field(default="json", env="LOG_FORMAT")  # json or text
    LOG_OUTPUT: str = Field(default="stdout", env="LOG_OUTPUT")  # stdout, file, or elk
    STRUCTURED_LOGGING: bool = Field(default=True, env="STRUCTURED_LOGGING")

    # ELK Stack configuration
    ELK_ENABLED: bool = Field(default=True, env="ELK_ENABLED")
    ELASTICSEARCH_URL: Optional[str] = Field(default=None, env="ELASTICSEARCH_URL")
    ELASTICSEARCH_HOST: str = Field(default="localhost", env="ELASTICSEARCH_HOST")
    ELASTICSEARCH_PORT: int = Field(default=9200, env="ELASTICSEARCH_PORT")
    ELASTICSEARCH_INDEX_PREFIX: str = Field(default="crm-logs", env="ELASTICSEARCH_INDEX_PREFIX")

    LOGSTASH_HOST: str = Field(default="localhost", env="LOGSTASH_HOST")
    LOGSTASH_PORT: int = Field(default=5044, env="LOGSTASH_PORT")
    LOGSTASH_USE_SSL: bool = Field(default=False, env="LOGSTASH_USE_SSL")

    KIBANA_HOST: str = Field(default="localhost", env="KIBANA_HOST")
    KIBANA_PORT: int = Field(default=5601, env="KIBANA_PORT")

    # Grafana dashboards
    GRAFANA_ENABLED: bool = Field(default=True, env="GRAFANA_ENABLED")
    GRAFANA_HOST: str = Field(default="localhost", env="GRAFANA_HOST")
    GRAFANA_PORT: int = Field(default=3000, env="GRAFANA_PORT")
    GRAFANA_API_KEY: Optional[str] = Field(default=None, env="GRAFANA_API_KEY")

    # Log retention and management
    LOG_RETENTION_DAYS: int = Field(default=30, env="LOG_RETENTION_DAYS")
    LOG_MAX_SIZE_MB: int = Field(default=100, env="LOG_MAX_SIZE_MB")
    LOG_BACKUP_COUNT: int = Field(default=5, env="LOG_BACKUP_COUNT")

    # Health checks
    HEALTH_CHECK_INTERVAL: int = Field(default=30, env="HEALTH_CHECK_INTERVAL")
    HEALTH_CHECK_TIMEOUT: int = Field(default=10, env="HEALTH_CHECK_TIMEOUT")
    
    class Config:
        env_prefix = "CRM_OBSERVABILITY_"


class ServiceIntegrationSettings(BaseSettings):
    """Service integration configuration for microservice communication."""
    
    # Core services
    AUTH_SERVICE_URL: str = Field(
        default="http://localhost:8001", 
        env="AUTH_SERVICE_URL"
    )
    USER_SERVICE_URL: str = Field(
        default="http://localhost:8002", 
        env="USER_SERVICE_URL"
    )
    TENANT_SERVICE_URL: str = Field(
        default="http://localhost:8003", 
        env="TENANT_SERVICE_URL"
    )
    CORE_SERVICE_URL: str = Field(
        default="http://localhost:8005", 
        env="CORE_SERVICE_URL"
    )
    
    # Business services
    COMMERCE_SERVICE_URL: str = Field(
        default="http://localhost:8009", 
        env="COMMERCE_SERVICE_URL"
    )
    NOTIFICATION_SERVICE_URL: str = Field(
        default="http://localhost:8010", 
        env="NOTIFICATION_SERVICE_URL"
    )
    PAYMENT_SERVICE_URL: str = Field(
        default="http://localhost:8013",
        env="PAYMENT_SERVICE_URL"
    )
    SUPPLIER_SERVICE_URL: str = Field(
        default="http://localhost:8004",
        env="SUPPLIER_SERVICE_URL"
    )
    I18N_SERVICE_URL: str = Field(
        default="http://localhost:8006",
        env="I18N_SERVICE_URL"
    )
    CDN_SERVICE_URL: str = Field(
        default="http://localhost:8007",
        env="CDN_SERVICE_URL"
    )
    MEDIA_SERVICE_URL: str = Field(
        default="http://localhost:8008",
        env="MEDIA_SERVICE_URL"
    )
    DOMAIN_SERVICE_URL: str = Field(
        default="http://localhost:8014",
        env="DOMAIN_SERVICE_URL"
    )

    # Shared modules
    HR_MODULE_URL: str = Field(
        default="http://localhost:8015",
        env="HR_MODULE_URL"
    )
    FINANCIAL_MODULE_URL: str = Field(
        default="http://localhost:8016",
        env="FINANCIAL_MODULE_URL"
    )
    EMAIL_MODULE_URL: str = Field(
        default="http://localhost:8017",
        env="EMAIL_MODULE_URL"
    )

    # Service timeouts and retries
    SERVICE_TIMEOUT_DEFAULT: int = Field(default=30, env="SERVICE_TIMEOUT_DEFAULT")
    SERVICE_RETRY_ATTEMPTS: int = Field(default=3, env="SERVICE_RETRY_ATTEMPTS")
    SERVICE_RETRY_DELAY: float = Field(default=1.0, env="SERVICE_RETRY_DELAY")
    
    # Circuit breaker configuration
    CIRCUIT_BREAKER_FAILURE_THRESHOLD: int = Field(
        default=5, 
        env="CIRCUIT_BREAKER_FAILURE_THRESHOLD"
    )
    CIRCUIT_BREAKER_RECOVERY_TIMEOUT: int = Field(
        default=60, 
        env="CIRCUIT_BREAKER_RECOVERY_TIMEOUT"
    )
    
    class Config:
        env_prefix = "CRM_INTEGRATION_"


class CRMSettings(BaseSettings):
    """Main CRM service configuration."""
    
    # Service information
    SERVICE_NAME: str = Field(default="crm-service", env="SERVICE_NAME")
    SERVICE_VERSION: str = Field(default="2.0.0", env="SERVICE_VERSION")
    SERVICE_PORT: int = Field(default=8011, env="SERVICE_PORT")
    SERVICE_HOST: str = Field(default="0.0.0.0", env="SERVICE_HOST")
    
    # Environment
    ENVIRONMENT: str = Field(default="development", env="ENVIRONMENT")
    DEBUG: bool = Field(default=False, env="DEBUG")
    
    # API configuration
    API_V1_PREFIX: str = Field(default="/api/v1", env="API_V1_PREFIX")
    DOCS_URL: str = Field(default="/docs", env="DOCS_URL")
    REDOC_URL: str = Field(default="/redoc", env="REDOC_URL")
    
    # Rate limiting
    RATE_LIMIT_ENABLED: bool = Field(default=True, env="RATE_LIMIT_ENABLED")
    RATE_LIMIT_REQUESTS_PER_MINUTE: int = Field(
        default=100, 
        env="RATE_LIMIT_REQUESTS_PER_MINUTE"
    )
    
    # Caching
    CACHE_ENABLED: bool = Field(default=True, env="CACHE_ENABLED")
    CACHE_TTL_SECONDS: int = Field(default=300, env="CACHE_TTL_SECONDS")
    
    # Nested settings
    database: DatabaseSettings = DatabaseSettings()
    messaging: MessagingSettings = MessagingSettings()
    security: SecuritySettings = SecuritySettings()
    observability: ObservabilitySettings = ObservabilitySettings()
    integrations: ServiceIntegrationSettings = ServiceIntegrationSettings()
    
    @field_validator('ENVIRONMENT')
    @classmethod
    def validate_environment(cls, v):
        allowed_envs = ['development', 'staging', 'production']
        if v not in allowed_envs:
            raise ValueError(f'Environment must be one of {allowed_envs}')
        return v
    
    class Config:
        env_prefix = "CRM_"
        case_sensitive = True


@lru_cache()
def get_settings() -> CRMSettings:
    """Get cached settings instance."""
    return CRMSettings()


# Global settings instance
settings = get_settings()
