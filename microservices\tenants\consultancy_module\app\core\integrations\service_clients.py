"""
Consultancy Service Integration Clients
=======================================
HTTP clients for communicating with other microservices.
"""

import httpx
from typing import Dict, Any, Optional, List
from ..config import get_settings, get_service_urls_config


class ServiceClient:
    """Base class for service clients."""
    
    def __init__(self, service_name: str, base_url: str):
        self.service_name = service_name
        self.base_url = base_url.rstrip('/')
        self.client = httpx.AsyncClient(
            base_url=self.base_url,
            timeout=30.0,
            headers={
                "Content-Type": "application/json",
                "User-Agent": "consultancy-service/2.0.0"
            }
        )
    
    async def get(self, endpoint: str, params: Optional[Dict] = None) -> Dict[str, Any]:
        """Make GET request to service."""
        response = await self.client.get(endpoint, params=params)
        response.raise_for_status()
        return response.json()
    
    async def post(self, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """Make POST request to service."""
        response = await self.client.post(endpoint, json=data)
        response.raise_for_status()
        return response.json()
    
    async def put(self, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """Make PUT request to service."""
        response = await self.client.put(endpoint, json=data)
        response.raise_for_status()
        return response.json()
    
    async def delete(self, endpoint: str) -> Dict[str, Any]:
        """Make DELETE request to service."""
        response = await self.client.delete(endpoint)
        response.raise_for_status()
        return response.json()
    
    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()


class AuthServiceClient(ServiceClient):
    """Client for Auth Service integration."""
    
    def __init__(self):
        service_urls = get_service_urls_config()
        super().__init__("auth", service_urls.auth_service_url)
    
    async def validate_token(self, token: str) -> Dict[str, Any]:
        """Validate JWT token."""
        return await self.post("/api/v1/auth/validate", {"token": token})
    
    async def check_permissions(self, user_id: str, permissions: List[str]) -> Dict[str, Any]:
        """Check user permissions."""
        return await self.post("/api/v1/auth/permissions/check", {
            "user_id": user_id,
            "permissions": permissions
        })


class UserServiceClient(ServiceClient):
    """Client for User Service integration."""
    
    def __init__(self):
        service_urls = get_service_urls_config()
        super().__init__("user", service_urls.user_service_url)
    
    async def get_user(self, user_id: str) -> Dict[str, Any]:
        """Get user information."""
        return await self.get(f"/api/v1/users/{user_id}")
    
    async def get_user_associations(self, user_id: str) -> Dict[str, Any]:
        """Get user tenant associations."""
        return await self.get(f"/api/v1/users/{user_id}/associations")


class TenantServiceClient(ServiceClient):
    """Client for Tenant Service integration."""
    
    def __init__(self):
        service_urls = get_service_urls_config()
        super().__init__("tenant", service_urls.tenant_service_url)
    
    async def get_tenant(self, tenant_id: str) -> Dict[str, Any]:
        """Get tenant information."""
        return await self.get(f"/api/v1/tenants/{tenant_id}")
    
    async def get_tenant_settings(self, tenant_id: str) -> Dict[str, Any]:
        """Get tenant settings."""
        return await self.get(f"/api/v1/tenants/{tenant_id}/settings")


class FinancialServiceClient(ServiceClient):
    """Client for Financial Service integration."""
    
    def __init__(self):
        service_urls = get_service_urls_config()
        super().__init__("financial", service_urls.financial_service_url)
    
    async def create_invoice(self, invoice_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create invoice in financial service."""
        return await self.post("/api/v1/invoices", invoice_data)
    
    async def get_financial_report(self, tenant_id: str, period: str) -> Dict[str, Any]:
        """Get financial report."""
        return await self.get(f"/api/v1/reports/{tenant_id}", {"period": period})


class HRServiceClient(ServiceClient):
    """Client for HR Service integration."""
    
    def __init__(self):
        service_urls = get_service_urls_config()
        super().__init__("hr", service_urls.hr_service_url)
    
    async def get_employee(self, employee_id: str) -> Dict[str, Any]:
        """Get employee information."""
        return await self.get(f"/api/v1/employees/{employee_id}")
    
    async def get_employee_schedule(self, employee_id: str) -> Dict[str, Any]:
        """Get employee schedule."""
        return await self.get(f"/api/v1/employees/{employee_id}/schedule")


# Global service client instances
auth_client = AuthServiceClient()
user_client = UserServiceClient()
tenant_client = TenantServiceClient()
financial_client = FinancialServiceClient()
hr_client = HRServiceClient()


def get_auth_client() -> AuthServiceClient:
    """Get Auth Service client."""
    return auth_client


def get_user_client() -> UserServiceClient:
    """Get User Service client."""
    return user_client


def get_tenant_client() -> TenantServiceClient:
    """Get Tenant Service client."""
    return tenant_client


def get_financial_client() -> FinancialServiceClient:
    """Get Financial Service client."""
    return financial_client


def get_hr_client() -> HRServiceClient:
    """Get HR Service client."""
    return hr_client
