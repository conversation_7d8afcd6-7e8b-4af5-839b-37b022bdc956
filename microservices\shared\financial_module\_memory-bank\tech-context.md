# Financial Module - Technical Context

## 🛠️ **Stack Tecnológico**

### **Core Framework**
```python
# FastAPI Application
FastAPI==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0
```

### **Database & ORM**
```python
# PostgreSQL with Citus Extension
psycopg2-binary==2.9.9
SQLAlchemy==2.0.23
alembic==1.13.1
asyncpg==0.29.0

# Citus for horizontal scaling
citus==12.1
```

### **Shared Library Integration**
```python
# Shared components from microservices.core.shared_lib
from microservices.core.shared_lib.config.infrastructure.financial import (
    FinancialEventBase,
    FinancialEventPublisher,
    FinancialKafkaClient,
    FinancialEncryption,
    FinancialMetrics
)
```

### **Message Brokers**
```python
# Event-driven architecture
kafka-python==2.0.2
pika==1.3.2  # RabbitMQ
redis==5.0.1
celery==5.3.4
```

### **Security & Authentication**
```python
# Security stack
PyJWT==2.8.0
cryptography==41.0.8
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Vault integration
hvac==2.1.0  # HashiCorp Vault client
```

### **Observability**
```python
# Monitoring and tracing
prometheus-client==0.19.0
opentelemetry-api==1.21.0
opentelemetry-sdk==1.21.0
opentelemetry-instrumentation-fastapi==0.42b0
structlog==23.2.0
```

### **Enterprise Dependencies**
```python
# Enterprise-specific packages
istio-client==1.19.0
kubernetes==28.1.0
vault-client==0.7.2
jaeger-client==4.8.0
```

## 🏗️ **Arquitetura de Código**

### **Estrutura de Diretórios**
```
financial_module/
├── app/
│   ├── api/v1/              # API endpoints versionados
│   ├── core/
│   │   ├── config/          # Configurações específicas
│   │   └── integrations/    # Integrações com outros serviços
│   ├── models/              # SQLAlchemy models
│   ├── schemas/             # Pydantic schemas
│   ├── services/            # Business logic
│   └── websockets/          # Real-time updates
├── migrations/              # Alembic migrations
├── tests/                   # Test suite
└── _memory-bank/           # Service documentation
```

### **Padrões de Design**

#### **Repository Pattern**
```python
# Base repository using shared_lib
from microservices.core.shared_lib.config.infrastructure.database import BaseRepository

class TransactionRepository(BaseRepository):
    model = Transaction
    
    async def get_by_tenant(self, tenant_id: UUID) -> List[Transaction]:
        # Tenant-specific queries with sharding
        pass
```

#### **Service Layer Pattern**
```python
# Business logic separation
class TransactionService:
    def __init__(self, repo: TransactionRepository, event_publisher: FinancialEventPublisher):
        self.repo = repo
        self.event_publisher = event_publisher
    
    async def create_transaction(self, data: TransactionCreate) -> Transaction:
        # Business logic + event publishing
        pass
```

#### **Event-Driven Architecture**
```python
# Using shared_lib event system
from microservices.core.shared_lib.config.infrastructure.financial import (
    TransactionEvent,
    FinancialEventPublisher
)

# Event publishing
await event_publisher.publish(
    TransactionEvent(
        transaction_id=transaction.id,
        tenant_id=transaction.tenant_id,
        amount=transaction.amount,
        event_type="transaction_created"
    )
)
```

## 🔧 **Configurações Específicas**

### **Database Configuration**
```python
# app/core/config/database.py
from microservices.core.shared_lib.config.infrastructure.database import DatabaseConfig

class FinancialDatabaseConfig(DatabaseConfig):
    # Financial-specific database settings
    CITUS_SHARD_COUNT: int = 32
    FINANCIAL_DB_POOL_SIZE: int = 20
    TRANSACTION_TIMEOUT: int = 30
    
    # Sharding configuration
    SHARD_KEY: str = "tenant_id"
    REPLICATION_FACTOR: int = 3
```

### **Service URLs**
```python
# app/core/config/service_urls.py
class ServiceURLs:
    USER_SERVICE = "http://user-service:8000"
    TENANT_SERVICE = "http://tenant-service:8000"
    AUTH_SERVICE = "http://auth-service:8000"
    PAYMENT_SERVICE = "http://payment-service:8000"
    
    # External integrations
    STRIPE_API = "https://api.stripe.com/v1"
    PAYPAL_API = "https://api.paypal.com/v1"
```

### **Vault Paths**
```python
# app/core/config/vault.py
class VaultPaths:
    FINANCIAL_SECRETS = "secret/financial"
    PAYMENT_GATEWAYS = "secret/payment-gateways"
    ENCRYPTION_KEYS = "secret/encryption/financial"
    DATABASE_CREDENTIALS = "secret/database/financial"
```

## 🔄 **Integration Patterns**

### **Service Communication**
```python
# app/core/integrations/service_clients.py
from microservices.core.shared_lib.config.infrastructure.messaging import ServiceClient

class UserServiceClient(ServiceClient):
    service_name = "user-service"
    
    async def get_user(self, user_id: UUID) -> dict:
        return await self.get(f"/users/{user_id}")

class TenantServiceClient(ServiceClient):
    service_name = "tenant-service"
    
    async def get_tenant_permissions(self, tenant_id: UUID, user_id: UUID) -> dict:
        return await self.get(f"/tenants/{tenant_id}/permissions/{user_id}")
```

### **Event Handling**
```python
# Event consumers using shared_lib
from microservices.core.shared_lib.config.infrastructure.financial import FinancialEventConsumer

class FinancialEventHandler(FinancialEventConsumer):
    async def handle_user_created(self, event: dict):
        # Create financial profile for new user
        pass
    
    async def handle_tenant_created(self, event: dict):
        # Setup financial configuration for new tenant
        pass
```

## 📊 **Performance Optimizations**

### **Database Optimizations**
```python
# Query optimization using shared_lib
from microservices.core.shared_lib.config.infrastructure.financial import FinancialQueryOptimizer

optimizer = FinancialQueryOptimizer()

# Optimized queries for financial data
optimized_query = optimizer.optimize_transaction_query(
    tenant_id=tenant_id,
    date_range=(start_date, end_date),
    include_categories=True
)
```

### **Caching Strategy**
```python
# Multi-layer caching
from microservices.core.shared_lib.config.infrastructure.financial import FinancialRedisClient

redis_client = FinancialRedisClient()

# Cache financial summaries
await redis_client.cache_financial_summary(
    tenant_id=tenant_id,
    summary_data=summary,
    ttl=3600  # 1 hour
)
```

## 🧪 **Testing Strategy**

### **Test Configuration**
```python
# tests/conftest.py
import pytest
from microservices.core.shared_lib.config.infrastructure.testing import FinancialTestClient

@pytest.fixture
def financial_test_client():
    return FinancialTestClient(
        service_name="financial-module",
        test_database=True,
        mock_external_services=True
    )
```

### **Integration Tests**
```python
# tests/test_integration.py
class TestFinancialIntegration:
    async def test_transaction_flow(self, financial_test_client):
        # Test complete transaction flow
        # Including event publishing and service communication
        pass
    
    async def test_multi_tenant_isolation(self, financial_test_client):
        # Test tenant data isolation
        pass
```

## 🚀 **Deployment Configuration**

### **Docker Configuration**
```dockerfile
# Dockerfile
FROM python:3.11-slim

# Install dependencies
COPY requirements.txt .
RUN pip install -r requirements.txt

# Copy application
COPY app/ /app/
WORKDIR /app

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### **Kubernetes Deployment**
```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: financial-module
spec:
  replicas: 3
  selector:
    matchLabels:
      app: financial-module
  template:
    spec:
      containers:
      - name: financial-module
        image: trix/financial-module:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: financial-secrets
              key: database-url
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
```

## 🔍 **Monitoring e Observabilidade**

### **Metrics Collection**
```python
# Using shared_lib financial metrics
from microservices.core.shared_lib.config.infrastructure.financial import FinancialMetrics

metrics = FinancialMetrics("financial-module")

# Custom financial metrics
metrics.transaction_counter.inc()
metrics.transaction_amount_histogram.observe(amount)
metrics.tenant_active_gauge.set(active_tenants_count)
```

### **Distributed Tracing**
```python
# Using shared_lib tracing
from microservices.core.shared_lib.config.infrastructure.financial import FinancialTracing

tracing = FinancialTracing("financial-module")

@tracing.trace_financial_operation
async def process_transaction(transaction_data):
    # Traced financial operation
    pass
```