# ============================================================================
# HR SERVICE - Microserviço de Recursos Humanos
# ============================================================================
# Porta: 8014 | Database: postgres-hr (5447) | Redis: DB 14
# Submódulos: employee_management, time_tracking, work_schedules, document_management, recruitment, employee_portal, performance_management, public_resume, onboarding, payroll_integration, lms_system, achievement_badges
# ============================================================================

services:
  # ============================================================================
  # HR SERVICE - Serviço Principal
  # ============================================================================
  trix-shared-hr:
    build:
      context: ../
      dockerfile: docker/Dockerfile
    container_name: trix-shared-hr
    ports:
      - "8014:8000"
    environment:
      - DATABASE_URL=postgresql+asyncpg://postgres:TrixSuperSecure2024!@trix-citus-shared-coordinator:5432/hr_db
      - REDIS_URL=redis://:${REDIS_PASSWORD}@trix-redis:6379/14
      - AUTH_SERVICE_URL=http://trix-core-auth:8001
      - USER_SERVICE_URL=http://trix-core-user:8002
      - TENANT_SERVICE_URL=http://trix-core-tenant:8003
      - FINANCIAL_SERVICE_URL=http://trix-shared-financial:8013
      - EMAIL_SERVICE_URL=http://trix-shared-email:8012
      - SECRET_KEY=${JWT_SECRET_KEY}
      - DEBUG=${DEBUG}
      - SERVICE_NAME=hr-service
      - SERVICE_VERSION=1.0.0
      - CELERY_BROKER_URL=redis://:${REDIS_PASSWORD}@trix-redis:6379/14
      - CELERY_RESULT_BACKEND=redis://:${REDIS_PASSWORD}@trix-redis:6379/14
      - LMS_ENABLED=true
      - GAMIFICATION_ENABLED=true
      - DIGITAL_SIGNATURE_ENABLED=true
      - PAYROLL_INTEGRATION_ENABLED=true
    depends_on:
      - trix-redis
    external_links:
      - trix-citus-shared-coordinator:trix-citus-shared-coordinator
    volumes:
      - ../:/app
      - trix_hr_documents:/app/documents
      - trix_hr_certificates:/app/certificates
      - trix_hr_resumes:/app/resumes
      - trix_hr_courses:/app/courses
    networks:
      - trix-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    labels:
      - "trix.category=shared"
      - "trix.service=microservice"
      - "trix.module=hr"
      - "trix.port=8014"

  # ============================================================================
  # HR DATABASE - Usando banco principal consolidado
  # ============================================================================
  # Banco removido - usando banco principal 'db' do docker-compose.yml raiz

networks:
  trix-network:
    external: true
