"""
Ghost Function Service - Vault Configuration

Configurações específicas do HashiCorp Vault para o Ghost Function Service.
Estende as configurações da shared_lib para paths específicos do serviço.

Author: Trix Platform Team
"""

from microservices.core.shared_lib.config import VaultBaseSettings
from .settings import get_settings


class GhostVaultSettings(VaultBaseSettings):
    """Ghost Function Service specific Vault settings."""
    
    def __init__(self):
        super().__init__()
        settings = get_settings()
        
        # Ghost Function specific Vault paths
        self.vault_paths = {
            **self.vault_paths,  # Inherit base paths
            
            # Ghost Function specific secrets
            "ghost_function_db": "secret/ghost-function/database",
            "ghost_function_api_keys": "secret/ghost-function/api-keys",
            "ghost_function_certificates": "secret/ghost-function/certificates",
            
            # Service monitoring secrets
            "service_monitoring": "secret/ghost-function/monitoring",
            "proxy_authentication": "secret/ghost-function/proxy-auth",
            
            # Circuit breaker configurations
            "circuit_breaker_config": "secret/ghost-function/circuit-breaker",
            
            # Failover configurations
            "failover_config": "secret/ghost-function/failover",
            
            # Hibernation configurations
            "hibernation_config": "secret/ghost-function/hibernation"
        }
    
    def get_ghost_function_secret_path(self, secret_type: str) -> str:
        """Get Vault path for Ghost Function specific secrets."""
        return self.vault_paths.get(f"ghost_function_{secret_type}", f"secret/ghost-function/{secret_type}")
    
    def get_service_monitoring_path(self) -> str:
        """Get Vault path for service monitoring secrets."""
        return self.vault_paths["service_monitoring"]
    
    def get_proxy_auth_path(self) -> str:
        """Get Vault path for proxy authentication secrets."""
        return self.vault_paths["proxy_authentication"]


# Global Ghost Vault settings instance
ghost_vault_settings = GhostVaultSettings()


def get_ghost_vault_settings() -> GhostVaultSettings:
    """Get the Ghost Function Vault settings instance."""
    return ghost_vault_settings
