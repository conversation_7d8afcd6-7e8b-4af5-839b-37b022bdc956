FROM alpine:latest

# Install pgbouncer, bash and netcat for healthcheck
RUN apk add --no-cache pgbouncer bash netcat-openbsd

# Create pgbouncer user and directories
RUN adduser -D -s /bin/bash pgbouncer && \
    mkdir -p /etc/pgbouncer /var/log/pgbouncer && \
    chown -R pgbouncer:pgbouncer /etc/pgbouncer /var/log/pgbouncer

# Copy entrypoint script
COPY entrypoint.sh /usr/local/bin/entrypoint.sh
RUN chmod +x /usr/local/bin/entrypoint.sh

# Expose PgBouncer port
EXPOSE 6432

# Health check
HEALTHCHECK --interval=10s --timeout=5s --start-period=30s --retries=3 \
    CMD nc -z localhost 6432 || exit 1

# Run as pgbouncer user
USER pgbouncer

# Run PgBouncer with entrypoint
ENTRYPOINT ["/usr/local/bin/entrypoint.sh"]
