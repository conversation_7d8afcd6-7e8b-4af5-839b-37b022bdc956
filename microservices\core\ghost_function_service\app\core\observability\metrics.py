"""
Ghost Function Service - Metrics Configuration

Métricas específicas do Ghost Function Service que complementam as métricas
da shared_lib para funcionalidades específicas de proxy e resiliência.

Author: Trix Platform Team
"""

from prometheus_client import Counter, Histogram, Gauge
from microservices.core.shared_lib.infrastructure.observability import (
    MetricsCollector,
    get_metrics_collector
)
from ..config.settings import get_settings

# Configurações
settings = get_settings()

# Métricas específicas do Ghost Function Service
ghost_requests_counter = Counter(
    'ghost_function_requests_total',
    'Total number of requests processed by Ghost Function',
    ['method', 'service_name', 'status', 'source']
)

failover_requests_counter = Counter(
    'ghost_function_failover_total',
    'Total number of failover events',
    ['service_name', 'from_source', 'to_source', 'reason']
)

proxy_duration_histogram = Histogram(
    'ghost_function_proxy_duration_seconds',
    'Time spent proxying requests',
    ['service_name', 'endpoint', 'status'],
    buckets=[0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0]
)

hibernation_state_gauge = Gauge(
    'ghost_function_hibernation_state',
    'Current hibernation state of services',
    ['service_name', 'hibernation_level']
)

circuit_breaker_state_gauge = Gauge(
    'ghost_function_circuit_breaker_state',
    'Current circuit breaker state',
    ['service_name', 'state']
)

service_health_gauge = Gauge(
    'ghost_function_service_health',
    'Health status of monitored services',
    ['service_name', 'status']
)

wake_up_duration_histogram = Histogram(
    'ghost_function_wake_up_duration_seconds',
    'Time taken to wake up ghost instances',
    ['service_name', 'hibernation_level'],
    buckets=[0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.0, 5.0]
)

ghost_instance_count_gauge = Gauge(
    'ghost_function_active_instances',
    'Number of active ghost instances',
    ['service_name', 'instance_type']
)


class GhostFunctionMetrics:
    """Ghost Function Service specific metrics manager."""
    
    def __init__(self):
        # Usar MetricsCollector da shared_lib para métricas comuns
        self.shared_metrics = get_metrics_collector(
            service_name="ghost-function-service",
            service_version=settings.SERVICE_VERSION,
            environment=settings.SERVICE_ENVIRONMENT
        )
    
    def record_proxy_operation(self, operation: str, service_name: str, status: str, duration: float):
        """Record proxy operation metrics."""
        
        # Métricas compartilhadas (HTTP, infraestrutura)
        self.shared_metrics.requests_counter.labels(
            endpoint=f"proxy.{operation}",
            method="PROXY",
            status=status
        ).inc()
        
        self.shared_metrics.request_duration_histogram.labels(
            endpoint=f"proxy.{operation}",
            method="PROXY"
        ).observe(duration)
        
        # Métricas específicas do Ghost Function
        ghost_requests_counter.labels(
            method=operation,
            service_name=service_name,
            status=status,
            source="proxy"
        ).inc()
        
        proxy_duration_histogram.labels(
            service_name=service_name,
            endpoint=operation,
            status=status
        ).observe(duration)
    
    def record_failover_event(self, service_name: str, from_source: str, to_source: str, reason: str = "timeout"):
        """Record failover event metrics."""
        failover_requests_counter.labels(
            service_name=service_name,
            from_source=from_source,
            to_source=to_source,
            reason=reason
        ).inc()
    
    def record_hibernation_change(self, service_name: str, hibernation_level: str):
        """Record hibernation level change."""
        hibernation_state_gauge.labels(
            service_name=service_name,
            hibernation_level=hibernation_level
        ).set(1)
    
    def record_circuit_breaker_state(self, service_name: str, state: str):
        """Record circuit breaker state change."""
        circuit_breaker_state_gauge.labels(
            service_name=service_name,
            state=state
        ).set(1 if state == "open" else 0)
    
    def record_service_health(self, service_name: str, is_healthy: bool):
        """Record service health status."""
        service_health_gauge.labels(
            service_name=service_name,
            status="healthy" if is_healthy else "unhealthy"
        ).set(1 if is_healthy else 0)
    
    def record_wake_up_duration(self, service_name: str, hibernation_level: str, duration: float):
        """Record time taken to wake up a ghost instance."""
        wake_up_duration_histogram.labels(
            service_name=service_name,
            hibernation_level=hibernation_level
        ).observe(duration)
    
    def record_ghost_instance_count(self, service_name: str, instance_type: str, count: int):
        """Record number of active ghost instances."""
        ghost_instance_count_gauge.labels(
            service_name=service_name,
            instance_type=instance_type
        ).set(count)
    
    def get_metrics_summary(self) -> dict:
        """Get summary of all Ghost Function metrics."""
        return {
            "proxy_requests": ghost_requests_counter._value._value,
            "failover_events": failover_requests_counter._value._value,
            "shared_metrics": self.shared_metrics.get_metrics_summary()
        }


# Global metrics instance
ghost_metrics = GhostFunctionMetrics()
