"""
Media Schemas
=============

Schemas Pydantic para operações de mídia no Media System.

✅ MIGRAÇÃO CONCLUÍDA - Usando shared_lib para configurações comuns
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from uuid import UUID

from pydantic import BaseModel, Field, validator
from fastapi import UploadFile

from ..models.media import MediaType, MediaStatus, MediaVisibility, MediaContext


class MediaBase(BaseModel):
    """Schema base para mídia."""
    
    filename: str = Field(..., max_length=255, description="Nome do arquivo")
    original_filename: str = Field(..., max_length=255, description="Nome original do arquivo")
    file_size: int = Field(..., ge=0, description="Tamanho do arquivo em bytes")
    mime_type: str = Field(..., max_length=100, description="Tipo MIME do arquivo")
    context: MediaContext = Field(..., description="Contexto da mídia")
    is_public: bool = Field(default=False, description="Se a mídia é pública")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Metadados adicionais")


class MediaCreate(MediaBase):
    """Schema para criação de mídia."""
    
    tenant_id: UUID = Field(..., description="ID do tenant")
    user_id: UUID = Field(..., description="ID do usuário")
    file_hash: str = Field(..., max_length=64, description="Hash SHA-256 do arquivo")
    file_path: str = Field(..., max_length=500, description="Caminho do arquivo no storage")
    region: str = Field(..., description="Região de armazenamento")


class MediaUpdate(BaseModel):
    """Schema para atualização de mídia."""
    
    filename: Optional[str] = Field(None, max_length=255)
    is_public: Optional[bool] = None
    metadata: Optional[Dict[str, Any]] = None


class MediaResponse(MediaBase):
    """Schema para resposta de mídia."""
    
    id: UUID
    tenant_id: UUID
    user_id: UUID
    file_path: str
    file_hash: str
    region: str
    is_processed: bool
    created_at: datetime
    updated_at: datetime
    
    # URLs geradas dinamicamente
    download_url: Optional[str] = None
    thumbnail_url: Optional[str] = None
    preview_url: Optional[str] = None
    
    class Config:
        from_attributes = True


class MediaUploadRequest(BaseModel):
    """Schema para requisição de upload."""
    
    context: MediaContext = Field(..., description="Contexto da mídia")
    tenant_id: UUID = Field(..., description="ID do tenant")
    is_public: bool = Field(default=False, description="Se a mídia é pública")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Metadados adicionais")
    
    # Configurações de processamento
    auto_thumbnail: bool = Field(default=True, description="Gerar thumbnail automaticamente")
    auto_compression: bool = Field(default=True, description="Comprimir automaticamente")
    auto_ocr: bool = Field(default=False, description="Executar OCR automaticamente")


class MediaUploadResponse(BaseModel):
    """Schema para resposta de upload."""
    
    media_id: UUID
    upload_url: str
    expires_at: datetime
    max_file_size: int
    allowed_mime_types: List[str]
    
    # Configurações aplicadas
    processing_enabled: bool
    thumbnail_enabled: bool
    compression_enabled: bool
    ocr_enabled: bool


class MediaBulkUploadRequest(BaseModel):
    """Schema para requisição de upload em lote."""
    
    files_count: int = Field(..., ge=1, le=100, description="Número de arquivos")
    context: MediaContext = Field(..., description="Contexto da mídia")
    tenant_id: UUID = Field(..., description="ID do tenant")
    is_public: bool = Field(default=False, description="Se as mídias são públicas")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Metadados comuns")
    
    # Configurações de processamento
    auto_thumbnail: bool = Field(default=True, description="Gerar thumbnails automaticamente")
    auto_compression: bool = Field(default=True, description="Comprimir automaticamente")
    auto_ocr: bool = Field(default=False, description="Executar OCR automaticamente")


class MediaBulkUploadResponse(BaseModel):
    """Schema para resposta de upload em lote."""
    
    batch_id: UUID
    upload_urls: List[Dict[str, Any]]  # [{"media_id": UUID, "upload_url": str}]
    expires_at: datetime
    max_file_size: int
    allowed_mime_types: List[str]
    
    # Status do lote
    total_files: int
    uploaded_files: int = 0
    processed_files: int = 0
    failed_files: int = 0


class MediaSearchRequest(BaseModel):
    """Schema para busca de mídia."""
    
    tenant_id: Optional[UUID] = None
    user_id: Optional[UUID] = None
    context: Optional[MediaContext] = None
    media_type: Optional[MediaType] = None
    status: Optional[MediaStatus] = None
    is_public: Optional[bool] = None
    
    # Filtros de data
    created_after: Optional[datetime] = None
    created_before: Optional[datetime] = None
    
    # Filtros de tamanho
    min_file_size: Optional[int] = None
    max_file_size: Optional[int] = None
    
    # Busca textual
    search_query: Optional[str] = Field(None, max_length=255, description="Busca em filename e metadata")
    
    # Paginação
    page: int = Field(default=1, ge=1)
    page_size: int = Field(default=20, ge=1, le=100)
    
    # Ordenação
    order_by: str = Field(default="created_at", description="Campo para ordenação")
    order_direction: str = Field(default="desc", regex="^(asc|desc)$")


class MediaSearchResponse(BaseModel):
    """Schema para resposta de busca."""
    
    items: List[MediaResponse]
    total: int
    page: int
    page_size: int
    total_pages: int
    has_next: bool
    has_previous: bool
    
    # Estatísticas da busca
    total_size_bytes: int = 0
    media_types_count: Dict[str, int] = Field(default_factory=dict)
    contexts_count: Dict[str, int] = Field(default_factory=dict)
