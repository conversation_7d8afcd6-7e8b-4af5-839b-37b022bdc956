# ============================================================================
# DOMAIN SERVICE v2.0.0 - Enterprise Microservice
# ============================================================================
# Porta: 8015 | Database: Citus Data (5448) | Redis Cluster: DB 15
# Submódulos: custom_domains, domain_registration, dns_management, ssl_management
# Architecture: Event-driven with full observability
# ============================================================================

services:
  # ============================================================================
  # DOMAIN SERVICE v2.0.0 - Enterprise Service
  # ============================================================================
  trix-core-domain:
    build:
      context: ../
      dockerfile: docker/Dockerfile
    container_name: trix-core-domain
    restart: unless-stopped
    ports:
      - "8015:8000"
      - "9090:9090"  # Metrics port
    environment:
      # Service Configuration
      - SERVICE_NAME=domain-service
      - SERVICE_VERSION=2.0.0
      - PORT=8000
      - HOST=0.0.0.0
      - ENVIRONMENT=production
      - DEBUG=false
      - LOG_LEVEL=INFO
      - REGION=us-east-1
      - AVAILABILITY_ZONE=us-east-1a

      # Citus Data Database Configuration
      - DATABASE_URL=postgresql+asyncpg://domains_user:domains_pass@trix-citus-coordinator:5432/domains_db
      - PGBOUNCER_URL=******************************************************************/domains_db
      - DATABASE_SHARD_COUNT=32
      - DATABASE_REPLICATION_FACTOR=2
      - DATABASE_POOL_SIZE=20
      - DATABASE_MAX_OVERFLOW=30

      # Messaging Configuration
      - KAFKA_BOOTSTRAP_SERVERS=trix-kafka-1:9092,trix-kafka-2:9092,trix-kafka-3:9092
      - KAFKA_TOPIC_DOMAINS=domain-events
      - KAFKA_TOPIC_DNS=dns-events
      - KAFKA_TOPIC_SSL=ssl-events
      - RABBITMQ_URL=amqp://domains_user:domains_pass@trix-rabbitmq-cluster:5672/
      - RABBITMQ_EXCHANGE=domain-exchange
      - REDIS_URL=redis://:redis_pass@trix-redis-cluster:6379/15
      - REDIS_CLUSTER_NODES=trix-redis-1:6379,trix-redis-2:6379,trix-redis-3:6379

      # Security Configuration
      - VAULT_ADDR=https://trix-vault:8200
      - VAULT_TOKEN=${VAULT_TOKEN}
      - VAULT_MOUNT_PATH=secret/domain-service
      - SECRET_KEY=${DOMAIN_SERVICE_SECRET_KEY}
      - CORS_ORIGINS=https://*.trix.com,https://trix.com
      - CELERY_BROKER_URL=redis://:${REDIS_PASSWORD}@trix-redis:6379/15
      - CELERY_RESULT_BACKEND=redis://:${REDIS_PASSWORD}@trix-redis:6379/15
      # Domain Registrar APIs
      - GODADDY_API_KEY=${GODADDY_API_KEY}
      - GODADDY_API_SECRET=${GODADDY_API_SECRET}
      - NAMECHEAP_API_USER=${NAMECHEAP_API_USER}
      - NAMECHEAP_API_KEY=${NAMECHEAP_API_KEY}
      - OPENSRS_USERNAME=${OPENSRS_USERNAME}
      - OPENSRS_PRIVATE_KEY=${OPENSRS_PRIVATE_KEY}
      - RESELLERCLUB_USER_ID=${RESELLERCLUB_USER_ID}
      - RESELLERCLUB_API_KEY=${RESELLERCLUB_API_KEY}
      # SSL Configuration
      - LETSENCRYPT_ENABLED=true
      - LETSENCRYPT_EMAIL=${LETSENCRYPT_EMAIL}
      - SSL_AUTO_RENEWAL=true
      # DNS Configuration
      - CLOUDFLARE_API_TOKEN=${CLOUDFLARE_API_TOKEN}
      - DNS_AUTO_CONFIG=true
    depends_on:
    
