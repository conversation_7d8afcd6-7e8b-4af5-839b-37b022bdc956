"""
Processing Schemas
==================

Schemas Pydantic para operações de processamento de mídia.

✅ MIGRAÇÃO CONCLUÍDA - Usando shared_lib para configurações comuns
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from uuid import UUID

from pydantic import BaseModel, Field


class ProcessingJobBase(BaseModel):
    """Schema base para job de processamento."""
    
    job_type: str = Field(..., max_length=50, description="Tipo do job")
    priority: int = Field(default=5, ge=1, le=10, description="Prioridade do job")
    parameters: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Parâmetros do job")


class ProcessingJobCreate(ProcessingJobBase):
    """Schema para criação de job de processamento."""
    
    media_id: UUID = Field(..., description="ID da mídia")
    tenant_id: UUID = Field(..., description="ID do tenant")


class ProcessingJobUpdate(BaseModel):
    """Schema para atualização de job de processamento."""
    
    status: Optional[str] = None
    progress: Optional[int] = Field(None, ge=0, le=100)
    result_data: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None


class ProcessingJobResponse(ProcessingJobBase):
    """Schema para resposta de job de processamento."""
    
    id: UUID
    media_id: UUID
    tenant_id: UUID
    status: str
    progress: int
    result_data: Optional[Dict[str, Any]]
    error_message: Optional[str]
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    created_at: datetime
    
    class Config:
        from_attributes = True


class ProcessingRequest(BaseModel):
    """Schema para requisição de processamento."""
    
    media_id: UUID = Field(..., description="ID da mídia")
    job_types: List[str] = Field(..., description="Tipos de processamento")
    priority: int = Field(default=5, ge=1, le=10, description="Prioridade")
    parameters: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Parâmetros")


class ProcessingResponse(BaseModel):
    """Schema para resposta de processamento."""
    
    jobs: List[ProcessingJobResponse]
    batch_id: UUID
    estimated_completion: Optional[datetime]


class ThumbnailRequest(BaseModel):
    """Schema para requisição de thumbnail."""
    
    media_id: UUID = Field(..., description="ID da mídia")
    sizes: List[int] = Field(default=[150, 300, 600], description="Tamanhos dos thumbnails")
    quality: int = Field(default=85, ge=1, le=100, description="Qualidade da compressão")
    format: str = Field(default="webp", description="Formato de saída")


class ThumbnailResponse(BaseModel):
    """Schema para resposta de thumbnail."""
    
    job_id: UUID
    thumbnails: List[Dict[str, Any]]  # [{"size": 150, "url": "...", "path": "..."}]
    estimated_completion: Optional[datetime]


class OCRRequest(BaseModel):
    """Schema para requisição de OCR."""
    
    media_id: UUID = Field(..., description="ID da mídia")
    languages: List[str] = Field(default=["por", "eng"], description="Idiomas para OCR")
    confidence_threshold: int = Field(default=60, ge=0, le=100, description="Limite de confiança")
    extract_tables: bool = Field(default=False, description="Extrair tabelas")
    extract_forms: bool = Field(default=False, description="Extrair formulários")


class OCRResponse(BaseModel):
    """Schema para resposta de OCR."""
    
    job_id: UUID
    text: Optional[str] = None
    confidence: Optional[float] = None
    pages: Optional[List[Dict[str, Any]]] = None
    tables: Optional[List[Dict[str, Any]]] = None
    forms: Optional[List[Dict[str, Any]]] = None
    estimated_completion: Optional[datetime]


class CompressionRequest(BaseModel):
    """Schema para requisição de compressão."""
    
    media_id: UUID = Field(..., description="ID da mídia")
    quality: int = Field(default=85, ge=1, le=100, description="Qualidade da compressão")
    format: Optional[str] = Field(None, description="Formato de saída (opcional)")
    max_width: Optional[int] = Field(None, ge=1, description="Largura máxima")
    max_height: Optional[int] = Field(None, ge=1, description="Altura máxima")


class CompressionResponse(BaseModel):
    """Schema para resposta de compressão."""
    
    job_id: UUID
    original_size: int
    compressed_size: Optional[int] = None
    compression_ratio: Optional[float] = None
    estimated_completion: Optional[datetime]


class VideoProcessingRequest(BaseModel):
    """Schema para requisição de processamento de vídeo."""
    
    media_id: UUID = Field(..., description="ID da mídia")
    extract_thumbnail: bool = Field(default=True, description="Extrair thumbnail")
    thumbnail_time: int = Field(default=5, ge=0, description="Tempo para thumbnail (segundos)")
    compress: bool = Field(default=False, description="Comprimir vídeo")
    target_resolution: Optional[str] = Field(None, description="Resolução alvo (720p, 1080p)")
    target_bitrate: Optional[str] = Field(None, description="Bitrate alvo")


class VideoProcessingResponse(BaseModel):
    """Schema para resposta de processamento de vídeo."""
    
    job_id: UUID
    thumbnail_url: Optional[str] = None
    compressed_url: Optional[str] = None
    duration: Optional[float] = None
    resolution: Optional[str] = None
    bitrate: Optional[str] = None
    estimated_completion: Optional[datetime]


class AudioProcessingRequest(BaseModel):
    """Schema para requisição de processamento de áudio."""
    
    media_id: UUID = Field(..., description="ID da mídia")
    normalize: bool = Field(default=True, description="Normalizar áudio")
    target_format: Optional[str] = Field(None, description="Formato alvo")
    target_bitrate: Optional[str] = Field(None, description="Bitrate alvo")


class AudioProcessingResponse(BaseModel):
    """Schema para resposta de processamento de áudio."""
    
    job_id: UUID
    processed_url: Optional[str] = None
    duration: Optional[float] = None
    format: Optional[str] = None
    bitrate: Optional[str] = None
    estimated_completion: Optional[datetime]
