-- ============================================================================
-- CORE SERVICES DATABASES INITIALIZATION
-- ============================================================================

-- Create databases for Core Services
CREATE DATABASE auth_db;
CREATE DATABASE users_db;
CREATE DATABASE tenants_db;
CREATE DATABASE core_db;
CREATE DATABASE i18n_db;
CREATE DATABASE ghost_function_db;
CREATE DATABASE cdn_db;
CREATE DATABASE dns_bridge_db;
CREATE DATABASE domain_db;
CREATE DATABASE notification_db;
CREATE DATABASE payment_db;
CREATE DATABASE supplier_db;
CREATE DATABASE synapse_ai_db;
CREATE DATABASE media_system_db;

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE auth_db TO postgres;
GRANT ALL PRIVILEGES ON DATABASE users_db TO postgres;
GRANT ALL PRIVILEGES ON DATABASE tenants_db TO postgres;
GRANT ALL PRIVILEGES ON DATABASE core_db TO postgres;
GRANT ALL PRIVILEGES ON DATABASE i18n_db TO postgres;
GRANT ALL PRIVILEGES ON DATABASE ghost_function_db TO postgres;
GRANT ALL PRIVILEGES ON DATABASE cdn_db TO postgres;
GRANT ALL PRIVILEGES ON DATABASE dns_bridge_db TO postgres;
GRANT ALL PRIVILEGES ON DATABASE domain_db TO postgres;
GRANT ALL PRIVILEGES ON DATABASE notification_db TO postgres;
GRANT ALL PRIVILEGES ON DATABASE payment_db TO postgres;
GRANT ALL PRIVILEGES ON DATABASE supplier_db TO postgres;
GRANT ALL PRIVILEGES ON DATABASE synapse_ai_db TO postgres;
GRANT ALL PRIVILEGES ON DATABASE media_system_db TO postgres;
