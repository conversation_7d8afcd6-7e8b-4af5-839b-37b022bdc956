"""
HR Module Configuration for Shared Library Integration

This module provides HR-specific configuration that extends the shared library
base configurations while maintaining consistency across the platform.
"""

from typing import List, Dict, Any, Optional
from pydantic import Field
from microservices.core.shared_lib.config.vault_config import VaultBaseSettings
from microservices.core.shared_lib.config.kafka_config import KafkaSettings


class HRServiceSettings(VaultBaseSettings):
    """
    HR Module settings with integration to shared library configurations.
    
    Inherits common configurations from:
    - VaultBaseSettings: HashiCorp Vault configuration
    - Environment settings (DEBUG, LOG_LEVEL, etc.)
    """

    # ===== HR SERVICE SPECIFIC CONFIGURATIONS =====

    # Service Identity
    SERVICE_NAME: str = Field(default="hr-service", env="SERVICE_NAME")
    SERVICE_PORT: int = Field(default=8014, env="SERVICE_PORT")

    # Database Cluster (Citus Data) - HR Specific
    DATABASE_URL: str = Field(
        default="postgresql+asyncpg://hr_user:HrSecure2024!#$@trix-postgres-primary:5432/hr_db",
        env="DATABASE_URL"
    )
    DATABASE_POOL_SIZE: int = Field(default=20, env="DATABASE_POOL_SIZE")
    DATABASE_MAX_OVERFLOW: int = Field(default=30, env="DATABASE_MAX_OVERFLOW")
    DATABASE_POOL_TIMEOUT: int = Field(default=30, env="DATABASE_POOL_TIMEOUT")
    DATABASE_POOL_RECYCLE: int = Field(default=3600, env="DATABASE_POOL_RECYCLE")

    # Redis Cluster - HR Specific (employee sessions, LMS cache)
    REDIS_CLUSTER_NODES: List[str] = Field(
        default=[
            "redis-cluster-node-1:7000",
            "redis-cluster-node-2:7001",
            "redis-cluster-node-3:7002"
        ],
        env="REDIS_CLUSTER_NODES"
    )
    REDIS_PASSWORD: str = Field(..., env="REDIS_PASSWORD")

    # Service Mesh URLs - HR Specific
    USER_SERVICE_URL: str = Field(
        default="http://user-service.trix.svc.cluster.local:8002",
        env="USER_SERVICE_URL"
    )
    TENANT_SERVICE_URL: str = Field(
        default="http://tenant-service.trix.svc.cluster.local:8003",
        env="TENANT_SERVICE_URL"
    )
    AUTH_SERVICE_URL: str = Field(
        default="http://auth-service.trix.svc.cluster.local:8001",
        env="AUTH_SERVICE_URL"
    )
    NOTIFICATION_SERVICE_URL: str = Field(
        default="http://notification-service.trix.svc.cluster.local:8019",
        env="NOTIFICATION_SERVICE_URL"
    )

    # RabbitMQ - HR Specific
    RABBITMQ_URL: str = Field(
        default="amqp://hr_user:hr_pass@rabbitmq-cluster:5672/hr",
        env="RABBITMQ_URL"
    )

    # Observability - HR Specific
    JAEGER_AGENT_HOST: str = Field(default="jaeger-agent", env="JAEGER_AGENT_HOST")
    JAEGER_AGENT_PORT: int = Field(default=6831, env="JAEGER_AGENT_PORT")
    PROMETHEUS_METRICS_PORT: int = Field(default=9090, env="PROMETHEUS_METRICS_PORT")

    # HR Business Logic - Specific Settings
    MAX_EMPLOYEES_PER_TENANT: int = Field(default=10000, env="MAX_EMPLOYEES_PER_TENANT")
    TIME_TRACKING_PRECISION_MINUTES: int = Field(default=1, env="TIME_TRACKING_PRECISION_MINUTES")
    LMS_MAX_COURSE_DURATION_HOURS: int = Field(default=200, env="LMS_MAX_COURSE_DURATION_HOURS")
    GAMIFICATION_ENABLED: bool = Field(default=True, env="GAMIFICATION_ENABLED")
    PERFORMANCE_REVIEW_CYCLE_MONTHS: int = Field(default=12, env="PERFORMANCE_REVIEW_CYCLE_MONTHS")

    # File Storage - HR Specific
    DOCUMENT_STORAGE_PATH: str = Field(default="/app/storage/hr/documents", env="DOCUMENT_STORAGE_PATH")
    MAX_DOCUMENT_SIZE_MB: int = Field(default=50, env="MAX_DOCUMENT_SIZE_MB")
    ALLOWED_DOCUMENT_TYPES: List[str] = Field(
        default=["pdf", "doc", "docx", "jpg", "jpeg", "png"],
        env="ALLOWED_DOCUMENT_TYPES"
    )

    # LMS Configuration
    LMS_CERTIFICATE_TEMPLATE_PATH: str = Field(
        default="/app/templates/certificates",
        env="LMS_CERTIFICATE_TEMPLATE_PATH"
    )
    LMS_VIDEO_STORAGE_PATH: str = Field(
        default="/app/storage/hr/lms/videos",
        env="LMS_VIDEO_STORAGE_PATH"
    )

    # ===== SHARED CONFIGURATIONS (via shared_lib) =====

    @property
    def kafka_settings(self) -> KafkaSettings:
        """Returns Kafka configuration from shared_lib."""
        return KafkaSettings()

    def get_hr_vault_paths(self) -> Dict[str, str]:
        """Returns HR-specific Vault paths."""
        return {
            "service": self.get_vault_path("service"),
            "database": self.get_vault_path("database"),
            "redis": f"redis/{self.VAULT_ENVIRONMENT}/hr-service",
            "rabbitmq": f"messaging/{self.VAULT_ENVIRONMENT}/hr-service",
            "lms": f"hr/{self.VAULT_ENVIRONMENT}/lms",
            "documents": f"hr/{self.VAULT_ENVIRONMENT}/documents"
        }

    def get_hr_kafka_topics(self) -> Dict[str, str]:
        """Returns HR-specific Kafka topics."""
        kafka_config = self.kafka_settings
        return {
            "employee_events": kafka_config.get_service_topic("hr", "employee_events"),
            "lms_events": kafka_config.get_service_topic("hr", "lms_events"),
            "performance_events": kafka_config.get_service_topic("hr", "performance_events"),
            "gamification_events": kafka_config.get_service_topic("hr", "gamification_events"),
            "time_tracking_events": kafka_config.get_service_topic("hr", "time_tracking_events"),
            "audit_events": kafka_config.get_service_topic("hr", "audit_events")
        }

    class Config:
        env_file = ".env"
        case_sensitive = True


# Global instance for easy import (lazy loading)
_hr_settings = None


def get_hr_settings() -> HRServiceSettings:
    """
    Get HR service settings instance with lazy loading.
    
    Returns:
        HRServiceSettings: Configured HR settings instance
    """
    global _hr_settings
    if _hr_settings is None:
        _hr_settings = HRServiceSettings()
    return _hr_settings


# HR-specific configuration constants
HR_EMPLOYEE_STATUS_CHOICES = [
    "active",
    "inactive", 
    "on_leave",
    "terminated"
]

HR_EMPLOYMENT_TYPE_CHOICES = [
    "full_time",
    "part_time", 
    "contract",
    "temporary",
    "intern"
]

HR_COURSE_DIFFICULTY_LEVELS = [
    "beginner",
    "intermediate",
    "advanced",
    "expert"
]

HR_PERFORMANCE_RATING_SCALE = [
    "needs_improvement",
    "meets_expectations", 
    "exceeds_expectations",
    "outstanding"
]

# HR Gamification Configuration
HR_GAMIFICATION_CONFIG = {
    "levels": [
        {"level": 1, "name": "Novato", "min_points": 0, "max_points": 999},
        {"level": 2, "name": "Aprendiz", "min_points": 1000, "max_points": 2499},
        {"level": 3, "name": "Profissional", "min_points": 2500, "max_points": 4999},
        {"level": 4, "name": "Especialista", "min_points": 5000, "max_points": 9999},
        {"level": 5, "name": "Mestre", "min_points": 10000, "max_points": 19999},
        {"level": 6, "name": "Lenda", "min_points": 20000, "max_points": None}
    ],
    "achievement_categories": [
        "performance",
        "education", 
        "customer_service",
        "leadership",
        "innovation",
        "teamwork"
    ]
}

# HR Department Templates by Tenant Type
HR_DEPARTMENT_TEMPLATES = {
    "restaurant": [
        {"name": "Cozinha", "description": "Departamento de produção culinária"},
        {"name": "Atendimento", "description": "Departamento de atendimento ao cliente"},
        {"name": "Administração", "description": "Departamento administrativo"}
    ],
    "consultancy": [
        {"name": "Contabilidade", "description": "Departamento de contabilidade"},
        {"name": "Auditoria", "description": "Departamento de auditoria"},
        {"name": "Consultoria", "description": "Departamento de consultoria fiscal"}
    ],
    "shop": [
        {"name": "Vendas", "description": "Departamento de vendas"},
        {"name": "Estoque", "description": "Departamento de estoque"},
        {"name": "Administração", "description": "Departamento administrativo"}
    ]
}
