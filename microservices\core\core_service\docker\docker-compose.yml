# ============================================================================
# CORE SERVICES - Enterprise Microservice
# ============================================================================
# Porta: 8010 | Database: Citus Data Cluster | Redis: Multiple DBs
# Submódulos: audit (Sistema de Auditoria), eshop (E-commerce B2B/B2C), help_center (Centro de Ajuda)
# Enterprise Features: Event-driven, Observability, Security, Scalability
# ============================================================================

services:
  # ============================================================================
  # CORE SERVICES - Enterprise Service
  # ============================================================================
  trix-core-services:
    build:
      context: ../
      dockerfile: docker/Dockerfile
    container_name: trix-core-services
    ports:
      - "8010:8000"
      - "9090:9090"  # Prometheus metrics
    environment:
      # Service Configuration
      - SERVICE_NAME=core-service
      - SERVICE_VERSION=2.0.0
      - ENVIRONMENT=${ENVIRONMENT:-development}
      - DEBUG=${DEBUG:-false}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - HOST=0.0.0.0
      - PORT=8000

      # Database - Citus Core Coordinator
      - DATABASE_URL=postgresql+asyncpg://postgres:TrixSuperSecure2024!@trix-citus-core-coordinator:5432/core_db
      - CITUS_COORDINATOR_URL=postgresql+asyncpg://postgres:TrixSuperSecure2024!@trix-citus-core-coordinator:5432/core_db
      - CITUS_WORKER_URLS=postgresql+asyncpg://postgres:TrixSuperSecure2024!@trix-citus-worker-1:5432/core_db,postgresql+asyncpg://postgres:TrixSuperSecure2024!@trix-citus-worker-2:5432/core_db
      - DATABASE_POOL_SIZE=20
      - DATABASE_MAX_OVERFLOW=30

      # Redis Configuration
      - REDIS_URL=redis://:${REDIS_PASSWORD}@trix-redis:6379/5
      - REDIS_STREAMS_URL=redis://:${REDIS_PASSWORD}@trix-redis-streams:6379/0

      # Messaging - Event-Driven Architecture
      - KAFKA_ENABLED=true
      - KAFKA_BOOTSTRAP_SERVERS=trix-kafka-1:9092,trix-kafka-2:9092,trix-kafka-3:9092
      - KAFKA_CONSUMER_GROUP=core-service
      - RABBITMQ_ENABLED=true
      - RABBITMQ_URL=amqp://${CORE_DB_USER}:${CORE_DB_PASSWORD}@trix-rabbitmq:5672/
      - REDIS_STREAMS_ENABLED=true

      # Security - HashiCorp Vault & OPA
      - VAULT_ENABLED=true
      - VAULT_URL=https://trix-vault:8200
      - VAULT_TOKEN=${VAULT_TOKEN}
      - VAULT_NAMESPACE=core-service
      - OPA_ENABLED=true
      - OPA_URL=http://trix-opa:8181
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}

      # Service Mesh - Istio
      - ISTIO_ENABLED=true
      - MTLS_MODE=STRICT
      - SERVICE_MESH_NAMESPACE=trix-core

      # Observability - Prometheus
      - PROMETHEUS_ENABLED=true
      - PROMETHEUS_PORT=9090
      - PROMETHEUS_GATEWAY_URL=http://trix-prometheus-pushgateway:9091
      - PROMETHEUS_JOB_NAME=core-service

      # Observability - Jaeger
      - JAEGER_ENABLED=true
      - JAEGER_AGENT_HOST=trix-jaeger-agent
      - JAEGER_AGENT_PORT=6831
      - JAEGER_SERVICE_NAME=core-service

      # Observability - ELK Stack
      - ELASTICSEARCH_ENABLED=true
      - ELASTICSEARCH_URL=https://trix-elasticsearch:9200
      - ELASTICSEARCH_INDEX_PREFIX=core-service

      # Service Integration URLs
      - AUTH_SERVICE_URL=http://auth-service.trix-core.svc.cluster.local:8001
      - USER_SERVICE_URL=http://user-service.trix-core.svc.cluster.local:8002
      - TENANT_SERVICE_URL=http://tenant-service.trix-core.svc.cluster.local:8003
      - SUPPLIER_SERVICE_URL=http://supplier-service.trix-core.svc.cluster.local:8004
      - COMMERCE_SERVICE_URL=http://commerce-service.trix-business.svc.cluster.local:8005
      - PAYMENT_SERVICE_URL=http://payment-service.trix-business.svc.cluster.local:8006
      - NOTIFICATION_SERVICE_URL=http://notification-service.trix-core.svc.cluster.local:8007
      - I18N_SERVICE_URL=http://i18n-service.trix-core.svc.cluster.local:8008
      - CDN_SERVICE_URL=http://cdn-service.trix-core.svc.cluster.local:8009

      # Module Settings
      - AUDIT_RETENTION_DAYS=365
      - AUDIT_BATCH_SIZE=1000
      - ESHOP_COMMISSION_RATE=0.05
      - ESHOP_B2B_ENABLED=true
      - ESHOP_B2C_ENABLED=true
      - HELP_CENTER_SLA_HOURS=24
      - HELP_CENTER_WEBSOCKET_ENABLED=true

      # Performance & Scaling
      - AUTO_SCALING_ENABLED=true
      - MIN_REPLICAS=3
      - MAX_REPLICAS=100
      - TARGET_CPU_UTILIZATION=70
      - TARGET_MEMORY_UTILIZATION=80
      - REGION=us-east-1

      # CDN Configuration
      - CDN_ENABLED=true
      - VARNISH_URL=http://trix-varnish:80
      - MINIO_ENDPOINT=http://trix-minio:9000
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY}

      # Background Tasks
      - CELERY_BROKER_URL=redis://:${REDIS_PASSWORD}@trix-redis-celery:6379/0
      - CELERY_RESULT_BACKEND=redis://:${REDIS_PASSWORD}@trix-redis-celery:6379/1
      - CELERY_WORKER_CONCURRENCY=4

      # Feature Flags
      - FEATURE_AUDIT_ENABLED=true
      - FEATURE_ESHOP_ENABLED=true
      - FEATURE_HELP_CENTER_ENABLED=true
      - FEATURE_REAL_TIME_EVENTS=true
      - FEATURE_ADVANCED_ANALYTICS=true

    depends_on:
      - trix-db-core
      - trix-redis

    networks:
      - trix-network

    restart: unless-stopped

    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 512M

    volumes:
      - ./logs:/app/logs
      - ./config:/app/config

    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.core-service.rule=Host(`core.trix.local`)"
      - "traefik.http.routers.core-service.tls=true"
      - "traefik.http.services.core-service.loadbalancer.server.port=8000"
      - "prometheus.io/scrape=true"
      - "prometheus.io/port=9090"
      - "prometheus.io/path=/metrics"

  # ============================================================================
  # CORE DATABASE - PostgreSQL dedicado para Core Services
  # ============================================================================
  trix-db-core:
    image: postgres:15-alpine
    container_name: trix-db-core
    environment:
      POSTGRES_DB: ${CORE_DB_NAME}
      POSTGRES_USER: ${CORE_DB_USER}
      POSTGRES_PASSWORD: ${CORE_DB_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5438:5432"
    volumes:
      - trix_core_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${CORE_DB_USER} -d ${CORE_DB_NAME}"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - trix-network
    restart: unless-stopped
    labels:
      - "trix.category=database"
      - "trix.service=postgresql"
      - "trix.module=core"
      - "trix.port=5438"

networks:
  trix-network:
    external: true

volumes:
  core_logs:
    driver: local
  core_config:
    driver: local
    
