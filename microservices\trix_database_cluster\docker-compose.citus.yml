version: '3.9'

services:
  # ============================================================================
  # CORE SERVICES COORDINATOR
  # ============================================================================
  trix-citus-core-coordinator:
    image: citusdata/citus:12.1
    container_name: trix-citus-core-coordinator
    hostname: trix-citus-core-coordinator
    restart: unless-stopped
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: TrixSuperSecure2024!
      CITUS_HOST: trix-citus-core-coordinator
      CITUS_PORT: 5432
      PGUSER: postgres
      PGPASSWORD: TrixSuperSecure2024!
    ports:
      - "5432:5432"
    volumes:
      - citus_core_coordinator_data:/var/lib/postgresql/data
      - ./citus-config/core-coordinator.conf:/etc/postgresql/postgresql.conf:ro
      - ./citus-config/pg_hba.conf:/etc/postgresql/pg_hba.conf:ro
      - ./citus-init-scripts/core:/docker-entrypoint-initdb.d:ro
    command: >
      postgres
      -c shared_preload_libraries=citus
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c wal_level=replica
      -c max_wal_senders=10
      -c max_replication_slots=10
      -c citus.shard_count=32
      -c citus.shard_replication_factor=2
    networks:
      - trix-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d postgres"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    labels:
      - "trix.citus.role=coordinator"
      - "trix.citus.domain=core"
      - "trix.microservices=auth,user,tenant,core,i18n"

  # ============================================================================
  # TENANT SERVICES COORDINATOR
  # ============================================================================
  trix-citus-tenant-coordinator:
    image: citusdata/citus:12.1
    container_name: trix-citus-tenant-coordinator
    hostname: trix-citus-tenant-coordinator
    restart: unless-stopped
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: TrixSuperSecure2024!
      CITUS_HOST: trix-citus-tenant-coordinator
      CITUS_PORT: 5432
      PGUSER: postgres
      PGPASSWORD: TrixSuperSecure2024!
    ports:
      - "5433:5432"
    volumes:
      - citus_tenant_coordinator_data:/var/lib/postgresql/data
      - ./citus-config/tenant-coordinator.conf:/etc/postgresql/postgresql.conf:ro
      - ./citus-config/pg_hba.conf:/etc/postgresql/pg_hba.conf:ro
      - ./citus-init-scripts/tenant:/docker-entrypoint-initdb.d:ro
    command: >
      postgres
      -c shared_preload_libraries=citus
      -c max_connections=300
      -c shared_buffers=512MB
      -c effective_cache_size=2GB
      -c wal_level=replica
      -c max_wal_senders=10
      -c max_replication_slots=10
      -c citus.shard_count=64
      -c citus.shard_replication_factor=2
      -c citus.enable_repartition_joins=on
    networks:
      - trix-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d postgres"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    labels:
      - "trix.citus.role=coordinator"
      - "trix.citus.domain=tenant"
      - "trix.microservices=restaurant,consultancy,shop"

  # ============================================================================
  # SHARED SERVICES COORDINATOR
  # ============================================================================
  trix-citus-shared-coordinator:
    image: citusdata/citus:12.1
    container_name: trix-citus-shared-coordinator
    hostname: trix-citus-shared-coordinator
    restart: unless-stopped
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: TrixSuperSecure2024!
      CITUS_HOST: trix-citus-shared-coordinator
      CITUS_PORT: 5432
      PGUSER: postgres
      PGPASSWORD: TrixSuperSecure2024!
    ports:
      - "5434:5432"
    volumes:
      - citus_shared_coordinator_data:/var/lib/postgresql/data
      - ./citus-config/shared-coordinator.conf:/etc/postgresql/postgresql.conf:ro
      - ./citus-config/pg_hba.conf:/etc/postgresql/pg_hba.conf:ro
      - ./citus-init-scripts/shared:/docker-entrypoint-initdb.d:ro
    command: >
      postgres
      -c shared_preload_libraries=citus
      -c max_connections=250
      -c shared_buffers=384MB
      -c effective_cache_size=1.5GB
      -c wal_level=replica
      -c max_wal_senders=10
      -c max_replication_slots=10
      -c citus.shard_count=32
      -c citus.shard_replication_factor=2
    networks:
      - trix-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d postgres"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    labels:
      - "trix.citus.role=coordinator"
      - "trix.citus.domain=shared"
      - "trix.microservices=hr,crm,email,financial,notifications"

  # ============================================================================
  # CITUS WORKERS (Compartilhados entre coordenadores)
  # ============================================================================
  trix-citus-worker-1:
    image: citusdata/citus:12.1
    container_name: trix-citus-worker-1
    hostname: trix-citus-worker-1
    restart: unless-stopped
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: TrixSuperSecure2024!
      PGUSER: postgres
      PGPASSWORD: TrixSuperSecure2024!
    ports:
      - "5435:5432"
    volumes:
      - citus_worker1_data:/var/lib/postgresql/data
      - ./citus-config/worker.conf:/etc/postgresql/postgresql.conf:ro
      - ./citus-config/pg_hba.conf:/etc/postgresql/pg_hba.conf:ro
    command: >
      postgres
      -c shared_preload_libraries=citus
      -c max_connections=100
      -c shared_buffers=512MB
      -c effective_cache_size=2GB
      -c wal_level=replica
    networks:
      - trix-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d postgres"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    labels:
      - "trix.citus.role=worker"
      - "trix.citus.worker.id=1"

  trix-citus-worker-2:
    image: citusdata/citus:12.1
    container_name: trix-citus-worker-2
    hostname: trix-citus-worker-2
    restart: unless-stopped
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: TrixSuperSecure2024!
      PGUSER: postgres
      PGPASSWORD: TrixSuperSecure2024!
    ports:
      - "5436:5432"
    volumes:
      - citus_worker2_data:/var/lib/postgresql/data
      - ./citus-config/worker.conf:/etc/postgresql/postgresql.conf:ro
      - ./citus-config/pg_hba.conf:/etc/postgresql/pg_hba.conf:ro
    command: >
      postgres
      -c shared_preload_libraries=citus
      -c max_connections=100
      -c shared_buffers=512MB
      -c effective_cache_size=2GB
      -c wal_level=replica
    networks:
      - trix-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d postgres"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    labels:
      - "trix.citus.role=worker"
      - "trix.citus.worker.id=2"

  # ============================================================================
  # PGADMIN - DATABASE ADMINISTRATION
  # ============================================================================
  trix-citus-pgadmin:
    image: dpage/pgadmin4:latest
    container_name: trix-citus-pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: TrixAdmin2024!
      PGADMIN_CONFIG_SERVER_MODE: 'False'
      PGADMIN_CONFIG_MASTER_PASSWORD_REQUIRED: 'False'
    ports:
      - "8082:80"
    volumes:
      - citus_pgadmin_data:/var/lib/pgadmin
      - ./citus-config/citus-servers.json:/pgadmin4/servers.json:ro
    depends_on:
      trix-citus-core-coordinator:
        condition: service_healthy
      trix-citus-tenant-coordinator:
        condition: service_healthy
      trix-citus-shared-coordinator:
        condition: service_healthy
    networks:
      - trix-network
    labels:
      - "trix.citus.service=pgadmin"
      - "trix.citus.role=admin"

  # ============================================================================
  # CITUS MONITOR - Monitoramento do Cluster
  # ============================================================================
  trix-citus-monitor:
    image: citusdata/citus:12.1
    container_name: trix-citus-monitor
    hostname: trix-citus-monitor
    restart: unless-stopped
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: TrixSuperSecure2024!
    volumes:
      - ./citus-scripts:/scripts:ro
    command: >
      bash -c "
      echo 'Citus Monitor iniciado...'
      while true; do
        echo '=== Citus Cluster Status ==='
        date
        echo 'Verificando coordenadores...'
        pg_isready -h trix-citus-core-coordinator -p 5432 -U postgres && echo 'Core Coordinator: OK' || echo 'Core Coordinator: FAIL'
        pg_isready -h trix-citus-tenant-coordinator -p 5432 -U postgres && echo 'Tenant Coordinator: OK' || echo 'Tenant Coordinator: FAIL'
        pg_isready -h trix-citus-shared-coordinator -p 5432 -U postgres && echo 'Shared Coordinator: OK' || echo 'Shared Coordinator: FAIL'
        echo 'Verificando workers...'
        pg_isready -h trix-citus-worker-1 -p 5432 -U postgres && echo 'Worker 1: OK' || echo 'Worker 1: FAIL'
        pg_isready -h trix-citus-worker-2 -p 5432 -U postgres && echo 'Worker 2: OK' || echo 'Worker 2: FAIL'
        echo '=========================='
        sleep 60
      done
      "
    networks:
      - trix-network
    depends_on:
      - trix-citus-core-coordinator
      - trix-citus-tenant-coordinator
      - trix-citus-shared-coordinator
      - trix-citus-worker-1
      - trix-citus-worker-2
    labels:
      - "trix.citus.service=monitor"
      - "trix.citus.role=monitoring"

# ============================================================================
# NETWORKS
# ============================================================================
networks:
  trix-network:
    external: true

# ============================================================================
# VOLUMES
# ============================================================================
volumes:
  citus_core_coordinator_data:
    driver: local
    name: trix_citus_core_coordinator_data
  citus_tenant_coordinator_data:
    driver: local
    name: trix_citus_tenant_coordinator_data
  citus_shared_coordinator_data:
    driver: local
    name: trix_citus_shared_coordinator_data
  citus_worker1_data:
    driver: local
    name: trix_citus_worker1_data
  citus_worker2_data:
    driver: local
    name: trix_citus_worker2_data
  citus_pgadmin_data:
    driver: local
    name: trix_citus_pgadmin_data
