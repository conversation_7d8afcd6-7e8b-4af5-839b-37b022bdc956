"""
User Service Settings Configuration
==================================
User Service specific settings that extend shared_lib configurations.
Follows the pattern established by auth_service for consistency.
"""

from typing import List, Optional, Dict, Any
from pydantic import Field, field_validator
from pydantic_settings import SettingsConfigDict

# Import shared configurations from shared_lib
from microservices.core.shared_lib.config.vault_config import VaultBaseSettings
from microservices.core.shared_lib.config.kafka_config import KafkaSettings


class UserServiceSettings(VaultBaseSettings):
    """
    User Service specific settings extending shared_lib configurations.

    Inherits common configurations from:
    - VaultBaseSettings: Vault configuration and environment settings
    - Uses shared Kafka configuration from shared_lib
    """

    # ===== USER SERVICE SPECIFIC SETTINGS =====

    # Service Identity
    SERVICE_NAME: str = Field(default="user-service", env="SERVICE_NAME")
    SERVICE_PORT: int = Field(default=8002, env="SERVICE_PORT")

    # API Configuration
    API_V1_PREFIX: str = Field(default="/api/v1", env="API_V1_PREFIX")

    # Database Configuration - User Service specific
    DATABASE_URL: str = Field(
        default="postgresql+asyncpg://users_user:UsersSecure2024!#$@citus-coordinator:5432/users_db",
        env="USERS_DATABASE_URL"
    )
    DATABASE_POOL_SIZE: int = Field(default=20, env="DATABASE_POOL_SIZE")
    DATABASE_MAX_OVERFLOW: int = Field(default=30, env="DATABASE_MAX_OVERFLOW")
    DATABASE_POOL_TIMEOUT: int = Field(default=30, env="DATABASE_POOL_TIMEOUT")
    DATABASE_POOL_RECYCLE: int = Field(default=3600, env="DATABASE_POOL_RECYCLE")

    # Redis Configuration - User Service specific
    REDIS_CLUSTER_NODES: List[str] = Field(
        default=[
            "redis-cluster-node-1:7000",
            "redis-cluster-node-2:7001",
            "redis-cluster-node-3:7002"
        ],
        env="REDIS_CLUSTER_NODES"
    )
    REDIS_PASSWORD: str = Field(..., env="REDIS_PASSWORD")

    # Service Mesh URLs - User Service specific
    AUTH_SERVICE_URL: str = Field(
        default="http://auth-service.trix.svc.cluster.local:8001",
        env="AUTH_SERVICE_URL"
    )
    TENANT_SERVICE_URL: str = Field(
        default="http://tenant-service.trix.svc.cluster.local:8003",
        env="TENANT_SERVICE_URL"
    )
    NOTIFICATION_SERVICE_URL: str = Field(
        default="http://notification-service.trix.svc.cluster.local:8019",
        env="NOTIFICATION_SERVICE_URL"
    )


    # RabbitMQ Configuration - User Service specific
    RABBITMQ_URL: str = Field(
        default="amqp://user_user:user_pass@rabbitmq-cluster:5672/user",
        env="RABBITMQ_URL"
    )

    # Observability - User Service specific
    JAEGER_AGENT_HOST: str = Field(default="jaeger-agent", env="JAEGER_AGENT_HOST")
    JAEGER_AGENT_PORT: int = Field(default=6831, env="JAEGER_AGENT_PORT")
    PROMETHEUS_METRICS_PORT: int = Field(default=9090, env="PROMETHEUS_METRICS_PORT")

    # User Service Business Logic Settings
    USER_REGISTRATION_ENABLED: bool = Field(default=True, env="USER_REGISTRATION_ENABLED")
    EMAIL_VERIFICATION_REQUIRED: bool = Field(default=True, env="EMAIL_VERIFICATION_REQUIRED")
    TERMS_ACCEPTANCE_REQUIRED: bool = Field(default=True, env="TERMS_ACCEPTANCE_REQUIRED")

    # Gamification Settings
    GAMIFICATION_ENABLED: bool = Field(default=True, env="GAMIFICATION_ENABLED")
    DEFAULT_USER_XP: int = Field(default=0, env="DEFAULT_USER_XP")
    DEFAULT_USER_LEVEL: int = Field(default=1, env="DEFAULT_USER_LEVEL")
    DEFAULT_USER_COINS: int = Field(default=100, env="DEFAULT_USER_COINS")

    # ===== SHARED CONFIGURATIONS (via shared_lib) =====

    @property
    def kafka_settings(self) -> KafkaSettings:
        """Returns Kafka settings from shared_lib."""
        return KafkaSettings()

    def get_user_vault_paths(self) -> dict:
        """Returns User Service specific Vault paths."""
        return {
            "service": self.get_vault_path("service"),
            "database": self.get_vault_path("database"),
            "jwt": self.get_vault_path("jwt"),
            "redis": f"redis/{self.VAULT_ENVIRONMENT}/user-service",
            "rabbitmq": f"messaging/{self.VAULT_ENVIRONMENT}/user-service"
        }


    class Config:
        env_file = ".env"
        case_sensitive = True


# Global settings instance
settings = UserServiceSettings()


def get_settings() -> UserServiceSettings:
    """Get cached settings instance."""
    return settings



