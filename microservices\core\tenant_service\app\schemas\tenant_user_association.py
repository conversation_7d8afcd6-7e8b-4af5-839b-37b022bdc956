"""
Tenant User Association schemas for API serialization and validation.
"""

from __future__ import annotations
import uuid
from datetime import datetime
from typing import Optional, List, Dict, Any

from pydantic import BaseModel, ConfigDict, Field, field_validator, ValidationInfo

from app.models.tenant_user_association import TenantAssociationType


class TenantUserAssociationBase(BaseModel):
    """Base schema for tenant user associations."""
    
    user_id: uuid.UUID = Field(..., description="User ID from user service")
    tenant_id: uuid.UUID = Field(..., description="Associated tenant ID")
    association_type: TenantAssociationType = Field(..., description="Type of association")
    is_active: bool = Field(default=True, description="Whether association is active")
    subrole: Optional[str] = Field(None, description="Subrole for staff members")
    authorizations: Optional[List[str]] = Field(None, description="Special authorizations")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")

    @field_validator('subrole', mode='after')
    @classmethod
    def validate_subrole(cls, v, info: ValidationInfo):
        """Validate subrole based on association type."""
        association_type = info.data.get('association_type')

        if association_type == TenantAssociationType.STAFF and not v:
            raise ValueError('Subrole is required for staff associations')

        if association_type != TenantAssociationType.STAFF and v:
            raise ValueError('Subrole is only allowed for staff associations')

        return v

    @field_validator('authorizations', mode='before')
    @classmethod
    def validate_authorizations(cls, v):
        """Validate authorizations."""
        if v is not None:
            allowed_auths = ['tsupplier', 'admin', 'manager']
            for auth in v:
                if auth.lower() not in allowed_auths:
                    raise ValueError(f'Invalid authorization: {auth}')
        return v


class TenantUserAssociationCreate(TenantUserAssociationBase):
    """Schema for creating a new tenant user association."""
    pass


class TenantUserAssociationUpdate(BaseModel):
    """Schema for updating an existing tenant user association."""
    
    association_type: Optional[TenantAssociationType] = None
    is_active: Optional[bool] = None
    is_verified: Optional[bool] = None
    subrole: Optional[str] = None
    authorizations: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None

    @field_validator('authorizations', mode='before')
    @classmethod
    def validate_authorizations(cls, v):
        """Validate authorizations."""
        if v is not None:
            allowed_auths = ['tsupplier', 'admin', 'manager']
            for auth in v:
                if auth.lower() not in allowed_auths:
                    raise ValueError(f'Invalid authorization: {auth}')
        return v


class TenantUserAssociationInDBBase(TenantUserAssociationBase):
    """Base schema for association data from database."""
    
    id: uuid.UUID
    is_verified: bool = False
    created_by: Optional[uuid.UUID] = None
    approved_by: Optional[uuid.UUID] = None
    approved_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    
    model_config = ConfigDict(from_attributes=True)


class TenantUserAssociation(TenantUserAssociationInDBBase):
    """Complete association schema for API responses."""
    pass


class TenantUserAssociationSimple(BaseModel):
    """Simplified association schema for listings."""
    
    id: uuid.UUID
    user_id: uuid.UUID
    tenant_id: uuid.UUID
    association_type: TenantAssociationType
    is_active: bool
    is_verified: bool
    subrole: Optional[str] = None
    
    model_config = ConfigDict(from_attributes=True)


class TenantUserAssociationWithTenant(TenantUserAssociation):
    """Association schema with tenant information."""
    
    tenant_name: Optional[str] = None
    tenant_slug: Optional[str] = None
    tenant_type: Optional[str] = None


class TenantUserAssociationWithUser(TenantUserAssociation):
    """Association schema with user information."""
    
    user_email: Optional[str] = None
    user_name: Optional[str] = None


class UserTenantSummary(BaseModel):
    """Summary of user's tenant associations."""
    
    user_id: uuid.UUID
    associations: List[TenantUserAssociationSimple]
    total_tenants: int
    active_associations: int
    owner_associations: int
    staff_associations: int
    customer_associations: int
    supplier_associations: int
    has_tsupplier_auth: bool


class TenantUserSummary(BaseModel):
    """Summary of tenant's user associations."""
    
    tenant_id: uuid.UUID
    associations: List[TenantUserAssociationSimple]
    total_users: int
    active_associations: int
    owners: int
    staff: int
    customers: int
    suppliers: int


class AssociationRequest(BaseModel):
    """Schema for requesting tenant association."""
    
    tenant_id: uuid.UUID = Field(..., description="Tenant to associate with")
    association_type: TenantAssociationType = Field(..., description="Type of association requested")
    subrole: Optional[str] = Field(None, description="Requested subrole (for staff)")
    message: Optional[str] = Field(None, description="Message to tenant owner")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional request data")

    @field_validator('subrole', mode='after')
    @classmethod
    def validate_subrole(cls, v, info: ValidationInfo):
        """Validate subrole based on association type."""
        association_type = info.data.get('association_type')

        if association_type == TenantAssociationType.STAFF and not v:
            raise ValueError('Subrole is required for staff association requests')

        return v


class AssociationApproval(BaseModel):
    """Schema for approving/rejecting association requests."""
    
    approved: bool = Field(..., description="Whether to approve the request")
    subrole: Optional[str] = Field(None, description="Assigned subrole (for staff)")
    authorizations: Optional[List[str]] = Field(None, description="Granted authorizations")
    message: Optional[str] = Field(None, description="Message to requester")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional approval data")


class BulkAssociationOperation(BaseModel):
    """Schema for bulk association operations."""
    
    operation: str = Field(..., description="Operation type: create, update, delete")
    associations: List[Dict[str, Any]] = Field(..., description="List of associations to process")
    
    @field_validator('operation', mode='before')
    @classmethod
    def validate_operation(cls, v):
        """Validate operation type."""
        allowed_ops = ['create', 'update', 'delete', 'activate', 'deactivate']
        if v not in allowed_ops:
            raise ValueError(f'Operation must be one of: {", ".join(allowed_ops)}')
        return v
