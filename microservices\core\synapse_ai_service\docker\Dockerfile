# Multi-stage Dockerfile for Synapse AI Service
# Stage 1: Build dependencies
FROM python:3.11-slim as requirements-stage

WORKDIR /tmp

# Install Poetry and export plugin
RUN pip install poetry poetry-plugin-export

# Copy poetry files
COPY ./pyproject.toml ./poetry.lock* /tmp/

# Export requirements
RUN poetry export -f requirements.txt --output requirements.txt --without-hashes

# Stage 2: Production image
FROM python:3.11-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Create app user
RUN groupadd -r app && useradd -r -g app app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Set work directory
WORKDIR /code

# Copy requirements from build stage
COPY --from=requirements-stage /tmp/requirements.txt /code/requirements.txt

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade -r /code/requirements.txt

# Copy application code
COPY ./app /code/app

# Create necessary directories
RUN mkdir -p /code/logs && \
    chown -R app:app /code

# Switch to app user
USER app

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8020/health || exit 1

# Expose port
EXPOSE 8020

# Run the application
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8020", "--reload"]
