# ============================================================================
# CONSULTANCY SERVICE - Microserviço de Consultoria
# ============================================================================
# Porta: 8005 | Database: postgres-consultancy (5436) | Redis: DB 3
# Submódulos: case_management, task_management, client_management, billing_invoicing, compliance_management, document_management, employee_assignments, legal_consultation, tax_preparation, time_tracking, workflows, sector_templates
# ============================================================================

services:
  # ============================================================================
  # CONSULTANCY SERVICE - Serviço Principal
  # ============================================================================
  trix-tenant-consultancy:
    build:
      context: ../
      dockerfile: docker/Dockerfile
    container_name: trix-tenant-consultancy
    ports:
      - "8005:8000"
    environment:
      - DATABASE_URL=postgresql+asyncpg://postgres:TrixSuperSecure2024!@trix-citus-tenant-coordinator:5432/consultancy_db
      - REDIS_URL=redis://:${REDIS_PASSWORD}@trix-redis:6379/3
      - AUTH_SERVICE_URL=http://trix-core-auth:8001
      - USER_SERVICE_URL=http://trix-core-user:8002
      - TENANT_SERVICE_URL=http://trix-core-tenant:8003
      - FINANCIAL_SERVICE_URL=http://trix-shared-financial:8013
      - HR_SERVICE_URL=http://trix-shared-hr:8014
      - EMAIL_SERVICE_URL=http://trix-shared-email:8012
      - SECRET_KEY=${JWT_SECRET_KEY}
      - DEBUG=${DEBUG}
      - SERVICE_NAME=consultancy-service
      - SERVICE_VERSION=1.0.0
      - CELERY_BROKER_URL=redis://:${REDIS_PASSWORD}@trix-redis:6379/3
      - CELERY_RESULT_BACKEND=redis://:${REDIS_PASSWORD}@trix-redis:6379/3
      - DOCUMENT_STORAGE_PATH=/app/documents
      - DIGITAL_SIGNATURE_ENABLED=true
      - COMPLIANCE_ALERTS_ENABLED=true
    depends_on:
      - trix-redis
    external_links:
      - trix-citus-tenant-coordinator:trix-citus-tenant-coordinator
    volumes:
      - ../:/app
      - trix_consultancy_documents:/app/documents
      - trix_consultancy_templates:/app/templates
      - trix_consultancy_reports:/app/reports
      - trix_consultancy_backups:/app/backups
    networks:
      - trix-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    labels:
      - "trix.category=tenant"
      - "trix.service=microservice"
      - "trix.module=consultancy"
      - "trix.port=8005"

  # ============================================================================
  # DATABASE - Using Citus Tenant Coordinator (External)
  # ============================================================================
  # Database removed - using external Citus Tenant Coordinator
  # Connection: trix-citus-tenant-coordinator:5432/consultancy_db

networks:
  trix-network:
    external: true
