"""
Centralized Vault Configuration for Trix Microservices
======================================================

This module provides a standardized way to configure HashiCorp Vault
across all Trix microservices, ensuring consistency and reducing duplication.

Features:
- Environment-specific configurations
- Multiple authentication methods (token, kubernetes, approle)
- Type-safe configuration with Pydantic
- Easy integration with existing services
- Support for both development and production environments

Company ID: d6faa240-1cc7-4271-9223-baa60577f607
"""

import os
from typing import Optional, Dict, Any, Union
from pydantic import Field, SecretStr, field_validator
from pydantic_settings import BaseSettings, PydanticBaseSettingsSource, SettingsConfigDict

# Try to import pydantic-vault if available, otherwise use fallback
try:
    from pydantic_vault import VaultSettingsSource
    VAULT_INTEGRATION_AVAILABLE = True
except ImportError:
    VAULT_INTEGRATION_AVAILABLE = False
    VaultSettingsSource = None


class VaultConfig(BaseSettings):
    """
    Centralized Vault configuration class.
    
    This class defines all Vault-related settings that should be consistent
    across all Trix microservices.
    """
    
    # Basic Vault Configuration
    VAULT_URL: str = Field(
        default="http://vault:8200",
        env="VAULT_URL",
        description="URL do servidor Vault"
    )
    
    VAULT_TOKEN: str = Field(
        default="dev-root-token",
        env="VAULT_TOKEN",
        description="Token de autenticação do Vault"
    )
    
    VAULT_ENABLED: bool = Field(
        default=True,
        env="VAULT_ENABLED",
        description="Habilitar/desabilitar integração com Vault"
    )
    
    VAULT_MOUNT_POINT: str = Field(
        default="secret",
        env="VAULT_MOUNT_POINT",
        description="Ponto de montagem padrão do Vault"
    )
    
    # Company and Environment Configuration
    VAULT_COMPANY_ID: str = Field(
        default="d6faa240-1cc7-4271-9223-baa60577f607",
        env="VAULT_COMPANY_ID",
        description="ID da empresa no Vault"
    )
    
    VAULT_ENVIRONMENT: str = Field(
        default="development",
        env="VAULT_ENVIRONMENT",
        description="Ambiente atual (development, staging, production)"
    )
    
    # Advanced Vault Configuration
    VAULT_API_ADDR: str = Field(
        default="http://vault:8200",
        env="VAULT_API_ADDR",
        description="Endereço da API do Vault"
    )
    
    VAULT_CLUSTER_ADDR: str = Field(
        default="http://vault:8201",
        env="VAULT_CLUSTER_ADDR",
        description="Endereço do cluster do Vault"
    )
    
    # Authentication Configuration
    VAULT_AUTH_METHOD: str = Field(
        default="token",
        env="VAULT_AUTH_METHOD",
        description="Método de autenticação (token, kubernetes, approle, jwt)"
    )
    
    VAULT_ROLE_ID: Optional[str] = Field(
        default=None,
        env="VAULT_ROLE_ID",
        description="Role ID para autenticação AppRole"
    )
    
    VAULT_SECRET_ID: Optional[str] = Field(
        default=None,
        env="VAULT_SECRET_ID",
        description="Secret ID para autenticação AppRole"
    )
    
    VAULT_KUBERNETES_ROLE: Optional[str] = Field(
        default=None,
        env="VAULT_KUBERNETES_ROLE",
        description="Role para autenticação Kubernetes"
    )
    
    VAULT_JWT_ROLE: Optional[str] = Field(
        default=None,
        env="VAULT_JWT_ROLE",
        description="Role para autenticação JWT/OIDC"
    )
    
    VAULT_JWT_TOKEN: Optional[str] = Field(
        default=None,
        env="VAULT_JWT_TOKEN",
        description="Token JWT para autenticação"
    )
    
    VAULT_NAMESPACE: Optional[str] = Field(
        default=None,
        env="VAULT_NAMESPACE",
        description="Namespace do Vault (Enterprise)"
    )
    
    # Performance and Reliability Configuration
    VAULT_TIMEOUT: int = Field(
        default=30,
        env="VAULT_TIMEOUT",
        description="Timeout para operações do Vault (segundos)"
    )
    
    VAULT_CACHE_TTL: int = Field(
        default=300,
        env="VAULT_CACHE_TTL",
        description="TTL do cache de segredos (segundos)"
    )
    
    VAULT_RETRY_ATTEMPTS: int = Field(
        default=3,
        env="VAULT_RETRY_ATTEMPTS",
        description="Número de tentativas de retry"
    )
    
    VAULT_CERTIFICATE_VERIFY: Union[str, bool] = Field(
        default=True,
        env="VAULT_CA_BUNDLE",
        description="Verificação de certificado SSL"
    )
    
    @field_validator('VAULT_AUTH_METHOD')
    @classmethod
    def validate_auth_method(cls, v):
        """Valida se o método de autenticação é suportado."""
        valid_methods = ['token', 'kubernetes', 'approle', 'jwt']
        if v not in valid_methods:
            raise ValueError(f'Método de autenticação deve ser um de: {valid_methods}')
        return v
    
    @field_validator('VAULT_ENVIRONMENT')
    @classmethod
    def validate_environment(cls, v):
        """Valida se o ambiente é suportado."""
        valid_environments = ['development', 'staging', 'production']
        if v not in valid_environments:
            raise ValueError(f'Ambiente deve ser um de: {valid_environments}')
        return v
    
    model_config = SettingsConfigDict(
        case_sensitive=True,
        extra="ignore"
    )


class VaultBaseSettings(VaultConfig):
    """
    Classe base para configurações de microserviços com integração ao Vault.

    Esta classe deve ser herdada por todas as classes de configuração
    dos microserviços para garantir configuração consistente do Vault.

    Example:
        class MyServiceSettings(VaultBaseSettings):
            service_name: str = "my-service"
            service_port: int = 8000
            # Configurações específicas do serviço...
    """
    
    # Configurações básicas do serviço (podem ser sobrescritas)
    SERVICE_NAME: str = Field(
        default="trix-service",
        env="SERVICE_NAME",
        description="Nome do serviço"
    )
    
    SERVICE_VERSION: str = Field(
        default="1.0.0",
        env="SERVICE_VERSION",
        description="Versão do serviço"
    )
    
    SERVICE_ENVIRONMENT: str = Field(
        default="development",
        env="SERVICE_ENVIRONMENT",
        description="Ambiente do serviço"
    )
    
    DEBUG: bool = Field(
        default=False,
        env="DEBUG",
        description="Modo debug"
    )
    
    @classmethod
    def settings_customise_sources(
        cls,
        settings_cls: type[BaseSettings],
        init_settings: PydanticBaseSettingsSource,
        env_settings: PydanticBaseSettingsSource,
        dotenv_settings: PydanticBaseSettingsSource,
        file_secret_settings: PydanticBaseSettingsSource,
    ) -> tuple[PydanticBaseSettingsSource, ...]:
        """
        Customiza as fontes de configuração para incluir o Vault.
        
        Ordem de prioridade:
        1. Argumentos de inicialização
        2. Variáveis de ambiente
        3. Arquivo .env
        4. Vault (se disponível)
        5. Arquivos de segredos
        """
        sources = [
            init_settings,
            env_settings,
            dotenv_settings,
        ]
        
        # Adiciona VaultSettingsSource se disponível
        if VAULT_INTEGRATION_AVAILABLE and VaultSettingsSource:
            sources.append(VaultSettingsSource(settings_cls))
        
        sources.append(file_secret_settings)
        
        return tuple(sources)
    
    @property
    def vault_url(self) -> str:
        """Retorna a URL do Vault."""
        return self.VAULT_URL

    @property
    def vault_token(self) -> Optional[str]:
        """Retorna o token do Vault."""
        return self.VAULT_TOKEN

    @property
    def vault_enabled(self) -> bool:
        """Retorna se o Vault está habilitado."""
        return self.VAULT_ENABLED
    
    def get_vault_path(self, path_type: str = "service") -> str:
        """
        Gera o caminho do Vault para este serviço.
        
        Args:
            path_type: Tipo de caminho (service, database, jwt)
            
        Returns:
            str: Caminho do Vault
        """
        if path_type == "service":
            return get_service_vault_path(
                self.SERVICE_NAME,
                self.VAULT_ENVIRONMENT
            )
        elif path_type == "database":
            return get_database_vault_path(
                self.SERVICE_NAME,
                self.VAULT_ENVIRONMENT
            )
        elif path_type == "jwt":
            return get_jwt_vault_path(
                self.SERVICE_NAME,
                self.VAULT_ENVIRONMENT
            )
        else:
            raise ValueError(f"Tipo de caminho inválido: {path_type}")
    
    model_config = SettingsConfigDict(
        case_sensitive=True,
        extra="ignore"
    )


def get_vault_config() -> VaultConfig:
    """
    Retorna uma instância da configuração do Vault.
    
    Returns:
        VaultConfig: Configuração do Vault
    """
    return VaultConfig()


def get_service_vault_path(service_name: str, environment: str = "development") -> str:
    """
    Gera o caminho padrão do Vault para um serviço.
    
    Args:
        service_name: Nome do serviço (ex: "auth-service")
        environment: Ambiente (development, staging, production)
        
    Returns:
        str: Caminho do Vault (ex: "trix/development/auth-service")
    """
    return f"trix/{environment}/{service_name}"


def get_database_vault_path(service_name: str, environment: str = "development") -> str:
    """
    Gera o caminho padrão do Vault para credenciais de banco de dados.
    
    Args:
        service_name: Nome do serviço
        environment: Ambiente
        
    Returns:
        str: Caminho do Vault para DB
    """
    return f"database/{environment}/{service_name}"


def get_jwt_vault_path(service_name: str, environment: str = "development") -> str:
    """
    Gera o caminho padrão do Vault para chaves JWT.
    
    Args:
        service_name: Nome do serviço
        environment: Ambiente
        
    Returns:
        str: Caminho do Vault para JWT
    """
    return f"jwt/{environment}/{service_name}"


# Configurações específicas por ambiente
ENVIRONMENT_CONFIGS = {
    "development": {
        "VAULT_URL": "http://vault:8200",
        "VAULT_TOKEN": "dev-root-token",
        "VAULT_ENABLED": True,
        "VAULT_AUTH_METHOD": "token",
        "VAULT_CERTIFICATE_VERIFY": False
    },
    "staging": {
        "VAULT_URL": "https://vault-staging.trix.local:8200",
        "VAULT_TOKEN": None,
        "VAULT_ENABLED": True,
        "VAULT_AUTH_METHOD": "kubernetes",
        "VAULT_CERTIFICATE_VERIFY": True
    },
    "production": {
        "VAULT_URL": "https://vault.trix.local:8200",
        "VAULT_TOKEN": None,
        "VAULT_ENABLED": True,
        "VAULT_AUTH_METHOD": "kubernetes",
        "VAULT_CERTIFICATE_VERIFY": True
    }
}


def get_environment_config(environment: str = "development") -> Dict[str, Any]:
    """
    Retorna configurações específicas do ambiente.
    
    Args:
        environment: Nome do ambiente
        
    Returns:
        Dict: Configurações do ambiente
    """
    return ENVIRONMENT_CONFIGS.get(environment, ENVIRONMENT_CONFIGS["development"])
