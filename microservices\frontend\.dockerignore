# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# Build outputs
.next/
out/
dist/
build/

# Cache directories
.cache/
.parcel-cache/
.npm/
.eslintcache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Environment files
.env*
!.env.example

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory
coverage/
*.lcov
.nyc_output/

# TypeScript
*.tsbuildinfo
typings/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Test files
test/
tests/
__tests__/
*.test.*
*.spec.*

# Documentation
README.md
CHANGELOG.md
docs/

# Git
.git/
.gitignore

# Docker
Dockerfile*
.dockerignore
docker-compose*

# Development files
*.tmp
*.temp
.grunt/
.fusebox/
.dynamodb/
.tern-port
.vscode-test/
