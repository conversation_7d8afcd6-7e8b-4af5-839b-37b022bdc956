"""
Shared Logging Infrastructure for Trix Microservices
====================================================

Centralized structured logging implementation using structlog.
Provides consistent logging across all microservices with ELK stack integration.

Moved from individual microservices to reduce duplication.
"""

import logging
import sys
from typing import Dict, Any, Optional
from datetime import datetime
from functools import lru_cache

try:
    import structlog
    STRUCTLOG_AVAILABLE = True
except ImportError:
    STRUCTLOG_AVAILABLE = False
    structlog = None

from microservices.core.shared_lib.config.vault_config import VaultBaseSettings


class LoggingSettings(VaultBaseSettings):
    """Shared logging configuration."""
    
    LOG_LEVEL: str = "INFO"
    ENVIRONMENT: str = "development"
    SERVICE_NAME: str = "trix-service"
    SERVICE_VERSION: str = "1.0.0"
    
    # ELK Stack Integration
    ELK_ENABLED: bool = False
    ELASTICSEARCH_HOST: str = "elasticsearch"
    ELASTICSEARCH_PORT: int = 9200
    LOGSTASH_HOST: str = "logstash"
    LOGSTASH_PORT: int = 5000
    
    # Structured Logging
    JSON_LOGS: bool = True
    INCLUDE_TRACE: bool = False
    CORRELATION_ID_HEADER: str = "X-Correlation-ID"
    
    class Config:
        env_prefix = "LOG_"


class StructuredLogger:
    """
    Enterprise structured logger with ELK stack integration.
    Provides consistent logging across all microservices.
    """
    
    def __init__(self, service_name: str, service_version: str = "1.0.0"):
        self.service_name = service_name
        self.service_version = service_version
        self.settings = LoggingSettings()
        self._logger = None
        self._initialized = False
        
        if STRUCTLOG_AVAILABLE:
            self._setup_structlog()
        else:
            self._setup_standard_logging()
    
    def _setup_structlog(self):
        """Setup structured logging with structlog."""
        try:
            # Configure processors
            processors = [
                structlog.stdlib.filter_by_level,
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.stdlib.PositionalArgumentsFormatter(),
                structlog.processors.TimeStamper(fmt="iso"),
                self._add_service_context,
                structlog.processors.StackInfoRenderer(),
                structlog.processors.format_exc_info,
                structlog.processors.UnicodeDecoder(),
            ]
            
            # Add environment-specific processors
            if self.settings.ENVIRONMENT == "production" or self.settings.JSON_LOGS:
                processors.append(structlog.processors.JSONRenderer())
            else:
                processors.append(structlog.dev.ConsoleRenderer(colors=True))
            
            # Configure structlog
            structlog.configure(
                processors=processors,
                context_class=dict,
                logger_factory=structlog.stdlib.LoggerFactory(),
                wrapper_class=structlog.stdlib.BoundLogger,
                cache_logger_on_first_use=True,
            )
            
            # Configure standard logging
            logging.basicConfig(
                format="%(message)s",
                stream=sys.stdout,
                level=getattr(logging, self.settings.LOG_LEVEL.upper())
            )
            
            self._logger = structlog.get_logger(self.service_name)
            self._initialized = True
            
        except Exception as e:
            print(f"Failed to setup structlog: {e}")
            self._setup_standard_logging()
    
    def _setup_standard_logging(self):
        """Fallback to standard logging."""
        logging.basicConfig(
            level=getattr(logging, self.settings.LOG_LEVEL.upper()),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            stream=sys.stdout
        )
        self._logger = logging.getLogger(self.service_name)
        self._initialized = True
    
    def _add_service_context(self, logger, method_name: str, event_dict: Dict[str, Any]) -> Dict[str, Any]:
        """Add service context to all log entries."""
        event_dict.update({
            "service": self.service_name,
            "version": self.service_version,
            "environment": self.settings.ENVIRONMENT,
            "@timestamp": datetime.utcnow().isoformat(),
        })
        return event_dict
    
    def get_logger(self, name: str = None) -> Any:
        """Get logger instance."""
        if not self._initialized:
            return self._logger
        
        if STRUCTLOG_AVAILABLE and name:
            return structlog.get_logger(name)
        return self._logger
    
    def info(self, message: str, **kwargs):
        """Log info message."""
        if STRUCTLOG_AVAILABLE:
            self._logger.info(message, **kwargs)
        else:
            self._logger.info(f"{message} {kwargs}")
    
    def warning(self, message: str, **kwargs):
        """Log warning message."""
        if STRUCTLOG_AVAILABLE:
            self._logger.warning(message, **kwargs)
        else:
            self._logger.warning(f"{message} {kwargs}")
    
    def error(self, message: str, **kwargs):
        """Log error message."""
        if STRUCTLOG_AVAILABLE:
            self._logger.error(message, **kwargs)
        else:
            self._logger.error(f"{message} {kwargs}")
    
    def debug(self, message: str, **kwargs):
        """Log debug message."""
        if STRUCTLOG_AVAILABLE:
            self._logger.debug(message, **kwargs)
        else:
            self._logger.debug(f"{message} {kwargs}")


class LoggerMixin:
    """
    Mixin for adding structured logging to classes.
    Provides a logger configured with class context.
    """
    
    @property
    def logger(self):
        """Get logger with class context."""
        if not hasattr(self, '_logger'):
            service_name = getattr(self, 'service_name', 'trix-service')
            self._logger = get_logger(service_name).get_logger(self.__class__.__name__)
        return self._logger


# Global logger instances
_loggers: Dict[str, StructuredLogger] = {}


@lru_cache()
def get_logger(service_name: str, service_version: str = "1.0.0") -> StructuredLogger:
    """
    Get or create a structured logger instance for a service.
    
    Args:
        service_name: Name of the service
        service_version: Version of the service
        
    Returns:
        StructuredLogger: Configured logger instance
    """
    cache_key = f"{service_name}:{service_version}"
    if cache_key not in _loggers:
        _loggers[cache_key] = StructuredLogger(service_name, service_version)
    return _loggers[cache_key]


def setup_logging(service_name: str, service_version: str = "1.0.0") -> StructuredLogger:
    """
    Setup logging for a microservice.
    
    Args:
        service_name: Name of the service
        service_version: Version of the service
        
    Returns:
        StructuredLogger: Configured logger instance
    """
    logger = get_logger(service_name, service_version)
    logger.info(
        "🔧 Logging system configured",
        service=service_name,
        version=service_version,
        environment=logger.settings.ENVIRONMENT,
        log_level=logger.settings.LOG_LEVEL,
        structured_json=logger.settings.JSON_LOGS
    )
    return logger


def log_performance_metric(
    service_name: str,
    operation: str,
    duration_ms: float,
    **kwargs
):
    """Log performance metric with structured data."""
    logger = get_logger(service_name)
    logger.info(
        f"Performance metric: {operation}",
        operation=operation,
        duration_ms=duration_ms,
        event_type="performance.metric",
        **kwargs
    )


def log_business_event(
    service_name: str,
    event_type: str,
    entity_id: str,
    **kwargs
):
    """Log business event with structured data."""
    logger = get_logger(service_name)
    logger.info(
        f"Business event: {event_type}",
        event_type=event_type,
        entity_id=entity_id,
        category="business.event",
        **kwargs
    )
