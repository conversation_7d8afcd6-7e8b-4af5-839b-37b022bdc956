#!/bin/bash
set -e

echo "Creating pgbouncer config in /etc/pgbouncer"

# Create pgbouncer.ini with environment variables
cat > /etc/pgbouncer/pgbouncer.ini << EOF
#pgbouncer.ini

[databases]
* = host = ${DATABASES_HOST:-citus-coordinator} port=${DATABASES_PORT:-5432} user=${DATABASES_USER:-postgres} password=${DATABASES_PASSWORD:-postgres} dbname=${DATABASES_DBNAME:-trix_db}

[pgbouncer]
listen_addr = 0.0.0.0
listen_port = 6432
auth_type = any
ignore_startup_parameters = extra_float_digits
logfile = /var/log/pgbouncer/pgbouncer.log
pidfile = /var/log/pgbouncer/pgbouncer.pid

# Pool settings
pool_mode = ${POOL_MODE:-transaction}
max_client_conn = ${MAX_CLIENT_CONN:-200}
default_pool_size = ${DEFAULT_POOL_SIZE:-25}
min_pool_size = ${MIN_POOL_SIZE:-5}
reserve_pool_size = ${RESERVE_POOL_SIZE:-5}

# Log settings
admin_users = ${DATABASES_USER:-postgres}
log_connections = 1
log_disconnections = 1

# Connection sanity checks, timeouts
server_reset_query = ${SERVER_RESET_QUERY:-DISCARD ALL}
server_check_delay = 30
server_check_query = select 1
server_lifetime = 3600
server_idle_timeout = 600

EOF

# Create userlist.txt
cat > /etc/pgbouncer/userlist.txt << EOF
"${DATABASES_USER:-postgres}" "${DATABASES_PASSWORD:-postgres}"
EOF

echo "Starting pgbouncer with config:"
echo "- Host: ${DATABASES_HOST:-citus-coordinator}"
echo "- Port: ${DATABASES_PORT:-5432}"
echo "- User: ${DATABASES_USER:-postgres}"
echo "- Database: ${DATABASES_DBNAME:-trix_db}"

exec pgbouncer /etc/pgbouncer/pgbouncer.ini
