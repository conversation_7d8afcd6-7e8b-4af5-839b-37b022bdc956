#!/usr/bin/env python3
"""
🚀 Deploy Completo do Citus Distribuído - Trix
Orquestra todo o processo de configuração, migration e seed do cluster Citus
"""

import asyncio
import subprocess
import logging
import time
import os
from pathlib import Path
from typing import List, Dict, Any

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CitusCompleteDeployer:
    """Orquestrador completo do deploy Citus"""
    
    def __init__(self):
        self.deployment_steps = [
            "check_prerequisites",
            "setup_infrastructure", 
            "configure_cluster",
            "run_migrations",
            "run_seeds",
            "update_microservices",
            "verify_deployment",
            "generate_report"
        ]
        self.completed_steps = []
        self.failed_steps = []
        
    async def deploy_complete_citus(self):
        """Executa deploy completo do Citus"""
        logger.info("🚀 Iniciando deploy completo do Citus Distribuído - Trix")
        logger.info("=" * 70)
        
        start_time = time.time()
        
        try:
            for step in self.deployment_steps:
                logger.info(f"📋 Executando: {step}")
                
                if await self._execute_step(step):
                    self.completed_steps.append(step)
                    logger.info(f"✅ {step} concluído com sucesso")
                else:
                    self.failed_steps.append(step)
                    logger.error(f"❌ {step} falhou")
                    break
                
                logger.info("-" * 50)
            
            # Relatório final
            end_time = time.time()
            duration = end_time - start_time
            
            await self._generate_final_report(duration)
            
        except Exception as e:
            logger.error(f"❌ Erro crítico no deploy: {e}")
            raise
    
    async def _execute_step(self, step: str) -> bool:
        """Executa um passo específico do deploy"""
        try:
            if step == "check_prerequisites":
                return await self._check_prerequisites()
            elif step == "setup_infrastructure":
                return await self._setup_infrastructure()
            elif step == "configure_cluster":
                return await self._configure_cluster()
            elif step == "run_migrations":
                return await self._run_migrations()
            elif step == "run_seeds":
                return await self._run_seeds()
            elif step == "update_microservices":
                return await self._update_microservices()
            elif step == "verify_deployment":
                return await self._verify_deployment()
            elif step == "generate_report":
                return await self._generate_report()
            else:
                logger.error(f"Passo desconhecido: {step}")
                return False
                
        except Exception as e:
            logger.error(f"Erro no passo {step}: {e}")
            return False
    
    async def _check_prerequisites(self) -> bool:
        """Verifica pré-requisitos"""
        logger.info("🔍 Verificando pré-requisitos...")
        
        # Verificar Docker
        try:
            result = subprocess.run(['docker', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                logger.info(f"✅ Docker: {result.stdout.strip()}")
            else:
                logger.error("❌ Docker não encontrado")
                return False
        except FileNotFoundError:
            logger.error("❌ Docker não instalado")
            return False
        
        # Verificar Docker Compose
        try:
            result = subprocess.run(['docker-compose', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                logger.info(f"✅ Docker Compose: {result.stdout.strip()}")
            else:
                logger.error("❌ Docker Compose não encontrado")
                return False
        except FileNotFoundError:
            logger.error("❌ Docker Compose não instalado")
            return False
        
        # Verificar Python
        try:
            result = subprocess.run(['python3', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                logger.info(f"✅ Python: {result.stdout.strip()}")
            else:
                logger.error("❌ Python3 não encontrado")
                return False
        except FileNotFoundError:
            logger.error("❌ Python3 não instalado")
            return False
        
        # Verificar dependências Python
        try:
            import asyncpg
            logger.info("✅ asyncpg disponível")
        except ImportError:
            logger.error("❌ asyncpg não instalado. Execute: pip install asyncpg")
            return False
        
        # Verificar arquivos necessários
        required_files = [
            'docker-compose.citus.yml',
            'setup-citus-cluster.py',
            'citus-migration-manager.py',
            'citus-seed-manager.py',
            'update-microservices-citus.py'
        ]
        
        for file in required_files:
            if not Path(file).exists():
                logger.error(f"❌ Arquivo necessário não encontrado: {file}")
                return False
            else:
                logger.info(f"✅ {file} encontrado")
        
        return True
    
    async def _setup_infrastructure(self) -> bool:
        """Configura infraestrutura"""
        logger.info("🏗️ Configurando infraestrutura...")
        
        try:
            # Executar script de setup
            if Path('setup-citus-complete.sh').exists():
                result = subprocess.run(['bash', 'setup-citus-complete.sh'], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    logger.info("✅ Script de setup executado com sucesso")
                    return True
                else:
                    logger.error(f"❌ Erro no script de setup: {result.stderr}")
                    return False
            else:
                logger.warning("⚠️ Script setup-citus-complete.sh não encontrado, executando manualmente...")
                
                # Criar rede se não existir
                subprocess.run(['docker', 'network', 'create', 'trix-network'], 
                             capture_output=True)
                
                # Iniciar cluster
                result = subprocess.run(['docker-compose', '-f', 'docker-compose.citus.yml', 'up', '-d'],
                                      capture_output=True, text=True)
                
                if result.returncode == 0:
                    logger.info("✅ Cluster Citus iniciado")
                    # Aguardar inicialização
                    await asyncio.sleep(30)
                    return True
                else:
                    logger.error(f"❌ Erro ao iniciar cluster: {result.stderr}")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ Erro na configuração da infraestrutura: {e}")
            return False
    
    async def _configure_cluster(self) -> bool:
        """Configura cluster Citus"""
        logger.info("⚙️ Configurando cluster Citus...")
        
        try:
            # Importar e executar configurador
            from setup_citus_cluster import CitusClusterManager
            
            manager = CitusClusterManager()
            await manager.setup_cluster()
            
            logger.info("✅ Cluster Citus configurado")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro na configuração do cluster: {e}")
            return False
    
    async def _run_migrations(self) -> bool:
        """Executa migrations"""
        logger.info("🔄 Executando migrations distribuídas...")
        
        try:
            # Importar e executar migration manager
            from citus_migration_manager import CitusMigrationManager
            
            manager = CitusMigrationManager()
            await manager.run_distributed_migrations()
            
            logger.info("✅ Migrations executadas")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro nas migrations: {e}")
            return False
    
    async def _run_seeds(self) -> bool:
        """Executa seeds"""
        logger.info("🌱 Executando seeds distribuídos...")
        
        try:
            # Importar e executar seed manager
            from citus_seed_manager import CitusSeedManager
            
            manager = CitusSeedManager()
            await manager.run_distributed_seeds()
            
            logger.info("✅ Seeds executados")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro nos seeds: {e}")
            return False
    
    async def _update_microservices(self) -> bool:
        """Atualiza microserviços"""
        logger.info("🔄 Atualizando microserviços para Citus...")
        
        try:
            # Importar e executar atualizador
            from update_microservices_citus import MicroservicesCitusUpdater
            
            updater = MicroservicesCitusUpdater()
            updater.update_all_microservices()
            
            logger.info("✅ Microserviços atualizados")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro na atualização dos microserviços: {e}")
            return False
    
    async def _verify_deployment(self) -> bool:
        """Verifica deployment"""
        logger.info("🔍 Verificando deployment...")
        
        try:
            # Verificar containers
            result = subprocess.run(['docker-compose', '-f', 'docker-compose.citus.yml', 'ps'],
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("✅ Containers verificados")
                logger.info(result.stdout)
            else:
                logger.error("❌ Erro na verificação dos containers")
                return False
            
            # Verificar conectividade básica
            containers = [
                'trix-citus-core-coordinator',
                'trix-citus-tenant-coordinator', 
                'trix-citus-shared-coordinator',
                'trix-citus-worker-1',
                'trix-citus-worker-2'
            ]
            
            for container in containers:
                result = subprocess.run(['docker', 'exec', container, 'pg_isready', '-U', 'postgres'],
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    logger.info(f"✅ {container}: OK")
                else:
                    logger.error(f"❌ {container}: FAIL")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro na verificação: {e}")
            return False
    
    async def _generate_report(self) -> bool:
        """Gera relatório"""
        logger.info("📊 Gerando relatório...")
        
        try:
            # Informações de acesso
            access_info = {
                'pgadmin': 'http://localhost:8082',
                'core_coordinator': 'localhost:5432',
                'tenant_coordinator': 'localhost:5433', 
                'shared_coordinator': 'localhost:5434',
                'worker_1': 'localhost:5435',
                'worker_2': 'localhost:5436'
            }
            
            logger.info("🌐 Pontos de Acesso:")
            for service, url in access_info.items():
                logger.info(f"   {service}: {url}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro na geração do relatório: {e}")
            return False
    
    async def _generate_final_report(self, duration: float):
        """Gera relatório final"""
        logger.info("=" * 70)
        logger.info("📊 RELATÓRIO FINAL DO DEPLOY CITUS")
        logger.info("=" * 70)
        
        total_steps = len(self.deployment_steps)
        completed_count = len(self.completed_steps)
        failed_count = len(self.failed_steps)
        success_rate = (completed_count / total_steps) * 100
        
        logger.info(f"🎯 Total de passos: {total_steps}")
        logger.info(f"✅ Passos concluídos: {completed_count}")
        logger.info(f"❌ Passos falharam: {failed_count}")
        logger.info(f"📈 Taxa de sucesso: {success_rate:.1f}%")
        logger.info(f"⏱️ Tempo total: {duration:.1f}s")
        
        if self.completed_steps:
            logger.info("\n✅ Passos concluídos:")
            for step in self.completed_steps:
                logger.info(f"   - {step}")
        
        if self.failed_steps:
            logger.info("\n❌ Passos que falharam:")
            for step in self.failed_steps:
                logger.info(f"   - {step}")
        
        if success_rate == 100:
            logger.info("\n🎉 DEPLOY CONCLUÍDO COM SUCESSO!")
            logger.info("🌐 Cluster Citus está pronto para uso")
        else:
            logger.info("\n⚠️ Deploy parcialmente concluído")
            logger.info("🔧 Verifique os passos que falharam")
        
        logger.info("=" * 70)

if __name__ == "__main__":
    async def main():
        deployer = CitusCompleteDeployer()
        await deployer.deploy_complete_citus()
    
    asyncio.run(main())
