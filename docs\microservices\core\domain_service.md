# 🌐 Domain Service - Documentação Técnica Completa

> **🔗 Integração com Shared Lib**: Este serviço utiliza configurações e componentes centralizados da `microservices/core/shared_lib` para garantir consistência e reduzir duplicação entre microserviços. **MIGRAÇÃO PLANEJADA** - Configurações comuns serão movidas para a shared_lib. Veja a [seção de integração](#-integração-com-shared-lib) para detalhes.

## 📋 Visão Geral

O **Domain Service** é um microserviço fundamental da plataforma Trix responsável pelo gerenciamento completo de domínios personalizados, registro de domínios, DNS, SSL/TLS e proxy reverso. Implementa funcionalidades enterprise-grade com integração completa à arquitetura orientada a eventos e suporte a escalabilidade massiva para bilhões de usuários simultâneos.

### 🎯 **Informações Básicas**
- **Porta:** 8015
- **Database:** PostgreSQL + Citus Data (Sharded)
- **Messaging:** Apache Kafka + RabbitMQ + Redis Streams
- **Service Mesh:** Istio/Linkerd com mTLS
- **Secrets:** HashiCorp Vault
- **Monitoring:** Prometheus + Grafana + Jaeger + ELK
- **Orchestration:** Kubernetes + Helm + ArgoCD
- **Status:** 🔄 **MIGRAÇÃO PARA SHARED LIB EM ANDAMENTO**
- **Versão:** 2.0.0 (Enterprise-Grade)
- **Target Scale:** Bilhões de usuários simultâneos
- **Shared Lib Integration:** 🔄 **PLANEJADO** (Configurações comuns serão migradas)

### 📊 **Status de Implementação Enterprise**
- 🔄 **Database Sharding**: Citus Data para distribuição horizontal (usando shared_lib)
- 🔄 **Service Mesh**: Istio/Linkerd com mTLS automático (usando shared_lib)
- 🔄 **Event-Driven**: Kafka/RabbitMQ para comunicação assíncrona (usando shared_lib)
- 🔄 **Security**: Vault para secrets, OPA para policies (usando shared_lib)
- 🔄 **Observability**: Prometheus/Jaeger/ELK stack completo (usando shared_lib)
- 🔄 **Kubernetes**: Helm charts e manifests para orquestração (usando shared_lib)
- 🔄 **APIs Versionadas**: v1 com cache, rate limiting e bulk operations
- 🔄 **Performance**: Connection pooling, read replicas, caching (usando shared_lib)
- 🔄 **Geo-Distribution**: Multi-region deployment strategy (usando shared_lib)
- 🔄 **Auto-Scaling**: HPA baseado em CPU/Memory/Custom metrics (usando shared_lib)

## 🏗️ **Arquitetura Enterprise (v2.0 - Integração com Shared Lib)**

> **📚 Shared Components**: Este serviço utiliza componentes compartilhados da `microservices/core/shared_lib/` para configurações, utilitários e integrações comuns. **MIGRAÇÃO PLANEJADA** - Componentes comuns serão movidos para a shared_lib.

### ✅ **Technology Stack (100% Open Source - Usando Shared Lib)**
- **Orchestration:** Kubernetes + Helm + ArgoCD (🔗 shared_lib/infrastructure/kubernetes)
- **Service Mesh:** Istio/Linkerd com mTLS automático (🔗 shared_lib/infrastructure/kubernetes/istio_configs)
- **Databases:** PostgreSQL + Citus Data + PgBouncer (🔗 shared_lib/infrastructure/database)
- **Messaging:** Apache Kafka + RabbitMQ + Redis Streams (🔗 shared_lib/infrastructure/messaging)
- **CDN:** Varnish + MinIO + PowerDNS (específico do Domain Service)
- **Monitoring:** Prometheus + Grafana + Jaeger + ELK (🔗 shared_lib/infrastructure/observability)
- **Security:** HashiCorp Vault + OPA Gatekeeper + Falco (🔗 shared_lib/infrastructure/security)

### 🎯 **Event-Driven Architecture (Usando Shared Lib)**
- **Apache Kafka**: Eventos críticos e sincronização de dados (🔗 shared_lib/infrastructure/messaging/kafka_client.py)
- **RabbitMQ**: Mensagens rápidas e notificações para velocidade e escalabilidade (🔗 shared_lib/infrastructure/messaging/rabbitmq_client.py)
- **Padrão CQRS**: Separação de comandos e consultas (🔗 shared_lib/utils/event_sourcing.py)
- **Event Sourcing**: Histórico completo de mudanças de domínio (🔗 shared_lib/utils/event_sourcing.py)
- **Saga Pattern**: Transações distribuídas entre microserviços (🔗 shared_lib/utils/event_sourcing.py)
- **Soft References**: Relacionamentos entre microserviços via eventos, não FK constraints
- **DNS Automation**: Eventos de configuração DNS via Kafka/RabbitMQ (específico do Domain Service)

### 📁 **Estrutura de Diretórios (Integração com Shared Lib)**

> **📚 Shared Components**: Este serviço utiliza componentes compartilhados da `microservices/core/shared_lib/` para configurações, utilitários e integrações comuns. **MIGRAÇÃO PLANEJADA** - Componentes comuns serão movidos para a shared_lib.

```
microservices/core/domain_service/
├── app/
│   ├── api/
│   │   └── v1/                       # ✅ Versioned APIs
│   │       ├── custom_domains.py     # Custom domain endpoints
│   │       ├── domain_registration.py # Domain registration endpoints
│   │       ├── dns_management.py     # DNS management endpoints
│   │       ├── ssl_certificates.py   # SSL certificate endpoints
│   │       ├── health.py             # Health check endpoints
│   │       └── __init__.py           # API router exports
│   ├── core/
│   │   ├── config/                   # ✅ Domain-specific configs only
│   │   │   ├── settings.py           # Domain-specific settings (extends shared_lib)
│   │   │   ├── database.py           # Domain-specific database config
│   │   │   ├── dns_config.py         # PowerDNS configuration
│   │   │   ├── ssl_config.py         # SSL/TLS configuration
│   │   │   └── registrar_config.py   # Domain registrar configuration
│   │   ├── security/                 # ✅ Domain-specific security
│   │   │   ├── domain_validation.py  # Domain ownership validation
│   │   │   ├── ssl_validation.py     # SSL certificate validation
│   │   │   └── dns_validation.py     # DNS record validation
│   │   ├── database/                 # ✅ Domain-specific database layer
│   │   │   ├── domain_sharding.py    # Domain-specific sharding logic
│   │   │   ├── connection.py         # Domain database connections
│   │   │   └── migrations.py         # Domain migration management
│   │   ├── observability/            # ✅ Domain-specific observability only
│   │   │   ├── logging.py            # Domain-specific structured logging
│   │   │   ├── tracing.py            # Domain-specific distributed tracing
│   │   │   └── __init__.py           # Re-exports metrics from shared_lib
│   │   ├── integrations/             # ✅ External service integrations
│   │   │   ├── powerdns_client.py    # PowerDNS integration
│   │   │   ├── letsencrypt_client.py # Let's Encrypt integration
│   │   │   ├── registrar_clients.py  # Domain registrar integrations
│   │   │   └── cdn_client.py         # CDN integration
│   │   ├── auth.py                   # ✅ Auth middleware integration (uses shared_lib)
│   │   └── db_dependencies.py        # ✅ Database dependencies (uses shared_lib)
│   ├── models/
│   │   ├── custom_domain.py          # ✅ Custom domain model (sharded by tenant_id)
│   │   ├── domain_registration.py    # ✅ Domain registration model
│   │   ├── dns_record.py             # ✅ DNS records model
│   │   ├── ssl_certificate.py        # ✅ SSL certificate model
│   │   └── __init__.py               # Model exports
│   ├── schemas/
│   │   ├── custom_domain.py          # ✅ Custom domain schemas
│   │   ├── domain_registration.py    # ✅ Domain registration schemas
│   │   ├── dns_management.py         # ✅ DNS management schemas
│   │   ├── ssl_certificate.py        # ✅ SSL certificate schemas
│   │   ├── events.py                 # ✅ Event schemas for messaging
│   │   └── __init__.py               # Schema exports
│   ├── services/
│   │   ├── domain_service.py         # ✅ Domain management service
│   │   ├── dns_service.py            # ✅ DNS management service
│   │   ├── ssl_service.py            # ✅ SSL certificate service
│   │   ├── registrar_service.py      # ✅ Domain registrar service
│   │   ├── event_service.py          # ✅ Event publishing service
│   │   └── __init__.py               # Service exports

│   ├── custom_domains/                # ✅ SUBMÓDULO: Custom Domains
│   │   ├── api/
│   │   │   └── custom_domains_api.py  # ✅ API de domínios personalizados
│   │   ├── models/
│   │   │   ├── custom_domain.py       # ✅ Custom domain models
│   │   │   └── __init__.py            # Custom domain model exports
│   │   ├── schemas/
│   │   │   ├── custom_domain_schemas.py # ✅ Custom domain schemas
│   │   │   └── __init__.py            # Custom domain schema exports
│   │   ├── services/
│   │   │   ├── custom_domain_service.py # ✅ Custom domain service
│   │   │   └── __init__.py            # Custom domain service exports
│   │   └── __init__.py                # Custom domain module exports
│   ├── domain_registration/           # ✅ SUBMÓDULO: Domain Registration
│   │   ├── api/
│   │   │   └── registration_api.py    # ✅ API de registro de domínios
│   │   ├── models/
│   │   │   ├── domain_registration.py # ✅ Registration models
│   │   │   ├── domain_contact.py      # ✅ Contact models
│   │   │   ├── registrar_config.py    # ✅ Registrar configuration
│   │   │   └── __init__.py            # Registration model exports
│   │   ├── schemas/
│   │   │   ├── registration_schemas.py # ✅ Registration schemas
│   │   │   └── __init__.py            # Registration schema exports
│   │   ├── services/
│   │   │   ├── registration_service.py # ✅ Registration service
│   │   │   ├── registrar_service.py   # ✅ Registrar integration service
│   │   │   └── __init__.py            # Registration service exports
│   │   ├── processors/                # ✅ Registrar processors
│   │   │   ├── base_processor.py      # Base processor
│   │   │   ├── godaddy_processor.py   # GoDaddy integration
│   │   │   ├── namecheap_processor.py # Namecheap integration
│   │   │   └── __init__.py            # Processor exports
│   │   └── __init__.py                # Registration module exports
│   ├── dns_management/                # ✅ SUBMÓDULO: DNS Management
│   │   ├── api/
│   │   │   └── dns_api.py             # ✅ API de gerenciamento DNS
│   │   ├── models/
│   │   │   ├── dns_record.py          # ✅ DNS record models
│   │   │   ├── dns_zone.py            # ✅ DNS zone models
│   │   │   └── __init__.py            # DNS model exports
│   │   ├── schemas/
│   │   │   ├── dns_schemas.py         # ✅ DNS schemas
│   │   │   └── __init__.py            # DNS schema exports
│   │   ├── services/
│   │   │   ├── dns_service.py         # ✅ DNS management service
│   │   │   ├── powerdns_service.py    # ✅ PowerDNS integration
│   │   │   └── __init__.py            # DNS service exports
│   │   └── __init__.py                # DNS module exports
│   ├── ssl_management/                # ✅ SUBMÓDULO: SSL Management
│   │   ├── api/
│   │   │   └── ssl_api.py             # ✅ API de certificados SSL
│   │   ├── models/
│   │   │   ├── ssl_certificate.py     # ✅ SSL certificate models
│   │   │   └── __init__.py            # SSL model exports
│   │   ├── schemas/
│   │   │   ├── ssl_schemas.py         # ✅ SSL schemas
│   │   │   └── __init__.py            # SSL schema exports
│   │   ├── services/
│   │   │   ├── ssl_service.py         # ✅ SSL management service
│   │   │   ├── letsencrypt_service.py # ✅ Let's Encrypt integration
│   │   │   └── __init__.py            # SSL service exports
│   │   └── __init__.py                # SSL module exports
│   ├── events/                        # ✅ Event-driven architecture
│   │   ├── handlers/                  # ✅ Event processing logic
│   │   │   ├── domain_events.py       # Domain event handlers
│   │   │   ├── dns_events.py          # DNS event handlers
│   │   │   └── ssl_events.py          # SSL event handlers
│   │   ├── publishers/                # ✅ Event publishing utilities
│   │   │   ├── kafka_publisher.py     # Kafka event publisher
│   │   │   ├── rabbitmq_publisher.py  # RabbitMQ publisher
│   │   │   └── redis_publisher.py     # Redis Streams publisher
│   │   ├── schemas/                   # ✅ Event data schemas
│   │   │   ├── domain_events.py       # Domain event schemas
│   │   │   ├── dns_events.py          # DNS event schemas
│   │   │   └── ssl_events.py          # SSL event schemas
│   │   └── __init__.py                # Event module exports
│   └── main.py                        # ✅ FastAPI application with Istio integration
├── k8s/                              # ✅ Kubernetes manifests
│   ├── base/                         # Base Kubernetes resources
│   │   ├── deployment.yaml           # Deployment configuration
│   │   ├── service.yaml              # Service definition
│   │   ├── configmap.yaml            # Configuration management
│   │   └── secret.yaml               # Vault secret references
│   ├── overlays/                     # Environment-specific overlays
│   │   ├── development/              # Dev environment
│   │   ├── staging/                  # Staging environment
│   │   └── production/               # Production environment
│   └── istio/                        # ✅ Service mesh configurations
│       ├── virtual-service.yaml      # Traffic routing rules
│       ├── destination-rule.yaml     # Load balancing policies
│       └── peer-authentication.yaml  # mTLS configuration
├── helm/                             # ✅ Helm charts for deployment
│   ├── Chart.yaml                    # Chart metadata
│   ├── values.yaml                   # Default values
│   ├── values-prod.yaml              # Production values
│   └── templates/                    # Kubernetes templates
├── monitoring/                       # ✅ Observability configurations
│   ├── prometheus/                   # Prometheus rules and alerts
│   ├── grafana/                      # Grafana dashboards
│   └── jaeger/                       # Jaeger tracing configuration
├── security/                         # ✅ Security configurations
│   ├── vault/                        # HashiCorp Vault policies
│   ├── opa/                          # Open Policy Agent rules
│   └── falco/                        # Runtime security rules
├── migrations/                       # ✅ Distributed database migrations
│   ├── citus/                        # Citus Data specific migrations
│   └── alembic/                      # Standard Alembic migrations
├── tests/                            # ✅ Comprehensive test suite
│   ├── unit/                         # Unit tests
│   ├── integration/                  # Integration tests
│   ├── load/                         # Load testing with K6
│   └── security/                     # Security testing
├── docker/                           # ✅ Container configurations
│   ├── Dockerfile                    # Multi-stage production build
│   ├── Dockerfile.dev                # Development build
│   └── docker-compose.yml            # Local development stack
├── scripts/                          # ✅ Automation scripts
│   ├── deploy.sh                     # Deployment automation
│   ├── migrate.sh                    # Database migration runner
│   └── scale.sh                      # Auto-scaling utilities
├── docs/                             # ✅ Documentation
│   ├── api/                          # API documentation
│   ├── deployment/                   # Deployment guides
│   └── architecture/                 # Architecture decisions
├── requirements/                     # ✅ Dependency management
│   ├── base.txt                      # Base dependencies
│   ├── production.txt                # Production dependencies
│   └── development.txt               # Development dependencies
└── pyproject.toml                    # ✅ Modern Python project configuration

# Shared Library Integration (MIGRAÇÃO PLANEJADA - PASTAS A SEREM REMOVIDAS)
../shared_lib/infrastructure/                # 🔗 Infraestrutura compartilhada
├── config/                        # 🔗 Configurações compartilhadas
│   ├── database.py                # 🔗 SERÁ USADO: Configuração Citus Data compartilhada
│   ├── vault.py                   # 🔗 SERÁ USADO: Configuração Vault compartilhada
│   ├── kafka_config.py            # 🔗 SERÁ USADO: Configuração Kafka compartilhada
│   └── redis_config.py            # 🔗 SERÁ USADO: Configuração Redis compartilhada
├── messaging/                     # 🔗 Clientes de messaging compartilhados
│   ├── kafka_client.py            # 🔗 SERÁ USADO: Cliente Kafka compartilhado
│   ├── rabbitmq_client.py         # 🔗 SERÁ USADO: Cliente RabbitMQ compartilhado
│   ├── redis_client.py            # 🔗 SERÁ USADO: Cliente Redis compartilhado
│   └── __init__.py                # 🔗 Exportações dos clientes
├── observability/                 # 🔗 Utilitários de monitoramento
│   ├── metrics.py                 # 🔗 SERÁ USADO: Métricas Prometheus compartilhadas
│   ├── tracing.py                 # 🔗 SERÁ USADO: Tracing distribuído compartilhado
│   ├── logging.py                 # 🔗 SERÁ USADO: Logging estruturado compartilhado
│   └── __init__.py                # 🔗 Exportações de observabilidade
├── security/                      # 🔗 Utilitários de segurança
│   ├── jwt.py                     # 🔗 SERÁ USADO: JWT handling compartilhado
│   ├── permissions.py             # 🔗 SERÁ USADO: OPA policy integration
│   ├── encryption.py              # 🔗 SERÁ USADO: Utilitários de criptografia
│   ├── rate_limiter.py            # 🔗 SERÁ USADO: Rate limiting compartilhado
│   └── __init__.py                # 🔗 Exportações de segurança
├── database/                      # 🔗 Utilitários de banco de dados
│   ├── connection.py              # 🔗 SERÁ USADO: Conexões de banco compartilhadas
│   ├── sharding.py                # 🔗 SERÁ USADO: Sharding compartilhado
│   └── __init__.py                # 🔗 Exportações de banco
├── kubernetes/                    # 🔗 Configurações Kubernetes compartilhadas
│   ├── base_manifests/            # 🔗 SERÁ USADO: Manifests base
│   ├── helm_templates/            # 🔗 SERÁ USADO: Templates Helm compartilhados
│   ├── istio_configs/             # 🔗 SERÁ USADO: Configurações Istio
│   └── monitoring/                # 🔗 SERÁ USADO: Configurações de monitoramento
└── __init__.py                    # 🔗 Exportações da infraestrutura
```

### 🔗 **Integração com Shared Lib (MIGRAÇÃO PLANEJADA)**

O Domain Service será migrado para utilizar completamente a `shared_lib` para:

> **🎯 MIGRAÇÃO PLANEJADA**: Todas as funcionalidades comuns serão movidas para a shared_lib e o Domain Service será limpo e otimizado. Funcionalidades específicas de domínio serão mantidas e organizadas.

#### **📋 Configurações Compartilhadas (SERÁ MIGRADO)**
- **Vault Configuration**: ✅ Configuração centralizada do HashiCorp Vault
- **Kafka Configuration**: ✅ Configuração dos brokers e tópicos Kafka
- **Database Configuration**: ✅ Configuração Citus Data compartilhada
- **Redis Configuration**: ✅ Configuração Redis Cluster compartilhada

#### **🛠️ Utilitários Compartilhados (SERÁ MIGRADO)**
- **Messaging Clients**: ✅ Clientes Kafka, RabbitMQ e Redis padronizados
- **Observability Tools**: ✅ Métricas Prometheus, logs e tracing padronizados
- **Security Utilities**: ✅ Funções de criptografia e validação
- **Database Utilities**: ✅ Conexões e migrações padronizadas

#### **🧹 Limpeza Planejada (SERÁ REALIZADA)**
- **Messaging Infrastructure**: ❌ Será removida (movida para shared_lib)
- **Metrics Duplicadas**: ❌ Serão removidas (centralizadas na shared_lib)
- **Security Duplicada**: ❌ Será removida (centralizada na shared_lib)
- **Database Layer Comum**: ❌ Será removida (centralizada na shared_lib)

#### **📦 Como Usar (APÓS MIGRAÇÃO)**
```python
# Importar configurações compartilhadas
from microservices.core.shared_lib.infrastructure.config import (
    VaultBaseSettings,
    KafkaSettings,
    CitusDataConfig,
    RedisClusterConfig
)

# Importar clientes de messaging compartilhados
from microservices.core.shared_lib.infrastructure.messaging import (
    KafkaClient,
    RabbitMQClient,
    RedisClient
)

# Importar utilitários de observabilidade compartilhados
from microservices.core.shared_lib.infrastructure.observability import (
    MetricsCollector,
    get_metrics_collector,
    SharedTracing,
    SharedLogging
)

# Importar métricas específicas do domain_service (re-exportadas da shared_lib)
from app.core.observability import (
    domain_requests_counter,
    ssl_issuance_counter,
    dns_propagation_histogram,
    metrics_manager
)

# Exemplo de uso (APÓS MIGRAÇÃO)
kafka_client = KafkaClient("domain-service")
rabbitmq_client = RabbitMQClient("domain-service")
redis_client = RedisClient("domain-service")
metrics = get_metrics_collector("domain-service", "2.0.0", "production")

# Usar métricas específicas do domain
domain_requests_counter.inc({"method": "create_domain", "status": "success"})
ssl_issuance_counter.inc({"ca": "letsencrypt", "status": "success"})
```

### 🎯 **Melhorias na Estrutura Organizacional**

#### **✅ Submódulos Preservados (Específicos do Domain Service)**
- **`custom_domains/`**: Domínios personalizados como submódulo organizado
- **`domain_registration/`**: Registro de domínios como submódulo organizado
- **`dns_management/`**: Gerenciamento DNS como submódulo organizado
- **`ssl_management/`**: Gerenciamento SSL como submódulo organizado
- **Vantagem**: Cada submódulo tem sua própria estrutura (api/, models/, schemas/, services/)

#### **🔗 Core Enterprise Compartilhado (Usando Shared Lib)**
- **`core/config/`**: Configurações específicas do Domain Service (🔗 extends shared_lib/infrastructure/config)
- **`core/security/`**: Validações específicas de domínio (🔗 uses shared_lib/infrastructure/security)
- **`core/messaging/`**: Event-driven messaging específico (🔗 uses shared_lib/infrastructure/messaging)
- **`core/observability/`**: Métricas específicas de domínio (🔗 uses shared_lib/infrastructure/observability)

#### **✅ Event-Driven Organizado (Específico do Domain Service)**
- **`events/handlers/`**: Processamento de eventos de domínio, DNS e SSL
- **`events/publishers/`**: Publishers específicos para eventos de domínio
- **`events/schemas/`**: Schemas de eventos organizados por contexto de domínio

#### **✅ APIs Versionadas (Específicas do Domain Service)**
- **`api/v1/`**: APIs versionadas para backward compatibility
- **Estrutura**: Endpoints organizados por funcionalidade de domínio

#### **🎯 Benefícios da Integração com Shared Lib**

**✅ Consistência Entre Microserviços**:
- Configurações padronizadas em todos os serviços
- Mesmos padrões de segurança, observability e messaging
- Redução de duplicação de código

**✅ Manutenibilidade**:
- Atualizações centralizadas na shared_lib
- Versionamento controlado de componentes comuns
- Facilita upgrades e patches de segurança

**✅ Reutilização**:
- Novos microserviços podem importar componentes prontos
- Padrões enterprise já implementados e testados
- Reduz tempo de desenvolvimento de novos serviços

### 🔧 **Modelos de Dados Enterprise (Citus Data Optimized)**

#### **1. Sharded Tables (Distributed by tenant_id)**
```python
class CustomDomain(Base):
    """
    Custom domain model optimized for Citus Data sharding.
    Distributed by tenant_id for optimal query performance.
    """
    __tablename__ = 'custom_domains'

    id: UUID = Field(primary_key=True, default_factory=uuid4)
    tenant_id: UUID = Field(index=True)  # 🔑 Sharding key for Citus Data
    domain_name: str = Field(unique=True, index=True)
    cname_target: str = Field(max_length=255)  # Ex: [tenant_id].trix-sites.com
    frontend_type: str = Field(index=True)  # 'ONLINE_STORE', 'DIGITAL_MENU'
    verification_status: VerificationStatus = Field(default=VerificationStatus.PENDING)
    is_active: bool = Field(default=False)
    proxy_config_status: ProxyConfigStatus = Field(default=ProxyConfigStatus.NOT_CONFIGURED)
    dns_validation_attempts: int = Field(default=0)
    last_dns_validation_at: datetime = Field(nullable=True)
    ssl_certificate_id: str = Field(nullable=True)
    ssl_issued_at: datetime = Field(nullable=True)
    ssl_expires_at: datetime = Field(nullable=True)
    ssl_status: SslStatus = Field(default=SslStatus.PENDING_ISSUANCE)
    last_ssl_check_at: datetime = Field(nullable=True)
    ssl_error_details: str = Field(nullable=True)
    region: str = Field(index=True)  # For geo-distribution
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    # Citus Data optimization indexes
    __table_args__ = (
        Index('idx_tenant_domain', 'tenant_id', 'domain_name'),
        Index('idx_region_created', 'region', 'created_at'),
        Index('idx_tenant_active', 'tenant_id', 'is_active'),
        Index('idx_ssl_expiry', 'ssl_expires_at'),
        # Distribute table by tenant_id
        {'citus_table_type': 'distributed'}
    )
```

```python
class DomainRegistration(Base):
    """
    Domain registration model optimized for Citus Data sharding.
    Distributed by tenant_id for optimal query performance.
    """
    __tablename__ = 'domain_registrations'

    id: UUID = Field(primary_key=True, default_factory=uuid4)
    user_id: UUID = Field(index=True)  # Soft reference to users table
    tenant_id: UUID = Field(index=True)  # 🔑 Sharding key for Citus Data
    domain_name: str = Field(index=True)
    tld: str = Field(index=True)
    registrar: str = Field(index=True)
    registration_date: datetime = Field(default_factory=datetime.utcnow)
    expiry_date: datetime = Field(index=True)
    status: DomainStatus = Field(default=DomainStatus.PENDING)
    auto_renew: bool = Field(default=False)
    whois_privacy_enabled: bool = Field(default=False)
    registrar_data: str = Field(nullable=True)  # JSON with registrar-specific data
    region: str = Field(index=True)  # For geo-distribution
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    # Citus Data optimization - co-locate with custom_domains table
    __table_args__ = (
        Index('idx_tenant_domain_reg', 'tenant_id', 'domain_name'),
        Index('idx_expiry_date', 'expiry_date'),
        Index('idx_tenant_status', 'tenant_id', 'status'),
        Index('idx_registrar_status', 'registrar', 'status'),
        # Co-locate with custom_domains table by tenant_id
        {'citus_table_type': 'distributed', 'colocate_with': 'custom_domains'}
    )
```

#### **2. Reference Tables (Global/Replicated)**
```python
class DomainStatus(str, enum.Enum):
    """Domain status enumeration - replicated across all Citus Data nodes."""
    PENDING = "pending"
    ACTIVE = "active"
    EXPIRED = "expired"
    SUSPENDED = "suspended"
    CANCELLED = "cancelled"

class VerificationStatus(str, enum.Enum):
    """Domain verification status enumeration."""
    PENDING = "PENDING"
    VERIFIED = "VERIFIED"
    FAILED = "FAILED"

class ProxyConfigStatus(str, enum.Enum):
    """Proxy configuration status enumeration."""
    NOT_CONFIGURED = "NOT_CONFIGURED"
    CONFIGURED = "CONFIGURED"
    ERROR_CONFIGURING = "ERROR_CONFIGURING"
    PENDING_REMOVAL = "PENDING_REMOVAL"
    REMOVED = "REMOVED"

class SslStatus(str, enum.Enum):
    """SSL certificate status enumeration."""
    PENDING_ISSUANCE = "PENDING_ISSUANCE"
    ACTIVE = "ACTIVE"
    ISSUANCE_FAILED = "ISSUANCE_FAILED"
    RENEWAL_FAILED = "RENEWAL_FAILED"
    DISABLED = "DISABLED"

class RegistrarConfig(Base):
    """
    Registrar configurations - replicated across all Citus Data nodes.
    Reference table for consistent registrar definitions.
    """
    __tablename__ = 'registrar_configs'

    id: UUID = Field(primary_key=True, default_factory=uuid4)
    name: str = Field(unique=True, max_length=50)
    api_endpoint: str = Field(max_length=255)
    supported_tlds: List[str] = Field(default_factory=list)
    features: List[str] = Field(default_factory=list)
    is_active: bool = Field(default=True)
    rate_limits: dict = Field(default_factory=dict)

    # Reference table - replicated across all shards
    __table_args__ = {'citus_table_type': 'reference'}
```

### 🚀 **Event-Driven Architecture Enterprise**

#### **📡 Event Schemas**
```python
class DomainCreatedEvent(BaseModel):
    """Event published when a custom domain is created."""
    event_type: str = "domain.created"
    tenant_id: UUID
    domain_id: UUID
    domain_name: str
    frontend_type: str
    timestamp: datetime
    user_id: UUID

class DomainVerifiedEvent(BaseModel):
    """Event published when a domain is successfully verified."""
    event_type: str = "domain.verified"
    tenant_id: UUID
    domain_id: UUID
    domain_name: str
    verification_method: str
    timestamp: datetime

class SSLCertificateIssuedEvent(BaseModel):
    """Event published when SSL certificate is issued."""
    event_type: str = "ssl.certificate.issued"
    tenant_id: UUID
    domain_id: UUID
    certificate_id: str
    expires_at: datetime
    timestamp: datetime
```

#### **🔄 Event Handlers**
- **Domain Events**: Criação, verificação, ativação de domínios
- **DNS Events**: Configuração automática de registros DNS
- **SSL Events**: Emissão e renovação automática de certificados
- **Integration Events**: Sincronização com CDN e proxy reverso

## 🚀 **Funcionalidades Enterprise Implementadas**

### ✅ **Módulo Custom Domains (Enterprise-Grade)**

#### **Gestão de Domínios Personalizados**
- CRUD completo com sharding otimizado por tenant_id
- Validação DNS automática via PowerDNS integration
- Configuração de proxy reverso via Varnish
- Gestão de certificados SSL/TLS via Let's Encrypt
- Integração com domínios registrados via event-driven architecture
- Verificação de features de assinatura via tenant service
- Multi-region support com geo-distribution
- Auto-scaling baseado em métricas customizadas

#### **Tipos de Frontend Suportados**
- **ONLINE_STORE:** Loja virtual personalizada com CDN
- **DIGITAL_MENU:** Menu digital para restaurantes
- **CORPORATE_SITE:** Sites corporativos
- **LANDING_PAGE:** Landing pages otimizadas
- **API_GATEWAY:** Endpoints de API personalizados
- **Extensível:** Suporte a novos tipos via configuration

#### **Verificação e Validação Enterprise**
- Verificação DNS automática via PowerDNS
- Validação de CNAME records com retry logic
- Monitoramento de status SSL via Prometheus
- Health checks distribuídos
- Chaos engineering para resilience testing

### ✅ **Módulo Domain Registration (Enterprise-Grade)**

#### **Registro de Domínios**
- Verificação de disponibilidade via múltiplos registradores
- Registro através de registradores com circuit breakers
- Renovação automática configurável via event scheduling
- Transferência de domínios com saga pattern
- Gestão de nameservers via PowerDNS
- Bulk operations para enterprise customers
- Rate limiting e throttling por registrador

#### **Registradores Suportados (Enterprise)**
- **GoDaddy:** API v1 completa com circuit breakers
- **Namecheap:** API sandbox e produção com retry logic
- **OpenSRS:** Integração enterprise com monitoring
- **ResellerClub:** API completa com rate limiting
- **Cloudflare Registrar:** Enterprise integration
- **Google Domains:** API integration
- **Extensível:** Plugin architecture para novos registradores

#### **Funcionalidades Avançadas Enterprise**
- WHOIS Privacy Protection com compliance tracking
- Auto-renovação configurável via event-driven scheduling
- Gestão de contatos (Admin, Tech, Billing) com encryption
- Configuração de nameservers customizados via PowerDNS
- Monitoramento de expiração com alerting via Prometheus
- Bulk domain operations para enterprise customers
- Multi-currency pricing support
- Compliance tracking (GDPR, CCPA, etc.)

### ✅ **Módulo DNS Management (Enterprise-Grade)**

#### **Gestão DNS Avançada**
- PowerDNS integration para controle total de DNS
- Gestão de zonas DNS com sharding otimizado
- Registros DNS automáticos (A, AAAA, CNAME, MX, TXT)
- DNSSEC support para segurança avançada
- GeoDNS para otimização de performance global
- Health checks automáticos para failover
- API completa para gestão programática

#### **Funcionalidades DNS Enterprise**
- Multi-region DNS com anycast
- Load balancing via DNS com health checks
- CDN integration via CNAME automation
- Email routing via MX records automation
- SPF, DKIM, DMARC automation para email security
- DNS analytics e monitoring via Prometheus
- Bulk DNS operations para enterprise customers

### ✅ **Módulo SSL Management (Enterprise-Grade)**

#### **Gestão SSL/TLS Avançada**
- Let's Encrypt integration com automation completa
- Wildcard certificates support
- Multi-domain certificates (SAN)
- Certificate transparency monitoring
- Auto-renewal com event-driven scheduling
- Certificate pinning support
- OCSP stapling automation

#### **Funcionalidades SSL Enterprise**
- Custom CA integration para enterprise customers
- Certificate lifecycle management
- Compliance tracking (PCI DSS, SOX, etc.)
- Certificate inventory e audit trails
- Performance monitoring via Prometheus
- Security scanning e vulnerability assessment
- Bulk certificate operations

## 🔗 **Integrações Enterprise com Outros Serviços**

### **🏗️ Serviços Core (Event-Driven Integration)**
- **Auth Service (8001):**
  - JWT validation via service mesh
  - User authentication para domain operations
  - Role-based access control via OPA policies
  - mTLS communication via Istio/Linkerd
- **User Service (8002):**
  - User profile validation via Kafka events
  - User-domain association management
  - Soft references sem FK constraints
  - Event-driven user updates
- **Tenant Service (8003):**
  - Tenant validation e ownership verification
  - Subscription feature verification via events
  - Multi-tenant isolation via sharding
  - Tenant-specific domain limits
- **Core Service (8004):**
  - System configuration via shared events
  - Global settings synchronization
  - Health check coordination
  - Service discovery integration

### **🏢 Serviços de Infraestrutura (Event-Driven)**
- **Supplier Service (8005):**
  - Domain registrar management
  - Supplier contract automation
  - Cost optimization via bulk operations
  - SLA monitoring e compliance
- **I18n Service (8006):**
  - Multi-language domain management
  - Localized error messages
  - Regional compliance requirements
  - Geo-specific domain policies
- **Notification Service (8007):**
  - Domain expiration alerts via Kafka
  - SSL certificate renewal notifications
  - DNS propagation status updates
  - Real-time status notifications via WebSocket
- **Commerce Service (8008):**
  - Domain purchase processing
  - Billing integration para domain renewals
  - Payment processing via events
  - Invoice generation automation

### **📡 Serviços de Media e CDN (Event-Driven)**
- **CDN Service (8009):**
  - Varnish cache configuration automation
  - MinIO storage integration
  - PowerDNS coordination
  - Edge location optimization
- **Media Service (8010):**
  - Asset delivery via custom domains
  - Image optimization per domain
  - Video streaming configuration
  - Static content caching

### **💳 Serviços de Pagamento (Event-Driven)**
- **Payment Service (8011):**
  - Domain registration payments
  - Renewal payment automation
  - Refund processing
  - Multi-currency support

### **🔧 Serviços Compartilhados (Event-Driven)**
- **HR Module:**
  - Employee domain access management
  - Role-based domain permissions
  - Audit trail para compliance
- **CRM Module:**
  - Customer domain relationship tracking
  - Support ticket integration
  - Customer communication automation
- **Financial Module:**
  - Domain cost tracking
  - Revenue recognition
  - Financial reporting integration
- **Email Module:**
  - Custom domain email configuration
  - MX record automation
  - Email security (SPF, DKIM, DMARC)

### **🌐 Frontend Integration (Event-Driven)**
- **Frontend Service:**
  - Domain-based routing
  - Custom domain preview
  - Real-time status updates
  - User interface customization per domain

### **🔌 Integrações Externas Enterprise**
- **Registradores de Domínio:**
  - GoDaddy, Namecheap, OpenSRS, ResellerClub
  - Cloudflare Registrar, Google Domains
  - Circuit breakers e retry logic
  - Rate limiting e throttling
- **Provedores DNS:**
  - PowerDNS (primary), Cloudflare, Route53
  - Multi-provider failover
  - GeoDNS optimization
  - DNSSEC automation
- **Certificados SSL:**
  - Let's Encrypt (primary), DigiCert, Sectigo
  - Wildcard e multi-domain support
  - Custom CA integration
  - Certificate transparency monitoring
- **CDN e Proxy:**
  - Varnish (primary), Cloudflare, AWS CloudFront
  - Edge optimization
  - DDoS protection
  - Performance monitoring

## 📡 **Endpoints da API Enterprise (v1)**

### **Custom Domains (/api/v1/custom-domains)**
```http
# Domain Management
POST   /                           # Criar domínio personalizado
GET    /                           # Listar domínios do tenant (paginado)
GET    /{domain_id}                # Obter domínio específico
PUT    /{domain_id}                # Atualizar configurações do domínio
DELETE /{domain_id}                # Remover domínio
POST   /{domain_id}/verify         # Verificar DNS
GET    /{domain_id}/status         # Status detalhado do domínio

# Bulk Operations (Enterprise)
POST   /bulk/create               # Criação em lote
POST   /bulk/verify               # Verificação em lote
POST   /bulk/delete               # Remoção em lote

# SSL Management
GET    /{domain_id}/ssl           # Status do certificado SSL
POST   /{domain_id}/ssl/renew     # Renovar certificado
POST   /{domain_id}/ssl/force-issue # Forçar emissão

# DNS Management
GET    /{domain_id}/dns           # Registros DNS
POST   /{domain_id}/dns           # Criar registro DNS
PUT    /{domain_id}/dns/{record_id} # Atualizar registro DNS
DELETE /{domain_id}/dns/{record_id} # Remover registro DNS

# Analytics & Monitoring
GET    /{domain_id}/analytics     # Analytics do domínio
GET    /{domain_id}/health        # Health check do domínio
GET    /{domain_id}/performance   # Métricas de performance
```

### **Domain Registration (/api/v1/domain-registration)**
```http
# Domain Registration
POST   /availability              # Verificar disponibilidade
POST   /domains                   # Registrar domínio
GET    /domains                   # Listar domínios registrados (paginado)
GET    /domains/{id}              # Obter domínio específico
PUT    /domains/{id}              # Atualizar configurações
DELETE /{id}                      # Cancelar domínio

# Domain Operations
POST   /domains/{id}/renew        # Renovar domínio
POST   /domains/{id}/transfer     # Transferir domínio
PUT    /domains/{id}/nameservers  # Atualizar nameservers
PUT    /domains/{id}/whois-privacy # Configurar privacidade
PUT    /domains/{id}/auto-renew   # Configurar auto-renovação

# Bulk Operations (Enterprise)
POST   /bulk/register             # Registro em lote
POST   /bulk/renew                # Renovação em lote
POST   /bulk/transfer             # Transferência em lote

# Registrar Management
GET    /registrars                # Listar registradores disponíveis
GET    /registrars/{name}/tlds    # TLDs suportados por registrador
GET    /registrars/{name}/pricing # Preços por registrador

# Analytics & Reporting
GET    /domains/{id}/analytics    # Analytics do domínio
GET    /domains/{id}/history      # Histórico de mudanças
GET    /reports/expiring          # Relatório de domínios expirando
GET    /reports/costs             # Relatório de custos
```

### **DNS Management (/api/v1/dns)**
```http
# Zone Management
GET    /zones                     # Listar zonas DNS
POST   /zones                     # Criar zona DNS
GET    /zones/{zone_id}           # Obter zona específica
PUT    /zones/{zone_id}           # Atualizar zona
DELETE /zones/{zone_id}           # Remover zona

# Record Management
GET    /zones/{zone_id}/records   # Listar registros da zona
POST   /zones/{zone_id}/records   # Criar registro DNS
GET    /records/{record_id}       # Obter registro específico
PUT    /records/{record_id}       # Atualizar registro
DELETE /records/{record_id}       # Remover registro

# Bulk Operations
POST   /zones/{zone_id}/records/bulk # Operações em lote

# DNSSEC
GET    /zones/{zone_id}/dnssec    # Status DNSSEC
POST   /zones/{zone_id}/dnssec/enable # Habilitar DNSSEC
POST   /zones/{zone_id}/dnssec/disable # Desabilitar DNSSEC

# Analytics
GET    /zones/{zone_id}/analytics # Analytics da zona
GET    /zones/{zone_id}/health    # Health check da zona
```

### **SSL Management (/api/v1/ssl)**
```http
# Certificate Management
GET    /certificates              # Listar certificados
POST   /certificates              # Solicitar certificado
GET    /certificates/{cert_id}    # Obter certificado específico
DELETE /certificates/{cert_id}    # Revogar certificado

# Certificate Operations
POST   /certificates/{cert_id}/renew # Renovar certificado
POST   /certificates/{cert_id}/reissue # Reemitir certificado
GET    /certificates/{cert_id}/status # Status do certificado

# Bulk Operations
POST   /certificates/bulk/renew   # Renovação em lote
POST   /certificates/bulk/check   # Verificação em lote

# Analytics & Monitoring
GET    /certificates/expiring     # Certificados expirando
GET    /certificates/{cert_id}/analytics # Analytics do certificado
GET    /certificates/compliance   # Relatório de compliance
```

### **Health & Monitoring (/api/v1/health)**
```http
GET    /                          # Health check geral
GET    /detailed                  # Health check detalhado
GET    /metrics                   # Métricas Prometheus
GET    /dependencies              # Status das dependências
```

## 🔧 **Configuração Enterprise e Deploy (Integração com Shared Lib)**

> **🔗 Shared Configuration**: Este serviço utiliza configurações compartilhadas da `microservices/core/shared_lib/infrastructure/config/` para garantir consistência entre microserviços.

### **Variáveis de Ambiente Enterprise (Usando Shared Lib)**
```env
# Service Identity
SERVICE_NAME=domain-service
SERVICE_VERSION=2.0.0
ENVIRONMENT=production

# 🔗 Database (Citus Data Sharded) - Configuração da Shared Lib
# Configurações base definidas em: shared_lib/infrastructure/config/database.py
DATABASE_URL=postgresql+asyncpg://${DOMAINS_DB_USER}:${DOMAINS_DB_PASSWORD}@trix-citus-coordinator:5432/${DOMAINS_DB_NAME}
DATABASE_SHARD_COUNT=32  # 🔗 Definido na shared_lib
DATABASE_REPLICATION_FACTOR=2  # 🔗 Definido na shared_lib
PGBOUNCER_URL=postgresql://${DOMAINS_DB_USER}:${DOMAINS_DB_PASSWORD}@trix-pgbouncer-domains:6432/${DOMAINS_DB_NAME}

# 🔗 Redis Streams & Caching - Configuração da Shared Lib
# Configurações base definidas em: shared_lib/infrastructure/config/redis_config.py
REDIS_URL=redis://:${REDIS_PASSWORD}@trix-redis-cluster:6379/15
REDIS_CLUSTER_NODES=trix-redis-1:6379,trix-redis-2:6379,trix-redis-3:6379  # 🔗 Shared Lib

# 🔗 Service Mesh (Istio/Linkerd) - Configuração da Shared Lib
# Configurações base definidas em: shared_lib/infrastructure/kubernetes/istio_configs/
SERVICE_MESH_ENABLED=true  # 🔗 Shared Lib
MTLS_ENABLED=true  # 🔗 Shared Lib
ISTIO_PROXY_CONFIG=/etc/istio/proxy  # 🔗 Shared Lib

# 🔗 Messaging (Event-Driven) - Configuração da Shared Lib
# Configurações base definidas em: shared_lib/infrastructure/config/kafka_config.py
KAFKA_BOOTSTRAP_SERVERS=trix-kafka-1:9092,trix-kafka-2:9092,trix-kafka-3:9092  # 🔗 Shared Lib
KAFKA_TOPIC_DOMAINS=domain-events  # Específico do Domain Service
KAFKA_TOPIC_DNS=dns-events  # Específico do Domain Service
KAFKA_TOPIC_SSL=ssl-events  # Específico do Domain Service
RABBITMQ_URL=amqp://${RABBITMQ_USER}:${RABBITMQ_PASSWORD}@trix-rabbitmq-cluster:5672/  # 🔗 Shared Lib
RABBITMQ_EXCHANGE=domain-exchange  # Específico do Domain Service

# 🔗 HashiCorp Vault (Secrets Management) - Configuração da Shared Lib
# Configurações base definidas em: shared_lib/infrastructure/config/vault.py
VAULT_ADDR=https://trix-vault:8200  # 🔗 Shared Lib
VAULT_TOKEN=${VAULT_TOKEN}  # 🔗 Shared Lib
VAULT_MOUNT_PATH=secret/domain-service  # Específico do Domain Service
VAULT_ROLE=domain-service-role  # Específico do Domain Service

# 🔗 Observability - Configuração da Shared Lib
# Configurações base definidas em: shared_lib/infrastructure/observability/
PROMETHEUS_ENDPOINT=http://trix-prometheus:9090  # 🔗 Shared Lib
JAEGER_AGENT_HOST=trix-jaeger-agent  # 🔗 Shared Lib
JAEGER_AGENT_PORT=6831  # 🔗 Shared Lib
ELASTICSEARCH_URL=https://trix-elasticsearch:9200  # 🔗 Shared Lib
GRAFANA_URL=http://trix-grafana:3000  # 🔗 Shared Lib

# 🔗 Serviços Internos (Service Discovery) - URLs da Shared Lib
# URLs base definidas em: shared_lib/infrastructure/config/service_urls.py
AUTH_SERVICE_URL=http://trix-core-auth.trix-system.svc.cluster.local:8001  # 🔗 Shared Lib
USER_SERVICE_URL=http://trix-core-user.trix-system.svc.cluster.local:8002  # 🔗 Shared Lib
TENANT_SERVICE_URL=http://trix-core-tenant.trix-system.svc.cluster.local:8003  # 🔗 Shared Lib
CORE_SERVICE_URL=http://trix-core-core.trix-system.svc.cluster.local:8004  # 🔗 Shared Lib
SUPPLIER_SERVICE_URL=http://trix-core-supplier.trix-system.svc.cluster.local:8005  # 🔗 Shared Lib
I18N_SERVICE_URL=http://trix-core-i18n.trix-system.svc.cluster.local:8006  # 🔗 Shared Lib
NOTIFICATION_SERVICE_URL=http://trix-core-notification.trix-system.svc.cluster.local:8007  # 🔗 Shared Lib
COMMERCE_SERVICE_URL=http://trix-core-commerce.trix-system.svc.cluster.local:8008  # 🔗 Shared Lib
CDN_SERVICE_URL=http://trix-core-cdn.trix-system.svc.cluster.local:8009  # 🔗 Shared Lib
MEDIA_SERVICE_URL=http://trix-core-media.trix-system.svc.cluster.local:8010  # 🔗 Shared Lib
PAYMENT_SERVICE_URL=http://trix-core-payment.trix-system.svc.cluster.local:8011  # 🔗 Shared Lib

# ⚙️ CONFIGURAÇÕES ESPECÍFICAS DO DOMAIN SERVICE
# Domain Registrars (Vault-managed secrets) - Específico do Domain Service
REGISTRAR_CONFIGS_PATH=secret/registrars
GODADDY_API_KEY_PATH=secret/registrars/godaddy/api_key
NAMECHEAP_API_KEY_PATH=secret/registrars/namecheap/api_key
OPENSRS_API_KEY_PATH=secret/registrars/opensrs/api_key
RESELLERCLUB_API_KEY_PATH=secret/registrars/resellerclub/api_key
CLOUDFLARE_API_KEY_PATH=secret/registrars/cloudflare/api_key

# DNS Management (PowerDNS) - Específico do Domain Service
POWERDNS_API_URL=http://trix-powerdns:8081
POWERDNS_API_KEY_PATH=secret/dns/powerdns/api_key
POWERDNS_ZONES_PATH=/etc/powerdns/zones
DNS_PROPAGATION_CHECK_INTERVAL=30
DNS_HEALTH_CHECK_INTERVAL=60
DNSSEC_ENABLED=true
GEODNS_ENABLED=true

# SSL/TLS Management - Específico do Domain Service
LETSENCRYPT_ENABLED=true
LETSENCRYPT_EMAIL=<EMAIL>
LETSENCRYPT_STAGING=false
SSL_AUTO_RENEWAL=true
SSL_RENEWAL_DAYS_BEFORE=30
SSL_CERTIFICATE_STORAGE=vault
CERTIFICATE_TRANSPARENCY_ENABLED=true
WILDCARD_CERTIFICATES_ENABLED=true
CUSTOM_CA_ENABLED=false

# CDN Integration (Varnish + MinIO) - Específico do Domain Service
VARNISH_ADMIN_URL=http://trix-varnish:6082
VARNISH_CONFIG_PATH=/etc/varnish/domains
MINIO_ENDPOINT=http://trix-minio:9000
MINIO_ACCESS_KEY_PATH=secret/cdn/minio/access_key
MINIO_SECRET_KEY_PATH=secret/cdn/minio/secret_key

# 🔗 Performance & Scaling - Configuração da Shared Lib
# Configurações base definidas em: shared_lib/infrastructure/config/performance.py
MAX_WORKERS=8  # 🔗 Shared Lib
WORKER_CONNECTIONS=1000  # 🔗 Shared Lib
KEEPALIVE_TIMEOUT=65  # 🔗 Shared Lib
REQUEST_TIMEOUT=30  # 🔗 Shared Lib
MAX_REQUEST_SIZE=10MB  # 🔗 Shared Lib
RATE_LIMIT_PER_MINUTE=1000  # 🔗 Shared Lib
BULK_OPERATION_LIMIT=100  # Específico do Domain Service

# 🔗 Security - Configuração da Shared Lib
# Configurações base definidas em: shared_lib/infrastructure/security/
CORS_ORIGINS=https://*.trix.com,https://trix.com  # 🔗 Shared Lib
ALLOWED_HOSTS=*.trix.com,trix.com  # 🔗 Shared Lib
SECURITY_HEADERS_ENABLED=true  # 🔗 Shared Lib
CSRF_PROTECTION_ENABLED=true  # 🔗 Shared Lib
XSS_PROTECTION_ENABLED=true  # 🔗 Shared Lib

# 🔗 Compliance & Audit - Configuração da Shared Lib
# Configurações base definidas em: shared_lib/infrastructure/config/compliance.py
AUDIT_LOG_ENABLED=true  # 🔗 Shared Lib
AUDIT_LOG_LEVEL=INFO  # 🔗 Shared Lib
GDPR_COMPLIANCE_ENABLED=true  # 🔗 Shared Lib
DATA_RETENTION_DAYS=2555  # 7 years - 🔗 Shared Lib
COMPLIANCE_REPORTING_ENABLED=true  # 🔗 Shared Lib

# 🔗 Multi-Region - Configuração da Shared Lib
# Configurações base definidas em: shared_lib/infrastructure/config/regions.py
REGION=us-east-1  # 🔗 Shared Lib
AVAILABILITY_ZONE=us-east-1a  # 🔗 Shared Lib
CROSS_REGION_REPLICATION=true  # 🔗 Shared Lib
DISASTER_RECOVERY_ENABLED=true  # 🔗 Shared Lib
```

### **Kubernetes Deployment (Helm - Usando Shared Lib)**

> **🔗 Shared Templates**: Este serviço utiliza templates Helm compartilhados da `microservices/core/shared_lib/infrastructure/kubernetes/helm_templates/` para garantir consistência entre deployments.

```yaml
# values-production.yaml (Extends shared_lib/infrastructure/kubernetes/helm_templates/values-base.yaml)

# 🔗 Configurações base herdadas da Shared Lib
# Base configuration from: shared_lib/infrastructure/kubernetes/helm_templates/values-base.yaml
replicaCount: 3  # 🔗 Shared Lib default
autoscaling:  # 🔗 Shared Lib configuration
  enabled: true
  minReplicas: 3
  maxReplicas: 100
  targetCPUUtilizationPercentage: 70
  targetMemoryUtilizationPercentage: 80

# ⚙️ Configurações específicas do Domain Service
image:
  repository: trix/domain-service  # Específico do Domain Service
  tag: "2.0.0"
  pullPolicy: IfNotPresent  # 🔗 Shared Lib default

service:
  type: ClusterIP  # 🔗 Shared Lib default
  port: 8015  # Específico do Domain Service
  targetPort: 8000  # 🔗 Shared Lib default

# ⚙️ Ingress específico do Domain Service
ingress:
  enabled: true  # 🔗 Shared Lib default
  className: istio  # 🔗 Shared Lib default
  annotations:  # 🔗 Shared Lib defaults
    kubernetes.io/ingress.class: istio
    cert-manager.io/cluster-issuer: letsencrypt-prod
  hosts:
    - host: domain-api.trix.com  # Específico do Domain Service
      paths:
        - path: /
          pathType: Prefix
  tls:
    - secretName: domain-api-tls  # Específico do Domain Service
      hosts:
        - domain-api.trix.com

# 🔗 Resources herdados da Shared Lib
resources:  # 🔗 Shared Lib configuration
  limits:
    cpu: 2000m
    memory: 4Gi
  requests:
    cpu: 500m
    memory: 1Gi

# 🔗 Node scheduling herdado da Shared Lib
nodeSelector:  # 🔗 Shared Lib configuration
  node-type: compute-optimized

tolerations:  # 🔗 Shared Lib configuration
  - key: "compute-optimized"
    operator: "Equal"
    value: "true"
    effect: "NoSchedule"

affinity:  # 🔗 Shared Lib configuration
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - weight: 100
      podAffinityTerm:
        labelSelector:
          matchExpressions:
          - key: app
            operator: In
            values:
            - domain-service  # Específico do Domain Service
        topologyKey: kubernetes.io/hostname

# 🔗 Istio Service Mesh (Configuração da Shared Lib)
# Base configuration from: shared_lib/infrastructure/kubernetes/istio_configs/
istio:  # 🔗 Shared Lib configuration
  enabled: true  # 🔗 Shared Lib default
  virtualService:  # 🔗 Shared Lib template
    enabled: true
    gateways:
      - istio-system/trix-gateway  # 🔗 Shared Lib default
    hosts:
      - domain-api.trix.com  # Específico do Domain Service
  destinationRule:  # 🔗 Shared Lib template
    enabled: true
    trafficPolicy:  # 🔗 Shared Lib defaults
      loadBalancer:
        simple: LEAST_CONN
      connectionPool:
        tcp:
          maxConnections: 100
        http:
          http1MaxPendingRequests: 50
          maxRequestsPerConnection: 10
  peerAuthentication:  # 🔗 Shared Lib template
    enabled: true
    mtls:
      mode: STRICT  # 🔗 Shared Lib default

# 🔗 Monitoring (Configuração da Shared Lib)
# Base configuration from: shared_lib/infrastructure/kubernetes/monitoring/
monitoring:  # 🔗 Shared Lib configuration
  enabled: true  # 🔗 Shared Lib default
  serviceMonitor:  # 🔗 Shared Lib template
    enabled: true
    interval: 30s  # 🔗 Shared Lib default
    path: /metrics  # 🔗 Shared Lib default
  prometheusRule:  # 🔗 Shared Lib template
    enabled: true
    rules:
      # ⚙️ Alertas específicos do Domain Service
      - alert: DomainServiceHighErrorRate
        expr: rate(http_requests_total{job="domain-service",status=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: High error rate in Domain Service
      - alert: DomainVerificationFailureRate
        expr: rate(domain_verification_failures_total[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: High domain verification failure rate
      - alert: SSLCertificateExpiringCritical
        expr: ssl_certificate_expiry_days < 7
        for: 1h
        labels:
          severity: critical
        annotations:
          summary: SSL certificates expiring soon

# Security
security:
  podSecurityPolicy:
    enabled: true
  networkPolicy:
    enabled: true
    ingress:
      - from:
        - namespaceSelector:
            matchLabels:
              name: istio-system
        - namespaceSelector:
            matchLabels:
              name: trix-system
    egress:
      - to:
        - namespaceSelector:
            matchLabels:
              name: trix-system
      - to: []
        ports:
        - protocol: TCP
          port: 443  # HTTPS
        - protocol: TCP
          port: 53   # DNS
        - protocol: UDP
          port: 53   # DNS

# Vault Integration
vault:
  enabled: true
  role: domain-service-role
  serviceAccount: domain-service-vault
  secrets:
    - secretPath: secret/domain-service/database
      mountPath: /vault/secrets/database
    - secretPath: secret/registrars
      mountPath: /vault/secrets/registrars
    - secretPath: secret/dns/powerdns
      mountPath: /vault/secrets/dns
```

## 🔒 **Segurança Enterprise e Compliance**

### **🛡️ Autenticação e Autorização Enterprise**
- **mTLS**: Comunicação segura via Istio/Linkerd service mesh
- **JWT Tokens**: Validação via Auth Service com HashiCorp Vault
- **OPA Policies**: Autorização baseada em políticas centralizadas
- **RBAC**: Role-based access control com granularidade por tenant
- **API Keys**: Gestão segura via Vault para integrações externas
- **Rate Limiting**: Proteção contra abuse com limites por usuário/tenant/IP
- **Circuit Breakers**: Proteção contra cascading failures

### **🔐 Proteção de Dados Enterprise**
- **Encryption at Rest**: Dados criptografados no PostgreSQL com TDE
- **Encryption in Transit**: TLS 1.3 para todas as comunicações
- **Vault Integration**: Secrets management centralizado
- **WHOIS Privacy**: Proteção automática de dados pessoais
- **Data Masking**: Logs sanitizados sem dados sensíveis
- **PII Protection**: Identificação e proteção automática de dados pessoais
- **Backup Encryption**: Backups criptografados com rotação de chaves

### **🔍 Validação e Sanitização Enterprise**
- **Input Validation**: Validação rigorosa via Pydantic schemas
- **Domain Validation**: Verificação de ownership via DNS challenges
- **SQL Injection Protection**: ORM com prepared statements
- **XSS Protection**: Sanitização de inputs e outputs
- **CSRF Protection**: Tokens CSRF para operações críticas
- **Domain Hijacking Prevention**: Verificação multi-fator para transferências
- **Rate Limiting**: Proteção contra brute force e DoS

### **📋 Compliance Enterprise**
- **GDPR**: Compliance completo com direito ao esquecimento
- **CCPA**: California Consumer Privacy Act compliance
- **SOX**: Sarbanes-Oxley compliance para auditoria financeira
- **PCI DSS**: Payment Card Industry compliance para pagamentos
- **ISO 27001**: Information security management compliance
- **SOC 2**: Service Organization Control 2 compliance
- **HIPAA**: Healthcare compliance (quando aplicável)

### **🔐 Security Monitoring**
- **Falco**: Runtime security monitoring
- **SIEM Integration**: Security Information and Event Management
- **Vulnerability Scanning**: Automated security scanning
- **Penetration Testing**: Regular security assessments
- **Threat Intelligence**: Integration with threat feeds
- **Incident Response**: Automated incident response workflows
- **Security Metrics**: Prometheus metrics para security KPIs

### **📊 Audit e Compliance Tracking**
- **Audit Logs**: Logs imutáveis de todas as operações
- **Compliance Dashboard**: Real-time compliance status
- **Automated Reporting**: Relatórios automáticos de compliance
- **Data Lineage**: Rastreamento completo de dados
- **Change Management**: Approval workflows para mudanças críticas
- **Evidence Collection**: Coleta automática de evidências para auditorias
- **Retention Policies**: Políticas de retenção baseadas em compliance

## 📊 **Monitoramento Enterprise e Observabilidade**

### **📈 Métricas Prometheus (Business & Infrastructure)**
```yaml
# Business Metrics
domain_registrations_total{registrar, tld, region}
domain_verifications_total{status, method, region}
ssl_certificates_issued_total{ca, type, region}
dns_queries_total{zone, record_type, region}
domain_renewals_total{registrar, auto_renew, region}
domain_transfers_total{from_registrar, to_registrar, region}

# Performance Metrics
domain_verification_duration_seconds{method, region}
ssl_issuance_duration_seconds{ca, type, region}
dns_propagation_duration_seconds{zone, region}
api_request_duration_seconds{endpoint, method, status}
database_query_duration_seconds{table, operation}

# Infrastructure Metrics
database_connections_active{shard, region}
kafka_messages_produced_total{topic, partition}
rabbitmq_messages_published_total{exchange, routing_key}
redis_operations_total{operation, database}
vault_secret_requests_total{mount, operation}

# Error Metrics
domain_verification_failures_total{reason, region}
ssl_issuance_failures_total{ca, reason, region}
registrar_api_errors_total{registrar, error_code}
dns_resolution_failures_total{zone, record_type}
```

### **🚨 Alertas Críticos Configurados**
```yaml
# Critical Alerts
- alert: DomainServiceDown
  expr: up{job="domain-service"} == 0
  for: 1m
  severity: critical

- alert: HighDomainVerificationFailureRate
  expr: rate(domain_verification_failures_total[5m]) > 0.1
  for: 5m
  severity: critical

- alert: SSLCertificateExpiringCritical
  expr: ssl_certificate_expiry_days < 7
  for: 1h
  severity: critical

- alert: DatabaseShardDown
  expr: database_shard_up == 0
  for: 2m
  severity: critical

- alert: RegistrarAPIDown
  expr: registrar_api_up == 0
  for: 5m
  severity: critical

# Warning Alerts
- alert: HighAPILatency
  expr: histogram_quantile(0.95, api_request_duration_seconds) > 1
  for: 10m
  severity: warning

- alert: SSLCertificateExpiringWarning
  expr: ssl_certificate_expiry_days < 30
  for: 1h
  severity: warning

- alert: DNSPropagationSlow
  expr: histogram_quantile(0.95, dns_propagation_duration_seconds) > 300
  for: 15m
  severity: warning
```

### **📊 Grafana Dashboards Enterprise**
- **Domain Service Overview**: KPIs principais e health status
- **Domain Operations**: Registrations, verifications, renewals
- **SSL Certificate Management**: Issuance, renewals, expirations
- **DNS Management**: Zones, records, propagation times
- **Performance Dashboard**: Latency, throughput, error rates
- **Infrastructure Dashboard**: Database, messaging, cache metrics
- **Security Dashboard**: Authentication, authorization, threats
- **Compliance Dashboard**: Audit trails, data retention, GDPR

### **🔍 Jaeger Distributed Tracing**
- **Request Tracing**: End-to-end request tracking
- **Service Dependencies**: Service interaction mapping
- **Performance Analysis**: Bottleneck identification
- **Error Analysis**: Error propagation tracking
- **Cross-Service Correlation**: Multi-service request correlation

### **📝 Structured Logging (ELK Stack)**
```json
{
  "timestamp": "2025-07-14T10:30:00Z",
  "level": "INFO",
  "service": "domain-service",
  "version": "2.0.0",
  "trace_id": "abc123",
  "span_id": "def456",
  "tenant_id": "tenant-123",
  "user_id": "user-456",
  "operation": "domain.verification",
  "domain_name": "example.com",
  "verification_method": "dns_challenge",
  "duration_ms": 1500,
  "status": "success",
  "region": "us-east-1",
  "metadata": {
    "dns_records_checked": 3,
    "propagation_time_ms": 1200,
    "registrar": "godaddy"
  }
}
```

### **🎯 SLA e SLO Targets**
- **Availability**: 99.99% uptime (4.32 minutes downtime/month)
- **API Latency**: P95 < 500ms, P99 < 1s
- **Domain Verification**: 95% success rate within 5 minutes
- **SSL Issuance**: 99% success rate within 10 minutes
- **DNS Propagation**: 95% within 5 minutes globally
- **Error Rate**: < 0.1% for all operations
- **Recovery Time**: < 5 minutes for critical failures

### **📱 Alerting Channels**
- **PagerDuty**: Critical alerts 24/7
- **Slack**: Warning alerts during business hours
- **Email**: Daily/weekly reports
- **Webhook**: Integration with external systems
- **SMS**: Critical alerts for on-call engineers

## 🚀 **Roadmap Enterprise (v2.0)**

### 🎯 **Fase 1: Infrastructure Foundation (Sprint 1-2)**
1. **🔄 Citus Data Setup**: Configurar sharding PostgreSQL com tenant_id
2. **🔄 Vault Integration**: Migrar secrets para HashiCorp Vault
3. **🔄 Istio Service Mesh**: Implementar mTLS automático
4. **🔄 Kubernetes Manifests**: Helm charts para deployment
5. **🔄 Prometheus Metrics**: Instrumentação básica

### 🎯 **Fase 2: Event-Driven Core (Sprint 3-4)**
1. **🔄 Kafka Integration**: Event sourcing e messaging
2. **🔄 RabbitMQ Setup**: Fast notifications
3. **🔄 Redis Streams**: Real-time updates
4. **🔄 Event Schemas**: Padronização de eventos
5. **🔄 CQRS Implementation**: Separação read/write

### 🎯 **Fase 3: Security & Compliance (Sprint 5-6)**
1. **🔄 OPA Policies**: Autorização centralizada
2. **🔄 Falco Runtime Security**: Monitoramento de segurança
3. **🔄 mTLS Enforcement**: Comunicação segura
4. **🔄 Audit Logging**: Compliance e auditoria
5. **🔄 Data Encryption**: Criptografia em repouso

### 🎯 **Fase 4: Observability & Performance (Sprint 7-8)**
1. **🔄 Jaeger Tracing**: Distributed tracing completo
2. **🔄 ELK Stack**: Centralized logging
3. **🔄 Grafana Dashboards**: Visualização de métricas
4. **🔄 Performance Testing**: Load testing com K6
5. **🔄 Auto-scaling**: HPA baseado em métricas customizadas

### 🎯 **Fase 5: Global Scale (Sprint 9-10)**
1. **🔄 Multi-Region**: Deployment geo-distribuído
2. **🔄 CDN Integration**: Varnish + MinIO + PowerDNS
3. **🔄 Read Replicas**: Otimização de consultas
4. **🔄 Connection Pooling**: PgBouncer optimization
5. **🔄 Chaos Engineering**: Resilience testing

### 🎯 **Fase 6: Advanced Features (Sprint 11-12)**
1. **🔄 Wildcard Domains**: Suporte a certificados wildcard
2. **🔄 GeoDNS**: DNS baseado em localização
3. **🔄 Advanced Analytics**: Machine learning para otimizações
4. **🔄 API Gateway**: Rate limiting e throttling avançado
5. **🔄 Compliance Automation**: Automated compliance reporting

---

**Última Atualização:** 2025-07-24
**Versão:** 2.0.0 (Enterprise-Grade)
**Status:** 🔄 **MIGRAÇÃO PARA SHARED LIB PLANEJADA**
**Target Scale:** Bilhões de usuários simultâneos
**Responsável:** Trix Development Team
**Shared Lib Integration:** 🔄 **PLANEJADO** (Configurações comuns serão migradas)

### 📝 **Log de Mudanças Majores (v2.0 - 2025-07-24)**

#### **🔗 Migração para Shared Library (NOVA - 2025-07-24)**
- 🔄 **Infraestrutura Compartilhada**: Configurações comuns movidas para `shared_lib/infrastructure/`
- 🔄 **Messaging Unificado**: Clientes Kafka, RabbitMQ e Redis centralizados na shared_lib
- 🔄 **Observability Padronizada**: Métricas, tracing e logging compartilhados
- 🔄 **Security Centralizada**: JWT, OPA e criptografia unificados na shared_lib
- 🔄 **Database Layer Comum**: Sharding e conexões Citus Data compartilhados
- 🔄 **Kubernetes Templates**: Manifests e Helm charts padronizados na shared_lib
- ✅ **Funcionalidades Específicas Preservadas**: DNS, SSL e registradores mantidos no Domain Service
- ✅ **Redução de Duplicação**: Eliminação de código duplicado entre microserviços
- ✅ **Manutenibilidade**: Atualizações centralizadas na shared_lib
- ✅ **Consistência**: Padrões uniformes entre todos os microserviços

#### **🎯 Benefícios da Migração para Shared Lib**
- **Consistência**: Configurações padronizadas em todos os serviços
- **Manutenibilidade**: Atualizações centralizadas na shared_lib
- **Reutilização**: Componentes prontos para novos microserviços
- **Redução de Código**: Eliminação de duplicações
- **Facilidade de Desenvolvimento**: Padrões enterprise já implementados

#### **🏗️ Reestruturação Arquitetural Completa**
- 🔄 **Database Sharding**: Migração para Citus Data com sharding por tenant_id
- 🔄 **Service Mesh**: Integração Istio/Linkerd com mTLS automático
- 🔄 **Event-Driven**: Arquitetura completa Kafka + RabbitMQ + Redis Streams
- 🔄 **Security-First**: HashiCorp Vault + OPA Gatekeeper + Falco
- 🔄 **Observability**: Prometheus + Grafana + Jaeger + ELK stack
- ✅ **Estrutura Organizada**: Submódulos preservados + core enterprise unificado

#### **📊 Modelos de Dados Otimizados**
- ✅ **Sharded Tables**: Custom domains e registrations distribuídas por tenant_id
- ✅ **Reference Tables**: Configurations replicadas globalmente
- ✅ **Co-location**: Otimização de joins via co-location strategy
- ✅ **Indexes**: Índices otimizados para queries distribuídas

#### **🚀 Event-Driven Architecture**
- ✅ **Event Sourcing**: Histórico imutável de todas as mudanças
- ✅ **CQRS**: Separação total de comandos e consultas
- ✅ **Multi-Layer Messaging**: Kafka (durability) + RabbitMQ (speed) + Redis (real-time)
- ✅ **Soft References**: Zero FK constraints entre microserviços

#### **🔐 Security Enterprise**
- ✅ **Vault Integration**: Secrets management centralizado
- ✅ **OPA Policies**: Autorização baseada em políticas
- ✅ **mTLS**: Comunicação segura via service mesh
- ✅ **Runtime Security**: Monitoramento com Falco

#### **📈 Observability Completa**
- ✅ **Prometheus Metrics**: Business e infrastructure metrics
- ✅ **Distributed Tracing**: Jaeger para requests distribuídos
- ✅ **Centralized Logging**: ELK stack para logs estruturados
- ✅ **Custom Dashboards**: Grafana para visualização

#### **☸️ Kubernetes Native**
- ✅ **Helm Charts**: Deployment automatizado
- ✅ **Auto-scaling**: HPA baseado em métricas customizadas
- ✅ **Multi-environment**: Dev, staging, production overlays
- ✅ **ArgoCD**: GitOps deployment pipeline

#### **🌍 Global Scale Preparation**
- ✅ **Multi-region**: Estratégia de deployment geo-distribuído
- ✅ **CDN Integration**: Varnish + MinIO + PowerDNS
- ✅ **Connection Pooling**: PgBouncer para otimização
- ✅ **Performance Testing**: Load testing com K6

#### **🔗 Microservices Connectivity**
- ✅ **16 Microservices Integration**: Conectividade completa via events
- ✅ **Service Discovery**: Kubernetes-native service discovery
- ✅ **Circuit Breakers**: Resilience patterns implementados
- ✅ **Health Checks**: Comprehensive health monitoring

### 📋 **Roadmap de Implementação**
- **Fase 1-2**: Infrastructure Foundation (Citus, Vault, Istio, K8s)
- **Fase 3-4**: Event-Driven Core (Kafka, RabbitMQ, CQRS)
- **Fase 5-6**: Security & Compliance (OPA, Falco, Audit)
- **Fase 7-8**: Observability & Performance (Jaeger, ELK, Auto-scaling)
- **Fase 9-10**: Global Scale (Multi-region, CDN, Chaos Engineering)
- **Fase 11-12**: Advanced Features (Wildcard, GeoDNS, ML Analytics)
