"""
Event Schemas
=============

Schemas Pydantic para eventos do Media System.

✅ MIGRAÇÃO CONCLUÍDA - Usando shared_lib para configurações comuns
"""

from datetime import datetime
from typing import Dict, Any, Optional
from uuid import UUID, uuid4

from pydantic import BaseModel, Field


class MediaEventBase(BaseModel):
    """Schema base para eventos de mídia."""
    
    event_type: str = Field(..., description="Tipo do evento")
    media_id: UUID = Field(..., description="ID da mídia")
    tenant_id: UUID = Field(..., description="ID do tenant")
    user_id: UUID = Field(..., description="ID do usuário")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Timestamp do evento")
    correlation_id: UUID = Field(default_factory=uuid4, description="ID de correlação")
    source_service: str = Field(default="media-system", description="Serviço de origem")
    version: str = Field(default="v1", description="Versão do evento")
    data: Dict[str, Any] = Field(default_factory=dict, description="Dados do evento")


class MediaUploadedEvent(MediaEventBase):
    """Evento de upload de mídia."""
    
    event_type: str = Field(default="media.uploaded", const=True)
    
    def __init__(self, **data):
        super().__init__(**data)
        self.data.update({
            "filename": data.get("filename"),
            "file_size": data.get("file_size"),
            "mime_type": data.get("mime_type"),
            "context": data.get("context"),
            "is_public": data.get("is_public", False),
            "region": data.get("region")
        })


class MediaProcessedEvent(MediaEventBase):
    """Evento de processamento de mídia."""
    
    event_type: str = Field(default="media.processed", const=True)
    
    def __init__(self, **data):
        super().__init__(**data)
        self.data.update({
            "job_type": data.get("job_type"),
            "status": data.get("status"),
            "processing_time": data.get("processing_time"),
            "result_data": data.get("result_data", {}),
            "error_message": data.get("error_message")
        })


class MediaDeletedEvent(MediaEventBase):
    """Evento de exclusão de mídia."""
    
    event_type: str = Field(default="media.deleted", const=True)
    
    def __init__(self, **data):
        super().__init__(**data)
        self.data.update({
            "filename": data.get("filename"),
            "file_size": data.get("file_size"),
            "context": data.get("context"),
            "deletion_reason": data.get("deletion_reason", "user_request"),
            "soft_delete": data.get("soft_delete", True)
        })


class MediaAccessEvent(MediaEventBase):
    """Evento de acesso à mídia."""
    
    event_type: str = Field(default="media.accessed", const=True)
    
    def __init__(self, **data):
        super().__init__(**data)
        self.data.update({
            "access_type": data.get("access_type"),  # download, view, thumbnail
            "user_agent": data.get("user_agent"),
            "ip_address": data.get("ip_address"),
            "referer": data.get("referer"),
            "file_size": data.get("file_size"),
            "response_time": data.get("response_time")
        })


class ProcessingJobEvent(MediaEventBase):
    """Evento de job de processamento."""
    
    event_type: str = Field(default="processing.job", const=True)
    
    def __init__(self, **data):
        super().__init__(**data)
        self.data.update({
            "job_id": str(data.get("job_id")),
            "job_type": data.get("job_type"),
            "status": data.get("status"),
            "progress": data.get("progress", 0),
            "priority": data.get("priority", 5),
            "started_at": data.get("started_at"),
            "completed_at": data.get("completed_at"),
            "error_message": data.get("error_message"),
            "result_data": data.get("result_data", {})
        })


class MediaQuotaEvent(MediaEventBase):
    """Evento de quota de mídia."""
    
    event_type: str = Field(default="media.quota", const=True)
    
    def __init__(self, **data):
        super().__init__(**data)
        self.data.update({
            "quota_type": data.get("quota_type"),  # warning, exceeded, reset
            "current_usage": data.get("current_usage"),
            "quota_limit": data.get("quota_limit"),
            "usage_percentage": data.get("usage_percentage"),
            "context": data.get("context")
        })


class MediaSecurityEvent(MediaEventBase):
    """Evento de segurança de mídia."""
    
    event_type: str = Field(default="media.security", const=True)
    
    def __init__(self, **data):
        super().__init__(**data)
        self.data.update({
            "security_type": data.get("security_type"),  # virus_detected, invalid_file, unauthorized_access
            "threat_level": data.get("threat_level"),  # low, medium, high, critical
            "details": data.get("details"),
            "action_taken": data.get("action_taken"),  # blocked, quarantined, deleted
            "scanner_result": data.get("scanner_result", {})
        })


class MediaAnalyticsEvent(MediaEventBase):
    """Evento de analytics de mídia."""
    
    event_type: str = Field(default="media.analytics", const=True)
    
    def __init__(self, **data):
        super().__init__(**data)
        self.data.update({
            "analytics_type": data.get("analytics_type"),  # view, download, share, search
            "session_id": data.get("session_id"),
            "device_type": data.get("device_type"),
            "browser": data.get("browser"),
            "location": data.get("location"),
            "duration": data.get("duration"),
            "engagement_score": data.get("engagement_score")
        })


class MediaCDNEvent(MediaEventBase):
    """Evento de CDN de mídia."""
    
    event_type: str = Field(default="media.cdn", const=True)
    
    def __init__(self, **data):
        super().__init__(**data)
        self.data.update({
            "cdn_action": data.get("cdn_action"),  # cache_hit, cache_miss, purge, sync
            "edge_location": data.get("edge_location"),
            "cache_status": data.get("cache_status"),
            "response_time": data.get("response_time"),
            "bytes_transferred": data.get("bytes_transferred"),
            "optimization_applied": data.get("optimization_applied", [])
        })


# Event factory para criar eventos facilmente
class MediaEventFactory:
    """Factory para criar eventos de mídia."""
    
    @staticmethod
    def create_uploaded_event(media_id: UUID, tenant_id: UUID, user_id: UUID, **kwargs) -> MediaUploadedEvent:
        """Cria evento de upload."""
        return MediaUploadedEvent(
            media_id=media_id,
            tenant_id=tenant_id,
            user_id=user_id,
            **kwargs
        )
    
    @staticmethod
    def create_processed_event(media_id: UUID, tenant_id: UUID, user_id: UUID, **kwargs) -> MediaProcessedEvent:
        """Cria evento de processamento."""
        return MediaProcessedEvent(
            media_id=media_id,
            tenant_id=tenant_id,
            user_id=user_id,
            **kwargs
        )
    
    @staticmethod
    def create_deleted_event(media_id: UUID, tenant_id: UUID, user_id: UUID, **kwargs) -> MediaDeletedEvent:
        """Cria evento de exclusão."""
        return MediaDeletedEvent(
            media_id=media_id,
            tenant_id=tenant_id,
            user_id=user_id,
            **kwargs
        )
    
    @staticmethod
    def create_access_event(media_id: UUID, tenant_id: UUID, user_id: UUID, **kwargs) -> MediaAccessEvent:
        """Cria evento de acesso."""
        return MediaAccessEvent(
            media_id=media_id,
            tenant_id=tenant_id,
            user_id=user_id,
            **kwargs
        )
