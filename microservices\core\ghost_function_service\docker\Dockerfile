# Ghost Function Service - Dockerfile
# O módulo mais poderoso da Trix - Coringa Absoluto
# 
# Multi-stage build para otimização de tamanho e segurança

# === STAGE 1: Builder ===
FROM python:3.11-slim as builder

# Metadados
LABEL maintainer="Trix Platform Team"
LABEL description="Ghost Function Service - O módulo mais poderoso da Trix"
LABEL version="1.0.0"

# Variáveis de ambiente para build
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PIP_NO_CACHE_DIR=1
ENV PIP_DISABLE_PIP_VERSION_CHECK=1

# Instalar dependências do sistema
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Criar diretório de trabalho
WORKDIR /app

# Copiar requirements
COPY microservices/core/ghost_function_service/requirements.txt .

# Instalar dependências Python
RUN pip install --upgrade pip && \
    pip install -r requirements.txt

# === STAGE 2: Runtime ===
FROM python:3.11-slim as runtime

# Variáveis de ambiente
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONPATH=/app
ENV PORT=8026

# Instalar dependências mínimas do sistema
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Criar usuário não-root para segurança
RUN groupadd -r ghost && useradd -r -g ghost ghost

# Criar diretórios
WORKDIR /app
RUN mkdir -p /app/logs && \
    chown -R ghost:ghost /app

# Copiar dependências do builder
COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin

# Copiar código da aplicação
COPY microservices/core/ghost_function_service/app/ ./app/
COPY microservices/core/ghost_function_service/alembic.ini .
COPY microservices/core/ghost_function_service/migrations/ ./migrations/

# Copiar shared_lib (necessário para integração)
COPY microservices/core/shared_lib/ ./microservices/core/shared_lib/

# Ajustar permissões
RUN chown -R ghost:ghost /app

# Mudar para usuário não-root
USER ghost

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:${PORT}/health || exit 1

# Expor porta
EXPOSE ${PORT}

# Comando padrão
CMD ["python", "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8026", "--reload"]
