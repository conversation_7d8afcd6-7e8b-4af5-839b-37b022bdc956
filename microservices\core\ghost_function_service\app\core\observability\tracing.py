"""
Ghost Function Service - Tracing Configuration

Configuração de tracing distribuído específica do Ghost Function Service.
Estende as configurações da shared_lib para funcionalidades específicas.

Author: Trix Platform Team
"""

from opentelemetry import trace
from microservices.core.shared_lib.infrastructure.observability import get_tracer
from ..config.settings import get_settings

# Configurações
settings = get_settings()


def get_ghost_tracer(name: str = "ghost-function-service") -> trace.Tracer:
    """
    Get a tracer instance for Ghost Function Service.
    
    Args:
        name: Tracer name (default: ghost-function-service)
        
    Returns:
        trace.Tracer: Configured tracer instance
    """
    return get_tracer(name)


def trace_proxy_request(tracer: trace.Tracer, service_name: str, endpoint: str):
    """Create a span for proxy request tracing."""
    return tracer.start_span(
        f"proxy_request_{service_name}",
        attributes={
            "service.name": "ghost-function-service",
            "proxy.target_service": service_name,
            "proxy.endpoint": endpoint,
            "operation.type": "proxy_request"
        }
    )


def trace_failover_event(tracer: trace.Tracer, service_name: str, from_source: str, to_source: str):
    """Create a span for failover event tracing."""
    return tracer.start_span(
        f"failover_{service_name}",
        attributes={
            "service.name": "ghost-function-service",
            "failover.service": service_name,
            "failover.from_source": from_source,
            "failover.to_source": to_source,
            "operation.type": "failover"
        }
    )


def trace_hibernation_change(tracer: trace.Tracer, service_name: str, hibernation_level: str):
    """Create a span for hibernation change tracing."""
    return tracer.start_span(
        f"hibernation_change_{service_name}",
        attributes={
            "service.name": "ghost-function-service",
            "hibernation.service": service_name,
            "hibernation.level": hibernation_level,
            "operation.type": "hibernation_change"
        }
    )


def trace_ghost_wake_up(tracer: trace.Tracer, service_name: str, hibernation_level: str):
    """Create a span for ghost wake up tracing."""
    return tracer.start_span(
        f"ghost_wake_up_{service_name}",
        attributes={
            "service.name": "ghost-function-service",
            "ghost.service": service_name,
            "ghost.hibernation_level": hibernation_level,
            "operation.type": "ghost_wake_up"
        }
    )


def trace_circuit_breaker_event(tracer: trace.Tracer, service_name: str, state: str):
    """Create a span for circuit breaker event tracing."""
    return tracer.start_span(
        f"circuit_breaker_{service_name}",
        attributes={
            "service.name": "ghost-function-service",
            "circuit_breaker.service": service_name,
            "circuit_breaker.state": state,
            "operation.type": "circuit_breaker"
        }
    )


# Global tracer instance for Ghost Function Service
ghost_tracer = get_ghost_tracer()
