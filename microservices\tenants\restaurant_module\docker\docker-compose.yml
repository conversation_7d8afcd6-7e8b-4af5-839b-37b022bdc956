# ============================================================================
# RESTAURANT SERVICE - Microserviço de Restaurante
# ============================================================================
# Porta: 8004 | Database: postgres-restaurant (5435) | Redis: DB 2
# Submódulos: menu, kds, delivery, table_management, recipes, my_pack, zones_sectors, tenant_settings
# ============================================================================

services:
  # ============================================================================
  # RESTAURANT SERVICE - Serviço Principal
  # ============================================================================
  trix-tenant-restaurant:
    build:
      context: ../
      dockerfile: docker/Dockerfile
    container_name: trix-tenant-restaurant
    ports:
      - "8004:8000"
    environment:
      - DATABASE_URL=postgresql+asyncpg://postgres:TrixSuperSecure2024!@trix-citus-tenant-coordinator:5432/restaurant_db
      - REDIS_URL=redis://:${REDIS_PASSWORD}@trix-redis:6379/2
      - AUTH_SERVICE_URL=http://trix-core-auth:8001
      - USER_SERVICE_URL=http://trix-core-user:8002
      - TENANT_SERVICE_URL=http://trix-core-tenant:8003
      - PRODUCT_SERVICE_URL=http://trix-infrastructure-products:8009
      - COMMERCE_SERVICE_URL=http://trix-infrastructure-commerce:8010
      - SECRET_KEY=${JWT_SECRET_KEY}
      - DEBUG=${DEBUG}
      - SERVICE_NAME=restaurant-service
      - SERVICE_VERSION=1.0.0
      - WEBSOCKET_ENABLED=true
      - QR_CODE_BASE_URL=${QR_CODE_BASE_URL}
      - PRINTER_ENABLED=true
      - KDS_ENABLED=true
    depends_on:
      - trix-redis
    external_links:
      - trix-citus-tenant-coordinator:trix-citus-tenant-coordinator
    volumes:
      - ../:/app
      - ../../../core/shared_lib:/app/microservices/core/shared_lib:ro
      - trix_restaurant_qr:/app/qr
      - trix_restaurant_receipts:/app/receipts
      - trix_restaurant_menus:/app/menus
    networks:
      - trix-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    labels:
      - "trix.category=tenant"
      - "trix.service=microservice"
      - "trix.module=restaurant"
      - "trix.port=8004"

  # ============================================================================
  # RESTAURANT DATABASE - Usando banco principal consolidado
  # ============================================================================
  # Banco removido - usando banco principal 'db' do docker-compose.yml raiz

networks:
  trix-network:
    external: true
