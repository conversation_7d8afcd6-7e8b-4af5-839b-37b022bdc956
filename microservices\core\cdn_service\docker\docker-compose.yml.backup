# ============================================================================
# CDN SERVICE - Docker Compose Configuration
# ============================================================================
# Porta: 8009 | Database: postgres-cdn (5449) | Redis: DB 9
# Tecnologias 100% Open-Source: Varnish, MinIO, Nginx, PostgreSQL, Redis
# ============================================================================

version: '3.8'

services:
  # ============================================================================
  # CDN SERVICE - Serviço Principal FastAPI
  # ============================================================================
  trix-cdn-service:
    build:
      context: ../
      dockerfile: docker/Dockerfile
    container_name: trix-cdn-service
    ports:
      - "8027:8000"
    environment:
      # Service Configuration
      - SERVICE_NAME=cdn-service
      - SERVICE_VERSION=1.0.0
      - SERVICE_PORT=8000
      - DEBUG=${DEBUG:-false}
      - ENVIRONMENT=${ENVIRONMENT:-production}
      
      # Database Configuration
      - DATABASE_URL=postgresql+asyncpg://${CDN_DB_USER:-cdn_user}:${CDN_DB_PASSWORD:-cdn_pass}@trix-db-cdn:5432/${CDN_DB_NAME:-cdn_db}
      
      # Redis Configuration
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis123}@trix-redis:6379/9
      
      # Varnish Configuration
      - VARNISH_HOST=trix-cdn-varnish
      - VARNISH_PORT=80
      - VARNISH_ADMIN_PORT=6082
      - VARNISH_SECRET=${VARNISH_SECRET:-varnish_secret_key}
      - VARNISH_CACHE_SIZE=1G
      - VARNISH_TTL_DEFAULT=3600
      - VARNISH_TTL_STATIC=86400
      - VARNISH_TTL_DYNAMIC=300
      
      # MinIO Configuration
      - MINIO_ENDPOINT=trix-cdn-minio:9000
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY:-trix_minio_access}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY:-trix_minio_secret_key_2024}
      - MINIO_SECURE=false
      
      # Performance Targets
      - CACHE_HIT_RATIO_TARGET=0.95
      - GLOBAL_LATENCY_TARGET=50
      - BANDWIDTH_SAVINGS_TARGET=0.80
      
      # Service Integrations
      - MEDIA_SERVICE_URL=http://trix-infrastructure-media:8007
      - AUTH_SERVICE_URL=http://trix-core-auth:8001
      
    depends_on:
      - trix-db-cdn
      - trix-cdn-varnish
      - trix-cdn-minio
      - trix-cdn-nginx
    networks:
      - trix-cdn-network
      - trix-main-network
    volumes:
      - cdn_logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # ============================================================================
  # VARNISH - HTTP Cache Accelerator
  # ============================================================================
  trix-cdn-varnish:
    image: varnish:7.4
    container_name: trix-cdn-varnish
    ports:
      - "8080:80"      # HTTP Cache
      - "6082:6082"    # Admin interface
    environment:
      - VARNISH_SIZE=1G
      - VARNISH_SECRET=${VARNISH_SECRET:-varnish_secret_key}
    volumes:
      - ./varnish/default.vcl:/etc/varnish/default.vcl:ro
      - ./varnish/error.html:/etc/varnish/error.html:ro
      - varnish_cache:/var/lib/varnish
    networks:
      - trix-cdn-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "varnishadm", "-S", "/etc/varnish/secret", "-T", "127.0.0.1:6082", "status"]
      interval: 30s
      timeout: 5s
      retries: 3
    command: >
      varnishd -F 
      -a :80 
      -T :6082 
      -f /etc/varnish/default.vcl 
      -S /etc/varnish/secret 
      -s malloc,1G

  # ============================================================================
  # MINIO - Object Storage
  # ============================================================================
  trix-cdn-minio:
    image: minio/minio:RELEASE.2024-01-16T16-07-38Z
    container_name: trix-cdn-minio
    ports:
      - "9000:9000"    # API
      - "9001:9001"    # Console
    environment:
      - MINIO_ROOT_USER=${MINIO_ACCESS_KEY:-trix_minio_access}
      - MINIO_ROOT_PASSWORD=${MINIO_SECRET_KEY:-trix_minio_secret_key_2024}
      - MINIO_BROWSER_REDIRECT_URL=http://localhost:9001
    volumes:
      - minio_data:/data
      - minio_config:/root/.minio
    networks:
      - trix-cdn-network
    restart: unless-stopped
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  # ============================================================================
  # NGINX - Load Balancer & Reverse Proxy
  # ============================================================================
  trix-cdn-nginx:
    image: nginx:1.25-alpine
    container_name: trix-cdn-nginx
    ports:
      - "8081:80"      # HTTP
      - "8443:443"     # HTTPS
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_cache:/var/cache/nginx
      - nginx_logs:/var/log/nginx
    networks:
      - trix-cdn-network
    restart: unless-stopped
    depends_on:
      - trix-cdn-varnish
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 5s
      retries: 3

  # ============================================================================
  # POSTGRESQL - CDN Database
  # ============================================================================
  trix-db-cdn:
    image: postgres:16-alpine
    container_name: trix-db-cdn
    ports:
      - "5449:5432"
    environment:
      - POSTGRES_DB=${CDN_DB_NAME:-cdn_db}
      - POSTGRES_USER=${CDN_DB_USER:-cdn_user}
      - POSTGRES_PASSWORD=${CDN_DB_PASSWORD:-cdn_pass}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - cdn_postgres_data:/var/lib/postgresql/data
      - ./postgres/init:/docker-entrypoint-initdb.d:ro
    networks:
      - trix-cdn-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${CDN_DB_USER:-cdn_user} -d ${CDN_DB_NAME:-cdn_db}"]
      interval: 30s
      timeout: 5s
      retries: 5
    command: >
      postgres
      -c shared_preload_libraries=pg_stat_statements
      -c pg_stat_statements.track=all
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100

  # ============================================================================
  # REDIS - Cache & Sessions (Shared)
  # ============================================================================
  trix-redis:
    image: redis:7-alpine
    container_name: trix-redis
    ports:
      - "6379:6379"
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD:-redis123}
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/etc/redis/redis.conf:ro
    networks:
      - trix-cdn-network
      - trix-main-network
    restart: unless-stopped
    command: redis-server /etc/redis/redis.conf --requirepass ${REDIS_PASSWORD:-redis123}
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 5s
      retries: 5

  # ============================================================================
  # PROMETHEUS - Metrics Collection
  # ============================================================================
  trix-cdn-prometheus:
    image: prom/prometheus:v2.48.0
    container_name: trix-cdn-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - trix-cdn-network
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=90d'
      - '--web.enable-lifecycle'

  # ============================================================================
  # GRAFANA - Metrics Visualization
  # ============================================================================
  trix-cdn-grafana:
    image: grafana/grafana:10.2.0
    container_name: trix-cdn-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - trix-cdn-network
    restart: unless-stopped
    depends_on:
      - trix-cdn-prometheus

# ============================================================================
# NETWORKS
# ============================================================================
networks:
  trix-cdn-network:
    driver: bridge
    name: trix-cdn-network
  trix-main-network:
    external: true
    name: trix-main-network

# ============================================================================
# VOLUMES
# ============================================================================
volumes:
  # CDN Service
  cdn_logs:
    name: trix-cdn-logs
  
  # Varnish
  varnish_cache:
    name: trix-varnish-cache
  
  # MinIO
  minio_data:
    name: trix-minio-data
  minio_config:
    name: trix-minio-config
  
  # Nginx
  nginx_cache:
    name: trix-nginx-cache
  nginx_logs:
    name: trix-nginx-logs
  
  # PostgreSQL
  cdn_postgres_data:
    name: trix-cdn-postgres-data
  
  # Redis
  redis_data:
    name: trix-redis-data
  
  # Monitoring
  prometheus_data:
    name: trix-prometheus-data
  grafana_data:
    name: trix-grafana-data
