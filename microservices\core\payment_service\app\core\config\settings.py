"""
Enterprise-grade configuration management with HashiCorp Vault integration.
Supports multi-environment deployment with secrets management.
"""

import os
from typing import List, Optional
from decimal import Decimal
from pydantic import Field
from pydantic_settings import BaseSettings
import sys
sys.path.append('/app')
from microservices.core.shared_lib.config.vault_config import StandardVaultConfig


class EnterpriseSettings(BaseSettings):
    """Enterprise-grade configuration with Vault integration."""

    # Service Identity
    SERVICE_NAME: str = "payment-service"
    SERVICE_VERSION: str = "2.0.0"
    ENVIRONMENT: str = Field(default="production")
    SERVICE_PORT: int = Field(default=8020)

    # Citus Data Configuration
    DATABASE_URL: str = Field(default="")  # Retrieved from Vault
    # Configurações do Vault herdadas de StandardVaultConfig
    # VAULT_URL, VAULT_TOKEN, VAULT_ENABLED, etc. j<PERSON> estão definidas
    
    CITUS_COORDINATOR_URL: str = Field(default="")    PGBOUNCER_URL: str = Field(default="")    DATABASE_POOL_SIZE: int = Field(default=20)
    DATABASE_MAX_OVERFLOW: int = Field(default=30)

    # Messaging Infrastructure
    KAFKA_BOOTSTRAP_SERVERS: str = Field(default="")    RABBITMQ_URL: str = Field(default="")    REDIS_CLUSTER_URL: str = Field(default="")    KAFKA_TOPIC_PREFIX: str = Field(default="payment")
    RABBITMQ_EXCHANGE: str = Field(default="payment-events")

    # Service Mesh & Security    OPA_URL: str = Field(default="http://opa:8181")
    ISTIO_ENABLED: bool = Field(default=True)
    MTLS_ENABLED: bool = Field(default=True)

    # Observability
    PROMETHEUS_GATEWAY: str = Field(default="http://prometheus-pushgateway:9091")
    JAEGER_AGENT_HOST: str = Field(default="jaeger-agent")
    JAEGER_AGENT_PORT: int = Field(default=6831)
    ELK_ENDPOINT: str = Field(default="http://elasticsearch:9200")
    METRICS_ENABLED: bool = Field(default=True)
    TRACING_ENABLED: bool = Field(default=True)

    # Performance & Scaling
    MAX_CONNECTIONS: int = Field(default=100)
    CONNECTION_POOL_SIZE: int = Field(default=20)
    WORKER_PROCESSES: int = Field(default=4)
    CACHE_TTL: int = Field(default=300)
    REQUEST_TIMEOUT: int = Field(default=30)
    RETRY_ATTEMPTS: int = Field(default=3)
    RETRY_BACKOFF_FACTOR: float = Field(default=2.0)

    # Payment Processing
    FRAUD_DETECTION_ENABLED: bool = Field(default=True)
    MAX_DAILY_AMOUNT: Decimal = Field(default=Decimal("50000.00"))
    MAX_TRANSACTION_AMOUNT: Decimal = Field(default=Decimal("10000.00"))
    PAYMENT_TIMEOUT_SECONDS: int = Field(default=120)
    WEBHOOK_TIMEOUT_SECONDS: int = Field(default=30)

    # Regional Configuration
    DEFAULT_REGION: str = Field(default="us-east-1")
    SUPPORTED_REGIONS: List[str] = Field(default=["us-east-1", "eu-west-1", "sa-east-1"])
    DEFAULT_CURRENCY: str = Field(default="BRL")
    SUPPORTED_CURRENCIES: List[str] = Field(default=["BRL", "USD", "EUR"])

    # Compliance & Security
    PCI_COMPLIANCE_LEVEL: str = Field(default="level-1")
    ENCRYPTION_ALGORITHM: str = Field(default="AES-256-GCM")
    TOKEN_EXPIRY_HOURS: int = Field(default=24)
    AUDIT_ENABLED: bool = Field(default=True)
    DATA_RETENTION_DAYS: int = Field(default=2555)  # 7 years for compliance

    # Service Discovery
    AUTH_SERVICE_URL: str = Field(default="http://trix-core-auth:8001")
    USER_SERVICE_URL: str = Field(default="http://trix-core-user:8002")
    TENANT_SERVICE_URL: str = Field(default="http://trix-core-tenant:8003")
    COMMERCE_SERVICE_URL: str = Field(default="http://trix-core-commerce:8010")
    NOTIFICATION_SERVICE_URL: str = Field(default="http://trix-core-notification:8019")

    # Gateway Configuration
    STRIPE_ENABLED: bool = Field(default=True)
    PAYPAL_ENABLED: bool = Field(default=True)
    PIX_ENABLED: bool = Field(default=True)
    BOLETO_ENABLED: bool = Field(default=True)
    MERCADOPAGO_ENABLED: bool = Field(default=True)

    # Rate Limiting
    RATE_LIMIT_ENABLED: bool = Field(default=True)
    RATE_LIMIT_REQUESTS: int = Field(default=1000)
    RATE_LIMIT_WINDOW: int = Field(default=3600)  # 1 hour

    # Kubernetes Integration
    KUBERNETES_NAMESPACE: str = Field(default="trix-payment")
    KUBERNETES_SERVICE_ACCOUNT: str = Field(default="payment-service")
    CONFIGMAP_NAME: str = Field(default="payment-config")
    SECRET_NAME: str = Field(default="payment-secrets")

    class Config:
        env_file = ".env"
        case_sensitive = True
        extra = "forbid"

    def get_database_url(self) -> str:
        """Get database URL with fallback to environment variable."""
        if self.DATABASE_URL:
            return self.DATABASE_URL
        return os.getenv("DATABASE_URL", "postgresql://payment:password@localhost:5432/payment_db")

    def get_redis_url(self) -> str:
        """Get Redis URL with fallback to environment variable."""
        if self.REDIS_CLUSTER_URL:
            return self.REDIS_CLUSTER_URL
        return os.getenv("REDIS_URL", "redis://localhost:6379/0")

    def get_kafka_config(self) -> dict:
        """Get Kafka configuration."""
        return {
            "bootstrap_servers": self.KAFKA_BOOTSTRAP_SERVERS.split(",") if self.KAFKA_BOOTSTRAP_SERVERS else ["localhost:9092"],
            "client_id": f"{self.SERVICE_NAME}-{self.ENVIRONMENT}",
            "group_id": f"{self.SERVICE_NAME}-consumer-group",
            "auto_offset_reset": "earliest",
            "enable_auto_commit": True,
            "security_protocol": "SSL" if self.MTLS_ENABLED else "PLAINTEXT"
        }

    def get_rabbitmq_config(self) -> dict:
        """Get RabbitMQ configuration."""
        return {
            "url": self.RABBITMQ_URL or "amqp://guest:guest@localhost:5672/",
            "exchange": self.RABBITMQ_EXCHANGE,
            "exchange_type": "topic",
            "durable": True,
            "auto_delete": False
        }

    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.ENVIRONMENT.lower() == "production"

    def is_development(self) -> bool:
        """Check if running in development environment."""
        return self.ENVIRONMENT.lower() in ["development", "dev", "local"]


# Global settings instance
settings = EnterpriseSettings()
