"""
Settings Module - Core Service Enterprise Edition
================================================
Centralized configuration settings for Core Service.
"""

import os
from typing import Optional, Dict, Any
from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict
import sys
sys.path.append('/app')
from microservices.core.shared_lib.config.vault_config import VaultBaseSettings



class Settings(BaseSettings):
    """Enterprise-grade configuration for Core Service with Vault integration."""

    # Service configuration
    SERVICE_NAME: str = "core-service"
    SERVICE_VERSION: str = "2.0.0"
    DEBUG: bool = Field(default=False, env="DEBUG")
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    ENVIRONMENT: str = Field(default="development", env="ENVIRONMENT")

    # Server configuration
    HOST: str = Field(default="0.0.0.0", env="HOST")
    PORT: int = Field(default=8000, env="PORT")

    # Database configuration - Citus Data Cluster
    DATABASE_URL: str = Field(
    # Configurações do Vault herdadas de StandardVaultConfig
    # VAULT_URL, VAULT_TOKEN, VAULT_ENABLED, etc. já estão definidas
    
        default="postgresql+asyncpg://core_user:core_pass@trix-citus-coordinator:5432/core_db",
        env="DATABASE_URL"
    )
    DATABASE_POOL_SIZE: int = Field(default=20, env="DATABASE_POOL_SIZE")
    DATABASE_MAX_OVERFLOW: int = Field(default=30, env="DATABASE_MAX_OVERFLOW")
    DATABASE_POOL_TIMEOUT: int = Field(default=30, env="DATABASE_POOL_TIMEOUT")
    DATABASE_POOL_RECYCLE: int = Field(default=3600, env="DATABASE_POOL_RECYCLE")

    # Redis configuration
    REDIS_URL: str = Field(default="redis://trix-redis:6379/0", env="REDIS_URL")
    REDIS_POOL_SIZE: int = Field(default=10, env="REDIS_POOL_SIZE")
    REDIS_TIMEOUT: int = Field(default=5, env="REDIS_TIMEOUT")

    # Messaging - Apache Kafka
    KAFKA_ENABLED: bool = Field(default=True, env="KAFKA_ENABLED")
    KAFKA_BOOTSTRAP_SERVERS: str = Field(default="trix-kafka:9092", env="KAFKA_BOOTSTRAP_SERVERS")
    KAFKA_TOPIC_PREFIX: str = Field(default="core-service", env="KAFKA_TOPIC_PREFIX")

    # Messaging - RabbitMQ
    RABBITMQ_ENABLED: bool = Field(default=True, env="RABBITMQ_ENABLED")
    RABBITMQ_URL: str = Field(default="amqp://guest:guest@trix-rabbitmq:5672/", env="RABBITMQ_URL")

    # Messaging - Redis Streams
    REDIS_STREAMS_ENABLED: bool = Field(default=True, env="REDIS_STREAMS_ENABLED")

    # Observability - Prometheus
    PROMETHEUS_ENABLED: bool = Field(default=True, env="PROMETHEUS_ENABLED")
    PROMETHEUS_PORT: int = Field(default=9090, env="PROMETHEUS_PORT")

    # Observability - Jaeger
    JAEGER_ENABLED: bool = Field(default=True, env="JAEGER_ENABLED")
    JAEGER_AGENT_HOST: str = Field(default="trix-jaeger", env="JAEGER_AGENT_HOST")
    JAEGER_AGENT_PORT: int = Field(default=6831, env="JAEGER_AGENT_PORT")

    # Observability - Elasticsearch (ELK Stack)
    ELASTICSEARCH_ENABLED: bool = Field(default=True, env="ELASTICSEARCH_ENABLED")
    ELASTICSEARCH_URL: str = Field(default="http://trix-elasticsearch:9200", env="ELASTICSEARCH_URL")
    ELASTICSEARCH_INDEX_PREFIX: str = Field(default="core-service", env="ELASTICSEARCH_INDEX_PREFIX")

    # Configurações do Vault herdadas de StandardVaultConfig
    # VAULT_ENABLED, VAULT_URL, VAULT_TOKEN, etc. já estão definidas

    # OPA Policy Engine
    OPA_ENABLED: bool = Field(default=True, env="OPA_ENABLED")
    OPA_URL: str = Field(default="http://trix-opa:8181", env="OPA_URL")

    # JWT Configuration
    JWT_SECRET_KEY: str = Field(default="core-service-secret-key", env="JWT_SECRET_KEY")
    JWT_ALGORITHM: str = Field(default="HS256", env="JWT_ALGORITHM")
    JWT_EXPIRE_MINUTES: int = Field(default=30, env="JWT_EXPIRE_MINUTES")

    # Feature Flags
    FEATURE_AUDIT_ENABLED: bool = Field(default=True, env="FEATURE_AUDIT_ENABLED")
    FEATURE_ESHOP_ENABLED: bool = Field(default=True, env="FEATURE_ESHOP_ENABLED")
    FEATURE_HELP_CENTER_ENABLED: bool = Field(default=True, env="FEATURE_HELP_CENTER_ENABLED")
    FEATURE_REAL_TIME_EVENTS: bool = Field(default=True, env="FEATURE_REAL_TIME_EVENTS")
    FEATURE_ADVANCED_ANALYTICS: bool = Field(default=True, env="FEATURE_ADVANCED_ANALYTICS")

    # Service URLs (from Service Discovery)
    AUTH_SERVICE_URL: str = Field(default="http://trix-core-auth:8001", env="AUTH_SERVICE_URL")
    USER_SERVICE_URL: str = Field(default="http://trix-core-user:8002", env="USER_SERVICE_URL")
    TENANT_SERVICE_URL: str = Field(default="http://trix-core-tenant:8003", env="TENANT_SERVICE_URL")

    # Celery Configuration
    CELERY_BROKER_URL: str = Field(default="redis://redis:6379/0", env="CELERY_BROKER_URL")
    CELERY_RESULT_BACKEND: str = Field(default="redis://redis:6379/1", env="CELERY_RESULT_BACKEND")
    CELERY_WORKER_CONCURRENCY: int = Field(default=2, env="CELERY_WORKER_CONCURRENCY")

    # Optional External Service Configurations (to avoid extra_forbidden errors)
    letsencrypt_email: Optional[str] = Field(default=None, env="LETSENCRYPT_EMAIL")
    cloudflare_api_token: Optional[str] = Field(default=None, env="CLOUDFLARE_API_TOKEN")
    fcm_server_key: Optional[str] = Field(default=None, env="FCM_SERVER_KEY")
    qr_code_base_url: Optional[str] = Field(default=None, env="QR_CODE_BASE_URL")
    resellerclub_api_key: Optional[str] = Field(default=None, env="RESELLERCLUB_API_KEY")

    model_config = SettingsConfigDict(
        extra="ignore"  # Ignore extra fields instead of raising error
    )




# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get settings instance."""
    return settings
