"""
Ghost Function Service - Service URLs Configuration

Configurações de URLs dos serviços que o Ghost Function Service monitora e faz proxy.
Centraliza todas as URLs de serviços para facilitar manutenção.

Author: Trix Platform Team
"""

import os
from typing import Dict, Optional
from .settings import get_settings


class ServiceURLsConfig:
    """Configuration for service URLs that Ghost Function monitors."""
    
    def __init__(self):
        self.settings = get_settings()
        self._service_urls = self._load_service_urls()
    
    def _load_service_urls(self) -> Dict[str, str]:
        """Load service URLs from environment variables or defaults."""
        return {
            # Core Services
            "auth_service": os.getenv(
                "AUTH_SERVICE_URL", 
                "http://auth-service.trix.svc.cluster.local:8001"
            ),
            "user_service": os.getenv(
                "USER_SERVICE_URL", 
                "http://user-service.trix.svc.cluster.local:8002"
            ),
            "tenant_service": os.getenv(
                "TENANT_SERVICE_URL", 
                "http://tenant-service.trix.svc.cluster.local:8003"
            ),
            "core_service": os.getenv(
                "CORE_SERVICE_URL", 
                "http://core-service.trix.svc.cluster.local:8004"
            ),
            
            # Infrastructure Services
            "notification_service": os.getenv(
                "NOTIFICATION_SERVICE_URL", 
                "http://notification-service.trix.svc.cluster.local:8019"
            ),
            "media_system": os.getenv(
                "MEDIA_SYSTEM_URL", 
                "http://media-system.trix.svc.cluster.local:8020"
            ),
            "cdn_service": os.getenv(
                "CDN_SERVICE_URL", 
                "http://cdn-service.trix.svc.cluster.local:8021"
            ),
            "i18n_service": os.getenv(
                "I18N_SERVICE_URL", 
                "http://i18n-service.trix.svc.cluster.local:8022"
            ),
            "dns_bridge_service": os.getenv(
                "DNS_BRIDGE_SERVICE_URL", 
                "http://dns-bridge-service.trix.svc.cluster.local:8023"
            ),
            
            # Database Cluster
            "trix_database_cluster": os.getenv(
                "TRIX_DATABASE_CLUSTER_URL", 
                "http://trix-database-cluster.trix.svc.cluster.local:5432"
            )
        }
    
    def get_service_url(self, service_name: str) -> Optional[str]:
        """Get URL for a specific service."""
        return self._service_urls.get(service_name)
    
    def get_all_service_urls(self) -> Dict[str, str]:
        """Get all service URLs."""
        return self._service_urls.copy()
    
    def is_service_configured(self, service_name: str) -> bool:
        """Check if a service is configured."""
        return service_name in self._service_urls
    
    def get_proxy_url(self, service_name: str, endpoint: str = "") -> Optional[str]:
        """Get the full proxy URL for a service endpoint."""
        base_url = self.get_service_url(service_name)
        if not base_url:
            return None
        
        # Remove leading slash from endpoint if present
        if endpoint.startswith("/"):
            endpoint = endpoint[1:]
        
        # Construct full URL
        if endpoint:
            return f"{base_url}/{endpoint}"
        return base_url
    
    def get_health_check_url(self, service_name: str) -> Optional[str]:
        """Get health check URL for a service."""
        return self.get_proxy_url(service_name, "health")


# Global service URLs configuration instance
service_urls_config = ServiceURLsConfig()


def get_service_urls_config() -> ServiceURLsConfig:
    """Get the service URLs configuration instance."""
    return service_urls_config
