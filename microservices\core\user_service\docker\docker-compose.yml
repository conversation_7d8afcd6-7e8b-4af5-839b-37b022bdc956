# ============================================================================
# USER SERVICE - Enterprise Microservice v2.0
# ============================================================================
# Porta: 8002 | Database: postgres-users (5433) | Redis: DB 1
# Submódulos: terms_rules, roles, User Management, User Associations
# Enterprise Features: Event-Driven, Caching, Rate Limiting, Observability
# Target Scale: Bilhões de usuários simultâneos
# ============================================================================

# Importa variáveis globais do arquivo principal
x-environment: &global-environment
  SECRET_KEY: ${SECRET_KEY}
  DEBUG: ${DEBUG}
  REDIS_PASSWORD: ${REDIS_PASSWORD}
  JWT_SECRET_KEY: ${JWT_SECRET_KEY}
  USERS_DB_NAME: users_db
  USERS_DB_USER: postgres
  USERS_DB_PASSWORD: TrixSuperSecure2024!
  USERS_DB_HOST: trix-citus-core-coordinator
  USERS_DB_PORT: 5432

  # Enterprise Messaging
  KAFKA_BOOTSTRAP_SERVERS: ${KAFKA_BOOTSTRAP_SERVERS:-trix-kafka:9092}
  RABBITMQ_URL: ${RABBITMQ_URL:-amqp://guest:guest@trix-rabbitmq:5672/}

  # Enterprise Security
  VAULT_URL: ${VAULT_URL:-http://trix-vault:8200}
  VAULT_TOKEN: ${VAULT_TOKEN:-dev-token}

  # Enterprise Observability
  JAEGER_AGENT_HOST: ${JAEGER_AGENT_HOST:-trix-jaeger}
  JAEGER_AGENT_PORT: ${JAEGER_AGENT_PORT:-6831}
  PROMETHEUS_GATEWAY: ${PROMETHEUS_GATEWAY:-trix-prometheus:9091}

services:
  # ============================================================================
  # USER SERVICE - Enterprise Microservice
  # ============================================================================
  trix-core-user:
    build:
      context: ../
      dockerfile: docker/Dockerfile
    container_name: trix-core-user
    ports:
      - "8002:8000"
    environment:
      <<: *global-environment

      # Database Configuration (Citus Core Coordinator)
      DATABASE_URL: postgresql+asyncpg://postgres:TrixSuperSecure2024!@trix-citus-core-coordinator:5432/users_db

      # Redis Configuration (Multi-DB)
      REDIS_URL: redis://:${REDIS_PASSWORD}@trix-redis:6379/1
      REDIS_CACHE_DB: 2
      REDIS_RATE_LIMIT_DB: 3
      REDIS_SESSIONS_DB: 4

      # Service Configuration
      SERVICE_NAME: user-service
      SERVICE_VERSION: 2.0.0
      ENVIRONMENT: ${ENVIRONMENT:-development}

      # Inter-Service Communication
      AUTH_SERVICE_URL: http://trix-core-auth:8001
      TENANT_SERVICE_URL: http://trix-core-tenant:8003
      NOTIFICATION_SERVICE_URL: http://trix-core-notification:8019

      # Enterprise Features
      ENABLE_CACHING: true
      ENABLE_RATE_LIMITING: true
      ENABLE_TRACING: true
      ENABLE_METRICS: true
      ENABLE_EVENT_DRIVEN: true

    depends_on:
      - trix-redis
    external_links:
      - trix-citus-core-coordinator:trix-citus-core-coordinator

    # Health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

    # Resource limits for enterprise scale
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 512M

    # Restart policy
    restart: unless-stopped

  # ============================================================================
  # PGBOUNCER - Enterprise Connection Pooling
  # ============================================================================
  trix-user-pgbouncer:
    image: pgbouncer/pgbouncer:latest
    container_name: trix-user-pgbouncer
    ports:
      - "6432:6432"
    volumes:
      - ./pgbouncer.ini:/etc/pgbouncer/pgbouncer.ini:ro
      - ./userlist.txt:/etc/pgbouncer/userlist.txt:ro
    environment:
      DATABASES_HOST: db
      DATABASES_PORT: 5432
      DATABASES_USER: trixuser
      DATABASES_PASSWORD: trixpass
      DATABASES_DBNAME: trixdb
      POOL_MODE: transaction
      MAX_CLIENT_CONN: 1000
      DEFAULT_POOL_SIZE: 25
      AUTH_TYPE: md5
    external_links:
      - trix-citus-core-coordinator:trix-citus-core-coordinator
    networks:
      - trix-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "psql", "-h", "localhost", "-p", "6432", "-U", "trixuser", "-d", "trixdb", "-c", "SELECT 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ============================================================================
  # USER DATABASE - Usando banco principal consolidado
  # ============================================================================
  # Banco removido - usando banco principal 'db' do docker-compose.yml raiz

networks:
  trix-network:
    external: true
