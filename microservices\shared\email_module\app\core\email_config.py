"""Email-specific configuration settings for Email Service.

🔗 MIGRAÇÃO PARA SHARED_LIB: Este arquivo agora utiliza configurações
compartilhadas da shared_lib para evitar duplicação e garantir consistência
entre microserviços. Configurações comuns foram movidas para:
microservices/core/shared_lib/config/email_config.py

Apenas configurações específicas do Email Service permanecem aqui.
"""

import os
from typing import List, Optional
from pydantic_settings import BaseSettings

# ✅ NOVO: Importando configurações compartilhadas da shared_lib
try:
    from microservices.core.shared_lib.config.email_config import (
        shared_email_settings,
        get_email_settings,
        get_smtp_config,
        get_imap_config,
        get_provider_config,
        EMAIL_PROVIDERS
    )
    SHARED_EMAIL_CONFIG_AVAILABLE = True
except ImportError:
    # Fallback para desenvolvimento local
    SHARED_EMAIL_CONFIG_AVAILABLE = False
    print("⚠️  Shared email config not available, using local config")


class EmailSettings(BaseSettings):
    """Email-specific settings."""
    
    # Application
    app_name: str = "email-service"
    app_version: str = "1.0.0"
    debug: bool = False
    port: int = 8012
    
    # Database
    database_url: str = "postgresql+asyncpg://postgres:password@localhost:5445/email_db"
    
    # Redis
    redis_url: str = "redis://localhost:6379/12"
    
    # Security
    secret_key: str = "your-secret-key-here"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # CORS
    allowed_origins: List[str] = ["http://localhost:3000", "http://localhost:8000"]
    
    # Logging
    log_level: str = "INFO"
    
    # Microservices Communication
    auth_service_url: str = "http://auth-service:8001"
    user_service_url: str = "http://user-service:8002"
    tenant_service_url: str = "http://tenant-service:8003"
    crm_service_url: str = "http://crm-service:8011"

    # ✅ MIGRADO: Email provider configurations moved to shared_lib
    # Use get_provider_config() or shared_email_settings.providers instead
    
    # ✅ MIGRADO: File storage, email delivery, analytics, template, and rate limiting
    # configurations moved to shared_lib. Use shared_email_settings instead:
    # - shared_email_settings.storage for file storage
    # - shared_email_settings.delivery for email delivery
    # - shared_email_settings.templates for template config

    # Email Service Specific Configuration (not shared)
    webmail_enabled: bool = True
    financial_integration_enabled: bool = True
    auto_invoice_processing: bool = True
    email_archiving_enabled: bool = True

    # Email Service Specific Features
    enable_email_forwarding: bool = True
    enable_email_aliases: bool = True
    enable_auto_responder: bool = True
    enable_spam_filtering: bool = True

    # Celery Configuration (Email Service specific)
    celery_broker_url: str = "redis://redis:6379/12"
    celery_result_backend: str = "redis://redis:6379/12"

    class Config:
        env_file = ".env"
        case_sensitive = False

    def get_shared_email_config(self):
        """Get shared email configuration from shared_lib."""
        if SHARED_EMAIL_CONFIG_AVAILABLE:
            return get_email_settings()
        return None

    def get_smtp_config(self):
        """Get SMTP configuration from shared_lib or fallback."""
        if SHARED_EMAIL_CONFIG_AVAILABLE:
            return get_smtp_config()
        # Fallback configuration
        return {
            "host": "localhost",
            "port": 587,
            "use_tls": True,
            "username": None,
            "password": None
        }

    def get_provider_config(self, provider: str):
        """Get email provider configuration from shared_lib or fallback."""
        if SHARED_EMAIL_CONFIG_AVAILABLE:
            return get_provider_config(provider)
        # Fallback configuration
        return {
            "sendgrid": {"api_key": None, "from_email": "<EMAIL>"},
            "ses": {"access_key_id": None, "secret_access_key": None, "region": "us-east-1"},
            "mailgun": {"api_key": None, "domain": None},
            "smtp": self.get_smtp_config()
        }.get(provider, {})


# Global settings instance
email_settings = EmailSettings()


def get_email_service_settings() -> EmailSettings:
    """Get the email service specific settings."""
    return email_settings


def get_combined_email_config():
    """
    Get combined email configuration from both shared_lib and email service.

    Returns a dictionary with all email configurations:
    - Shared configurations from shared_lib
    - Email service specific configurations
    """
    config = {
        "service": {
            "name": email_settings.app_name,
            "version": email_settings.app_version,
            "port": email_settings.port,
            "debug": email_settings.debug,
        },
        "database": {
            "url": email_settings.database_url,
        },
        "redis": {
            "url": email_settings.redis_url,
        },
        "features": {
            "webmail_enabled": email_settings.webmail_enabled,
            "financial_integration_enabled": email_settings.financial_integration_enabled,
            "auto_invoice_processing": email_settings.auto_invoice_processing,
            "email_archiving_enabled": email_settings.email_archiving_enabled,
            "enable_email_forwarding": email_settings.enable_email_forwarding,
            "enable_email_aliases": email_settings.enable_email_aliases,
            "enable_auto_responder": email_settings.enable_auto_responder,
            "enable_spam_filtering": email_settings.enable_spam_filtering,
        },
        "celery": {
            "broker_url": email_settings.celery_broker_url,
            "result_backend": email_settings.celery_result_backend,
        }
    }

    # Add shared configurations if available
    if SHARED_EMAIL_CONFIG_AVAILABLE:
        shared_config = get_email_settings()
        config.update({
            "shared": {
                "providers": shared_config.providers.dict(),
                "smtp": shared_config.smtp.dict(),
                "imap": shared_config.imap.dict(),
                "security": shared_config.security.dict(),
                "storage": shared_config.storage.dict(),
                "delivery": shared_config.delivery.dict(),
                "templates": shared_config.templates.dict(),
            }
        })

    return config
