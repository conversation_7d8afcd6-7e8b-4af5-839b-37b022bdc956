"""
Seed para Supplier Service - Sistema de Fornecedores e TVendors
==============================================================

Cria dados iniciais para o sistema de fornecedores, incluindo:
- Configurações de fornecedores padrão
- TVendor marketplace settings
- Purchase order templates
- Supplier categories e tipos
- Configurações de comissões e taxas
"""

import asyncio
import logging
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Any
from uuid import uuid4

from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from distributed_base import MicroserviceSeedModule

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SupplierSeed(MicroserviceSeedModule):
    """Seed para o Supplier Service."""

    def __init__(self):
        super().__init__(
            microservice_name="suppliers",
            module_name="suppliers"
        )

    async def check_existing_data(self) -> bool:
        """Check if supplier data already exists."""
        session = await self.get_database_session()
        try:
            # Check if supplier categories exist
            result = await session.execute(
                text("SELECT COUNT(*) FROM supplier_categories")
            )
            count = result.scalar()
            return count > 0
        except Exception:
            # If table doesn't exist, data doesn't exist
            return False
        finally:
            await session.close()

    async def create_data(self) -> None:
        """Create seed data for suppliers."""
        session = await self.get_database_session()
        try:
            await self.create_supplier_categories(session)
            await self.create_tvendor_settings(session)
            await self.create_purchase_order_templates(session)
            await self.create_supplier_performance_metrics(session)
            await session.commit()
        except Exception as e:
            await session.rollback()
            raise
        finally:
            await session.close()
    
    async def create_supplier_categories(self, session: AsyncSession) -> None:
        """Criar categorias padrão de fornecedores."""
        logger.info("🏷️ Criando categorias de fornecedores...")
        
        categories = [
            {
                'id': str(uuid4()),
                'name': 'Food & Beverages',
                'description': 'Fornecedores de alimentos e bebidas',
                'code': 'FOOD_BEV',
                'is_active': True,
                'commission_rate': Decimal('2.5'),
                'created_at': datetime.utcnow()
            },
            {
                'id': str(uuid4()),
                'name': 'Technology',
                'description': 'Fornecedores de tecnologia e equipamentos',
                'code': 'TECH',
                'is_active': True,
                'commission_rate': Decimal('5.0'),
                'created_at': datetime.utcnow()
            },
            {
                'id': str(uuid4()),
                'name': 'Services',
                'description': 'Fornecedores de serviços',
                'code': 'SERVICES',
                'is_active': True,
                'commission_rate': Decimal('3.0'),
                'created_at': datetime.utcnow()
            },
            {
                'id': str(uuid4()),
                'name': 'Office Supplies',
                'description': 'Fornecedores de material de escritório',
                'code': 'OFFICE',
                'is_active': True,
                'commission_rate': Decimal('1.5'),
                'created_at': datetime.utcnow()
            }
        ]
        
        for category in categories:
            await session.execute(
                text("""
                    INSERT INTO supplier_categories 
                    (id, name, description, code, is_active, commission_rate, created_at)
                    VALUES (:id, :name, :description, :code, :is_active, :commission_rate, :created_at)
                    ON CONFLICT (code) DO NOTHING
                """),
                category
            )
        
        await session.commit()
        logger.info(f"✅ Criadas {len(categories)} categorias de fornecedores")
    
    async def create_tvendor_settings(self, session: AsyncSession) -> None:
        """Criar configurações do TVendor marketplace."""
        logger.info("🏪 Criando configurações do TVendor marketplace...")
        
        settings = [
            {
                'id': str(uuid4()),
                'key': 'tvendor_application_fee',
                'value': '100.00',
                'description': 'Taxa de aplicação para TVendor',
                'data_type': 'decimal',
                'is_active': True,
                'created_at': datetime.utcnow()
            },
            {
                'id': str(uuid4()),
                'key': 'tvendor_monthly_fee',
                'value': '50.00',
                'description': 'Taxa mensal para TVendor',
                'data_type': 'decimal',
                'is_active': True,
                'created_at': datetime.utcnow()
            },
            {
                'id': str(uuid4()),
                'key': 'tvendor_commission_rate',
                'value': '5.0',
                'description': 'Taxa de comissão padrão TVendor (%)',
                'data_type': 'decimal',
                'is_active': True,
                'created_at': datetime.utcnow()
            },
            {
                'id': str(uuid4()),
                'key': 'auto_approve_suppliers',
                'value': 'false',
                'description': 'Aprovação automática de fornecedores',
                'data_type': 'boolean',
                'is_active': True,
                'created_at': datetime.utcnow()
            },
            {
                'id': str(uuid4()),
                'key': 'max_suppliers_per_tenant',
                'value': '100',
                'description': 'Máximo de fornecedores por tenant',
                'data_type': 'integer',
                'is_active': True,
                'created_at': datetime.utcnow()
            }
        ]
        
        for setting in settings:
            await session.execute(
                text("""
                    INSERT INTO supplier_settings 
                    (id, key, value, description, data_type, is_active, created_at)
                    VALUES (:id, :key, :value, :description, :data_type, :is_active, :created_at)
                    ON CONFLICT (key) DO UPDATE SET
                        value = EXCLUDED.value,
                        description = EXCLUDED.description,
                        updated_at = NOW()
                """),
                setting
            )
        
        await session.commit()
        logger.info(f"✅ Criadas {len(settings)} configurações do TVendor")
    
    async def create_purchase_order_templates(self, session: AsyncSession) -> None:
        """Criar templates de purchase orders."""
        logger.info("📋 Criando templates de purchase orders...")
        
        templates = [
            {
                'id': str(uuid4()),
                'name': 'Standard Purchase Order',
                'description': 'Template padrão para pedidos de compra',
                'template_type': 'STANDARD',
                'auto_approval_limit': Decimal('1000.00'),
                'requires_approval': True,
                'approval_workflow': 'MANAGER_APPROVAL',
                'is_active': True,
                'created_at': datetime.utcnow()
            },
            {
                'id': str(uuid4()),
                'name': 'Emergency Purchase Order',
                'description': 'Template para pedidos de emergência',
                'template_type': 'EMERGENCY',
                'auto_approval_limit': Decimal('500.00'),
                'requires_approval': True,
                'approval_workflow': 'IMMEDIATE_APPROVAL',
                'is_active': True,
                'created_at': datetime.utcnow()
            },
            {
                'id': str(uuid4()),
                'name': 'Recurring Purchase Order',
                'description': 'Template para pedidos recorrentes',
                'template_type': 'RECURRING',
                'auto_approval_limit': Decimal('2000.00'),
                'requires_approval': False,
                'approval_workflow': 'AUTO_APPROVAL',
                'is_active': True,
                'created_at': datetime.utcnow()
            }
        ]
        
        for template in templates:
            await session.execute(
                text("""
                    INSERT INTO purchase_order_templates 
                    (id, name, description, template_type, auto_approval_limit, 
                     requires_approval, approval_workflow, is_active, created_at)
                    VALUES (:id, :name, :description, :template_type, :auto_approval_limit,
                            :requires_approval, :approval_workflow, :is_active, :created_at)
                    ON CONFLICT (template_type) DO NOTHING
                """),
                template
            )
        
        await session.commit()
        logger.info(f"✅ Criados {len(templates)} templates de purchase orders")
    
    async def create_supplier_performance_metrics(self, session: AsyncSession) -> None:
        """Criar métricas de performance de fornecedores."""
        logger.info("📊 Criando métricas de performance...")
        
        metrics = [
            {
                'id': str(uuid4()),
                'metric_name': 'delivery_time_score',
                'display_name': 'Pontuação de Tempo de Entrega',
                'description': 'Score baseado na pontualidade das entregas',
                'weight': Decimal('30.0'),
                'min_value': Decimal('0.0'),
                'max_value': Decimal('100.0'),
                'is_active': True,
                'created_at': datetime.utcnow()
            },
            {
                'id': str(uuid4()),
                'metric_name': 'quality_score',
                'display_name': 'Pontuação de Qualidade',
                'description': 'Score baseado na qualidade dos produtos',
                'weight': Decimal('40.0'),
                'min_value': Decimal('0.0'),
                'max_value': Decimal('100.0'),
                'is_active': True,
                'created_at': datetime.utcnow()
            },
            {
                'id': str(uuid4()),
                'metric_name': 'price_competitiveness',
                'display_name': 'Competitividade de Preços',
                'description': 'Score baseado na competitividade dos preços',
                'weight': Decimal('20.0'),
                'min_value': Decimal('0.0'),
                'max_value': Decimal('100.0'),
                'is_active': True,
                'created_at': datetime.utcnow()
            },
            {
                'id': str(uuid4()),
                'metric_name': 'communication_score',
                'display_name': 'Pontuação de Comunicação',
                'description': 'Score baseado na qualidade da comunicação',
                'weight': Decimal('10.0'),
                'min_value': Decimal('0.0'),
                'max_value': Decimal('100.0'),
                'is_active': True,
                'created_at': datetime.utcnow()
            }
        ]
        
        for metric in metrics:
            await session.execute(
                text("""
                    INSERT INTO supplier_performance_metrics 
                    (id, metric_name, display_name, description, weight, 
                     min_value, max_value, is_active, created_at)
                    VALUES (:id, :metric_name, :display_name, :description, :weight,
                            :min_value, :max_value, :is_active, :created_at)
                    ON CONFLICT (metric_name) DO NOTHING
                """),
                metric
            )
        
        await session.commit()
        logger.info(f"✅ Criadas {len(metrics)} métricas de performance")
    
    async def run_seed(self) -> Dict[str, Any]:
        """Executar seed completo do Supplier Service."""
        self.logger.info("🏭 Iniciando seed do Supplier Service...")

        session = await self.get_database_session()

        try:
            # Criar estruturas básicas
            await self.create_supplier_categories(session)
            await self.create_tvendor_settings(session)
            await self.create_purchase_order_templates(session)
            await self.create_supplier_performance_metrics(session)

            await session.commit()
            self.logger.info("✅ Seed do Supplier Service concluído com sucesso!")

            return {
                'status': 'success',
                'message': 'Supplier Service seed completed successfully',
                'categories_created': 4,
                'settings_created': 5,
                'templates_created': 3,
                'metrics_created': 4,
                'timestamp': datetime.utcnow().isoformat()
            }

        except Exception as e:
            self.logger.error(f"❌ Erro no seed do Supplier Service: {str(e)}")
            await session.rollback()
            raise
        finally:
            await session.close()


# Função principal para execução do seed
async def seed() -> Dict[str, Any]:
    """Função principal para executar o seed do Supplier Service."""
    supplier_seed = SupplierSeed()
    success = await supplier_seed.run()

    return {
        'status': 'success' if success else 'failed',
        'message': 'Supplier Service seed completed' if success else 'Supplier Service seed failed',
        'categories_created': 4 if success else 0,
        'settings_created': 5 if success else 0,
        'templates_created': 3 if success else 0,
        'metrics_created': 4 if success else 0,
        'timestamp': datetime.utcnow().isoformat()
    }


if __name__ == "__main__":
    # Executar seed diretamente
    asyncio.run(seed())
