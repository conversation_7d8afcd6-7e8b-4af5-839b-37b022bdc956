"""
Example usage of shared email configuration.

This file demonstrates how to use the shared email configuration
across different microservices in the Trix platform.

🔗 Integration: Shows how email_module, notification_service, crm_module,
and other services can use the same email configuration.
"""

from typing import Dict, Any
import asyncio

# Import shared email configuration
from microservices.core.shared_lib.config.email_config import (
    shared_email_settings,
    get_email_settings,
    get_smtp_config,
    get_imap_config,
    get_provider_config,
    get_dns_records,
    EMAIL_PROVIDERS,
    DEFAULT_EMAIL_TEMPLATES
)


def example_basic_usage():
    """Basic usage of shared email configuration."""
    
    print("=== Basic Email Configuration Usage ===")
    
    # Get the global email settings
    email_settings = get_email_settings()
    print(f"Service: {email_settings.service_name} v{email_settings.service_version}")
    print(f"Environment: {email_settings.environment}")
    print(f"Default Provider: {email_settings.providers.default_provider}")
    
    # Get SMTP configuration
    smtp_config = get_smtp_config()
    print(f"\nSMTP Config:")
    print(f"  Host: {smtp_config['host']}:{smtp_config['port']}")
    print(f"  TLS: {smtp_config['use_tls']}")
    print(f"  Max Connections: {smtp_config['max_connections']}")
    
    # Get IMAP configuration
    imap_config = get_imap_config()
    print(f"\nIMAP Config:")
    print(f"  Host: {imap_config['host']}:{imap_config['port']}")
    print(f"  SSL: {imap_config['use_ssl']}")
    print(f"  STARTTLS: {imap_config['use_starttls']}")


def example_provider_usage():
    """Example of using different email providers."""
    
    print("\n=== Email Provider Configuration Usage ===")
    
    # Available providers
    print(f"Available Providers: {list(EMAIL_PROVIDERS.keys())}")
    
    # Get SendGrid configuration
    try:
        sendgrid_config = get_provider_config("sendgrid")
        print(f"\nSendGrid Config:")
        print(f"  From Email: {sendgrid_config['from_email']}")
        print(f"  From Name: {sendgrid_config['from_name']}")
        print(f"  API Key: {'***' if sendgrid_config['api_key'] else 'Not Set'}")
    except Exception as e:
        print(f"SendGrid config error: {e}")
    
    # Get AWS SES configuration
    try:
        ses_config = get_provider_config("ses")
        print(f"\nAWS SES Config:")
        print(f"  Region: {ses_config['region']}")
        print(f"  From Email: {ses_config['from_email']}")
        print(f"  Access Key: {'***' if ses_config['access_key_id'] else 'Not Set'}")
    except Exception as e:
        print(f"SES config error: {e}")
    
    # Get Mailgun configuration
    try:
        mailgun_config = get_provider_config("mailgun")
        print(f"\nMailgun Config:")
        print(f"  Domain: {mailgun_config['domain']}")
        print(f"  From Email: {mailgun_config['from_email']}")
        print(f"  API Key: {'***' if mailgun_config['api_key'] else 'Not Set'}")
    except Exception as e:
        print(f"Mailgun config error: {e}")


def example_dns_usage():
    """Example of using DNS configuration for email domains."""
    
    print("\n=== DNS Configuration Usage ===")
    
    # Example domains
    domains = ["example.com", "mycompany.com", "trix.com"]
    
    for domain in domains:
        try:
            dns_config = get_dns_records(domain)
            print(f"\nDNS Records for {domain}:")
            print(f"  MX Records:")
            for mx in dns_config['mx_records']:
                print(f"    {mx}")
            print(f"  SPF Record: {dns_config['spf_record']}")
            print(f"  DMARC Record: {dns_config['dmarc_record']}")
        except Exception as e:
            print(f"DNS config error for {domain}: {e}")


def example_storage_and_delivery():
    """Example of using storage and delivery configuration."""
    
    print("\n=== Storage and Delivery Configuration Usage ===")
    
    email_settings = get_email_settings()
    
    # Storage configuration
    storage = email_settings.storage
    print(f"Storage Config:")
    print(f"  Upload Dir: {storage.upload_dir}")
    print(f"  Template Dir: {storage.template_storage_dir}")
    print(f"  Attachment Dir: {storage.attachment_storage_dir}")
    print(f"  Max File Size: {storage.max_file_size / 1024 / 1024:.1f} MB")
    print(f"  Default Quota: {storage.default_quota_mb} MB")
    print(f"  Max Quota: {storage.max_quota_mb} MB")
    
    # Delivery configuration
    delivery = email_settings.delivery
    print(f"\nDelivery Config:")
    print(f"  Max Recipients: {delivery.max_recipients_per_email}")
    print(f"  Batch Size: {delivery.email_queue_batch_size}")
    print(f"  Retry Attempts: {delivery.email_retry_attempts}")
    print(f"  Retry Delay: {delivery.email_retry_delay}s")
    print(f"  Rate Limit (hour): {delivery.email_rate_limit_per_hour}")
    print(f"  Rate Limit (day): {delivery.email_rate_limit_per_day}")
    print(f"  Track Opens: {delivery.track_email_opens}")
    print(f"  Track Clicks: {delivery.track_email_clicks}")


def example_template_usage():
    """Example of using template configuration."""
    
    print("\n=== Template Configuration Usage ===")
    
    email_settings = get_email_settings()
    templates = email_settings.templates
    
    print(f"Template Config:")
    print(f"  Default Language: {templates.default_template_language}")
    print(f"  Cache TTL: {templates.template_cache_ttl}s")
    print(f"  Preview Enabled: {templates.enable_template_preview}")
    print(f"  Max Template Size: {templates.max_template_size / 1024:.1f} KB")
    print(f"  Variable Limit: {templates.template_variables_limit}")
    
    # Default templates
    print(f"\nDefault Templates:")
    for template_name, template_config in DEFAULT_EMAIL_TEMPLATES.items():
        print(f"  {template_name}:")
        print(f"    Subject: {template_config['subject']}")
        print(f"    Has HTML: {'Yes' if template_config['html'] else 'No'}")
        print(f"    Has Text: {'Yes' if template_config['text'] else 'No'}")


def example_microservice_integration():
    """Example of how different microservices can use the shared config."""
    
    print("\n=== Microservice Integration Examples ===")
    
    # Email Module usage
    print("Email Module Integration:")
    print("  - Uses all shared configurations")
    print("  - Adds email-specific features (webmail, archiving)")
    print("  - Manages email domains and accounts")
    
    # Notification Service usage
    print("\nNotification Service Integration:")
    print("  - Uses shared SMTP/provider configs")
    print("  - Uses shared templates for notifications")
    print("  - Uses shared delivery settings")
    
    # CRM Module usage
    print("\nCRM Module Integration:")
    print("  - Uses shared provider configs for marketing emails")
    print("  - Uses shared template system")
    print("  - Uses shared delivery and rate limiting")
    
    # Commerce Service usage
    print("\nCommerce Service Integration:")
    print("  - Uses shared configs for transactional emails")
    print("  - Uses shared templates for order confirmations")
    print("  - Uses shared delivery for receipts")


def example_environment_specific():
    """Example of environment-specific configuration."""
    
    print("\n=== Environment-Specific Configuration ===")
    
    email_settings = get_email_settings()
    
    print(f"Current Environment: {email_settings.environment}")
    print(f"Debug Mode: {email_settings.debug}")
    
    # Environment-specific recommendations
    if email_settings.environment == "development":
        print("Development Environment:")
        print("  - Use SMTP for testing")
        print("  - Enable debug logging")
        print("  - Use test email addresses")
    elif email_settings.environment == "staging":
        print("Staging Environment:")
        print("  - Use SendGrid with limited quota")
        print("  - Enable email tracking")
        print("  - Use staging domain")
    elif email_settings.environment == "production":
        print("Production Environment:")
        print("  - Use multiple providers for redundancy")
        print("  - Enable all security features")
        print("  - Monitor delivery rates")


async def example_async_usage():
    """Example of async usage (for future async email operations)."""
    
    print("\n=== Async Usage Example ===")
    
    # Simulate async email operations
    email_settings = get_email_settings()
    
    print("Async email operations would use:")
    print(f"  - Provider: {email_settings.providers.default_provider}")
    print(f"  - Max concurrent: {email_settings.smtp.max_connections}")
    print(f"  - Timeout: {email_settings.smtp.timeout}s")
    
    # Simulate async delay
    await asyncio.sleep(0.1)
    print("  ✅ Async email configuration loaded")


def main():
    """Run all examples."""
    
    print("🔗 Shared Email Configuration Examples")
    print("=" * 50)
    
    try:
        example_basic_usage()
        example_provider_usage()
        example_dns_usage()
        example_storage_and_delivery()
        example_template_usage()
        example_microservice_integration()
        example_environment_specific()
        
        # Run async example
        asyncio.run(example_async_usage())
        
        print("\n✅ All examples completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Error running examples: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
