#!/usr/bin/env python3
"""
Distributed Base - Classes base para sistema distribuído de seeds
================================================================

Classes base e utilitários para o sistema distribuído de seeds.
Conforme especificado em docs/seed.md
"""

import asyncio
import logging
import time
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

logger = logging.getLogger(__name__)


class MicroserviceSeedModule(ABC):
    """Base class for all microservice seed modules."""
    
    def __init__(self, microservice_name: str, module_name: str):
        self.microservice_name = microservice_name
        self.module_name = module_name
        self.logger = logging.getLogger(f"seed.{microservice_name}.{module_name}")
        self.engine = None
        self.session_factory = None
        
    async def get_database_session(self) -> AsyncSession:
        """Get database session for this microservice."""
        if not self.session_factory:
            await self._initialize_database()
        return self.session_factory()
    
    async def _initialize_database(self):
        """Initialize database connection."""
        from .config.microservices_config import MICROSERVICES_SEEDS
        
        config = MICROSERVICES_SEEDS.get(self.microservice_name)
        if not config:
            raise ValueError(f"Configuração não encontrada para {self.microservice_name}")
        
        db_url = config['db_url']
        self.engine = create_async_engine(db_url, echo=False)
        self.session_factory = sessionmaker(
            self.engine, 
            class_=AsyncSession, 
            expire_on_commit=False
        )
    
    @abstractmethod
    async def check_existing_data(self) -> bool:
        """Check if data already exists. Return True if exists."""
        pass
    
    @abstractmethod
    async def create_data(self) -> None:
        """Create seed data."""
        pass
    
    async def run(self) -> bool:
        """Run the seed module."""
        start_time = time.time()
        
        try:
            self.logger.info(f"🌱 Iniciando seed: {self.module_name}")
            
            # Verificar se dados já existem
            if await self.check_existing_data():
                self.logger.info(f"⚠️ Dados já existem para {self.module_name}, pulando...")
                return True
            
            # Criar dados
            await self.create_data()
            
            execution_time = time.time() - start_time
            self.logger.info(f"✅ Seed {self.module_name} concluído em {execution_time:.2f}s")
            
            return True
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"❌ Erro no seed {self.module_name}: {e}")
            return False
        finally:
            if self.engine:
                await self.engine.dispose()
    
    async def cleanup(self):
        """Cleanup resources."""
        if self.engine:
            await self.engine.dispose()


class DistributedSeedOrchestrator:
    """Orchestrator for distributed seed execution."""
    
    def __init__(self):
        self.executed_modules = []
        self.failed_modules = []
        self.total_records_created = 0
        
    async def execute_module(self, module: MicroserviceSeedModule) -> bool:
        """Execute a single seed module."""
        try:
            success = await module.run()
            
            if success:
                self.executed_modules.append(module.module_name)
            else:
                self.failed_modules.append(module.module_name)
            
            return success
            
        except Exception as e:
            logger.error(f"❌ Erro crítico ao executar {module.module_name}: {e}")
            self.failed_modules.append(module.module_name)
            return False
        finally:
            await module.cleanup()
    
    async def execute_microservice_seeds(self, microservice_name: str, modules: list) -> Dict[str, Any]:
        """Execute all seed modules for a microservice."""
        start_time = time.time()
        
        logger.info(f"🚀 Executando seeds para microserviço: {microservice_name}")
        
        successful_modules = []
        failed_modules = []
        
        for module in modules:
            success = await self.execute_module(module)
            
            if success:
                successful_modules.append(module.module_name)
            else:
                failed_modules.append(module.module_name)
        
        execution_time = time.time() - start_time
        
        result = {
            "microservice": microservice_name,
            "successful_modules": successful_modules,
            "failed_modules": failed_modules,
            "total_modules": len(modules),
            "success_rate": len(successful_modules) / len(modules) * 100 if modules else 0,
            "execution_time": execution_time
        }
        
        if failed_modules:
            logger.error(f"❌ Falhas em {microservice_name}: {failed_modules}")
        else:
            logger.info(f"✅ Todos os seeds de {microservice_name} executados com sucesso")
        
        return result
    
    def get_summary(self) -> Dict[str, Any]:
        """Get execution summary."""
        total_modules = len(self.executed_modules) + len(self.failed_modules)
        
        return {
            "total_modules": total_modules,
            "successful_modules": len(self.executed_modules),
            "failed_modules": len(self.failed_modules),
            "success_rate": len(self.executed_modules) / total_modules * 100 if total_modules > 0 else 0,
            "executed_modules": self.executed_modules,
            "failed_modules": self.failed_modules,
            "total_records_created": self.total_records_created
        }


class SeedTransaction:
    """Transaction manager for seed operations."""
    
    def __init__(self, session: AsyncSession):
        self.session = session
        self.savepoint = None
        
    async def __aenter__(self):
        """Start transaction."""
        self.savepoint = await self.session.begin()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """End transaction."""
        if exc_type is None:
            await self.savepoint.commit()
        else:
            await self.savepoint.rollback()
            logger.error(f"❌ Rollback executado devido a erro: {exc_val}")


class RetryManager:
    """Manager for retry operations with backoff."""
    
    def __init__(self, max_retries: int = 3, base_delay: float = 1.0):
        self.max_retries = max_retries
        self.base_delay = base_delay
    
    async def execute_with_retry(self, func, *args, **kwargs):
        """Execute function with retry and exponential backoff."""
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                last_exception = e
                
                if attempt < self.max_retries:
                    delay = self.base_delay * (2 ** attempt)
                    logger.warning(f"⚠️ Tentativa {attempt + 1} falhou, tentando novamente em {delay}s: {e}")
                    await asyncio.sleep(delay)
                else:
                    logger.error(f"❌ Todas as {self.max_retries + 1} tentativas falharam")
        
        raise last_exception


class SeedValidator:
    """Validator for seed data integrity."""
    
    @staticmethod
    async def validate_database_connection(db_url: str) -> bool:
        """Validate database connection."""
        try:
            engine = create_async_engine(db_url, echo=False)
            async with engine.begin() as conn:
                await conn.execute("SELECT 1")
            await engine.dispose()
            return True
        except Exception as e:
            logger.error(f"❌ Erro de conexão com banco: {e}")
            return False
    
    @staticmethod
    async def validate_table_exists(session: AsyncSession, table_name: str) -> bool:
        """Validate if table exists."""
        try:
            from sqlalchemy import text
            result = await session.execute(text(f"""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = '{table_name}'
                );
            """))
            return result.scalar()
        except Exception as e:
            logger.error(f"❌ Erro ao verificar tabela {table_name}: {e}")
            return False
    
    @staticmethod
    async def validate_data_integrity(session: AsyncSession, validations: list) -> bool:
        """Validate data integrity with custom validations."""
        try:
            from sqlalchemy import text
            
            for validation in validations:
                query = validation.get('query')
                expected = validation.get('expected')
                description = validation.get('description', 'Validação')
                
                if not query:
                    continue
                
                result = await session.execute(text(query))
                actual = result.scalar()
                
                if actual != expected:
                    logger.error(f"❌ Falha na validação '{description}': esperado {expected}, obtido {actual}")
                    return False
                
                logger.debug(f"✅ Validação '{description}' passou")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro na validação de integridade: {e}")
            return False
