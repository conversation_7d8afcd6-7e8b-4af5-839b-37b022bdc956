{"Servers": {"1": {"Name": "<PERSON>x Core Coordinator", "Group": "Citus Coordinators", "Host": "trix-citus-core-coordinator", "Port": 5432, "MaintenanceDB": "postgres", "Username": "postgres", "SSLMode": "prefer", "Timeout": 10, "UseSSHTunnel": 0, "TunnelPort": "22", "TunnelAuthentication": 0}, "2": {"Name": "Trix Shared Coordinator", "Group": "Citus Coordinators", "Host": "trix-citus-shared-coordinator", "Port": 5432, "MaintenanceDB": "postgres", "Username": "postgres", "SSLMode": "prefer", "Timeout": 10}, "3": {"Name": "<PERSON>x Tenant Coordinator", "Group": "Citus Coordinators", "Host": "trix-citus-tenant-coordinator", "Port": 5432, "MaintenanceDB": "postgres", "Username": "postgres", "SSLMode": "prefer", "Timeout": 10}, "4": {"Name": "Trix Worker 1", "Group": "Citus Workers", "Host": "trix-citus-worker-1", "Port": 5432, "MaintenanceDB": "postgres", "Username": "postgres", "SSLMode": "prefer", "Timeout": 10}, "5": {"Name": "Trix Worker 2", "Group": "Citus Workers", "Host": "trix-citus-worker-2", "Port": 5432, "MaintenanceDB": "postgres", "Username": "postgres", "SSLMode": "prefer", "Timeout": 10}}}