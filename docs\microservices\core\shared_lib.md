# 📚 Shared Library - Biblioteca de Funcionalidades Compartilhadas

## 📋 Visão Geral

A **Shared Library** é uma biblioteca centralizada de funcionalidades compartilhadas que contém todos os modelos, schemas, serviços e utilitários comuns utilizados pelos microserviços da plataforma Trix. Esta biblioteca **NÃO é um microserviço**, mas sim uma **pasta de funcionalidades únicas** que garante a centralização da programação e evita duplicação de código.

### 🎯 **Informações Básicas**
- **Localização:** `microservices/core/shared_lib/`
- **Tipo:** Biblioteca de código compartilhado
- **Função:** Centralizar funcionalidades comuns entre microserviços
- **Origem:** Evolução do commerce_service (monolito original)
- **Status:** ✅ **ATIVO - BIBLIOTECA CENTRAL**
- **Versão:** 1.0.0 (Shared Library)

### 📊 **Princípios Fundamentais**
- 🔄 **Reutilização Máxima**: Zero duplicação de código entre microserviços
- 🏗️ **Centralização**: Uma única fonte da verdade para funcionalidades comuns
- 🔧 **Modularidade**: Cada módulo pode ser importado independentemente
- 📦 **Flexibilidade**: Microserviços importam apenas o que necessitam
- 🚀 **Performance**: Evita overhead de comunicação entre serviços para funcionalidades básicas

## 🏗️ **Estrutura da Biblioteca**

### 📁 **Módulos Disponíveis**

```
microservices/core/shared_lib/
├── 🏗️ infrastructure/         # 🔗 INFRAESTRUTURA ENTERPRISE COMPARTILHADA
│   ├── config/                # 🔗 Configurações centralizadas
│   │   ├── settings.py        # 🔗 Configurações base compartilhadas
│   │   ├── database.py        # 🔗 Configuração Citus Data comum
│   │   ├── vault.py           # 🔗 HashiCorp Vault integration
│   │   ├── kafka_config.py    # 🔗 Configuração Apache Kafka
│   │   ├── redis_config.py    # 🔗 Configuração Redis Cluster
│   │   └── __init__.py        # 🔗 Exportações das configurações
│   ├── messaging/             # 🔗 Clientes de messaging compartilhados
│   │   ├── kafka_client.py    # 🔗 Cliente Kafka compartilhado
│   │   ├── rabbitmq_client.py # 🔗 Cliente RabbitMQ compartilhado
│   │   ├── redis_client.py    # 🔗 Cliente Redis compartilhado
│   │   └── __init__.py        # 🔗 Exportações dos clientes
│   ├── observability/         # 🔗 Utilitários de monitoramento
│   │   ├── metrics.py         # 🔗 Métricas Prometheus compartilhadas
│   │   ├── tracing.py         # 🔗 Tracing distribuído compartilhado
│   │   ├── logging.py         # 🔗 Logging estruturado compartilhado
│   │   └── __init__.py        # 🔗 Exportações de observabilidade
│   ├── security/              # 🔗 Utilitários de segurança
│   │   ├── jwt.py             # 🔗 JWT handling compartilhado
│   │   ├── permissions.py     # 🔗 OPA policy integration
│   │   ├── encryption.py      # 🔗 Utilitários de criptografia
│   │   ├── rate_limiter.py    # 🔗 Rate limiting compartilhado
│   │   └── __init__.py        # 🔗 Exportações de segurança
│   ├── database/              # 🔗 Utilitários de banco de dados
│   │   ├── connection.py      # 🔗 Conexões de banco compartilhadas
│   │   ├── sharding.py        # 🔗 Sharding Citus Data compartilhado
│   │   ├── migrations.py      # 🔗 Migrações distribuídas
│   │   └── __init__.py        # 🔗 Exportações de banco
│   ├── kubernetes/            # 🔗 Configurações Kubernetes compartilhadas
│   │   ├── base_manifests/    # 🔗 Manifests base para todos os serviços
│   │   ├── helm_templates/    # 🔗 Templates Helm compartilhados
│   │   ├── istio_configs/     # 🔗 Configurações Istio/Service Mesh
│   │   └── monitoring/        # 🔗 Configurações de monitoramento
│   └── __init__.py            # 🔗 Exportações da infraestrutura
├── 🔧 utils/                  # 🔗 Utilitários comuns
│   ├── event_sourcing.py      # 🔗 Event sourcing compartilhado
│   ├── common.py              # 🔗 Utilitários comuns
│   ├── validators.py          # 🔗 Validadores compartilhados
│   └── __init__.py            # 🔗 Exportações dos utilitários
├── 📧 email/                  # 🔗 Sistema de email compartilhado
│   ├── models/                # 🔗 Modelos de email
│   ├── services/              # 🔗 Serviços de email
│   ├── templates/             # 🔗 Templates de email
│   └── __init__.py            # 🔗 Exportações de email
├── 🌐 i18n/                   # 🔗 Internacionalização compartilhada
│   ├── translations/          # 🔗 Arquivos de tradução
│   ├── services/              # 🔗 Serviços de i18n
│   └── __init__.py            # 🔗 Exportações de i18n
├── 🔐 auth/                   # 🔗 Autenticação compartilhada
│   ├── middleware/            # 🔗 Middleware de autenticação
│   ├── dependencies/          # 🔗 Dependências de auth
│   └── __init__.py            # 🔗 Exportações de auth
├── 🥜 allergens/              # Gestão de alérgenos alimentares
│   ├── models/
│   │   └── allergen.py        # Modelo de alérgenos
│   ├── schemas/               # Schemas Pydantic
│   ├── services/              # Lógica de negócio
│   ├── api/                   # Endpoints (se necessário)
│   └── tests/                 # Testes unitários
├── 🛒 cart/                   # Sistema de carrinho de compras
│   ├── models/                # Modelos de carrinho
│   ├── schemas/               # Schemas de carrinho
│   ├── services/              # Lógica de carrinho
│   ├── api/                   # APIs de carrinho
│   └── websockets/            # WebSocket para carrinho em tempo real
├── 💳 checkout/               # Processo de finalização de compra
│   ├── models/                # Modelos de checkout
│   ├── schemas/               # Schemas de checkout
│   ├── services/              # Lógica de checkout
│   └── api/                   # APIs de checkout
├── 🎨 customizations/         # Sistema de customizações
│   └── models/                # Modelos de customização
├── 📦 inventory/              # Gestão de estoque
│   ├── models/                # Modelos de estoque
│   ├── schemas/               # Schemas de estoque
│   ├── services/              # Lógica de estoque
│   └── api/                   # APIs de estoque
├── 🎬 media_system/           # Sistema de mídia
│   ├── models.py              # Modelos de mídia
│   ├── schemas.py             # Schemas de mídia
│   ├── services/              # Serviços de mídia
│   ├── api/                   # APIs de mídia
│   ├── dependencies.py        # Dependências
│   └── websockets/            # WebSocket para mídia
├── 🎁 offerts/                # Sistema de ofertas
│   ├── models/                # Modelos de ofertas
│   ├── schemas/               # Schemas de ofertas
│   ├── services/              # Lógica de ofertas
│   ├── api/                   # APIs de ofertas
│   └── websockets/            # WebSocket para ofertas
├── 📋 orders/                 # Sistema de pedidos
│   ├── models/
│   │   └── order.py           # Modelo principal de pedidos
│   ├── schemas/               # Schemas de pedidos
│   ├── services/              # Lógica de pedidos
│   ├── api/                   # APIs de pedidos
│   └── websockets/            # WebSocket para pedidos em tempo real
├── 🏪 pos/                    # Sistema de PDV (Point of Sale)
│   ├── models/                # Modelos de PDV
│   ├── schemas/               # Schemas de PDV
│   ├── services/              # Lógica de PDV
│   └── api/                   # APIs de PDV
├── 📱 qr/                     # Sistema de QR Code
│   ├── magicqr/               # QR Code mágico
│   └── standard/              # QR Code padrão
├── ⭐ reviews/                # Sistema de avaliações
│   ├── models/                # Modelos de reviews
│   ├── schemas/               # Schemas de reviews
│   ├── services/              # Lógica de reviews
│   └── api/                   # APIs de reviews
├── 🚚 shipping/               # Sistema de entrega
│   ├── models/                # Modelos de entrega
│   ├── schemas/               # Schemas de entrega
│   ├── services/              # Lógica de entrega
│   ├── api/                   # APIs de entrega
│   └── websockets/            # WebSocket para tracking
└── 🌱 migration/              # 🔗 Sistema de migração e seed distribuído
    ├── seed/                  # 🔗 Seeds distribuídos por microserviço
    │   ├── distributed_main.py # 🔗 Orquestrador central de seeds
    │   ├── core_services/     # 🔗 Seeds dos core services
    │   └── business_services/ # 🔗 Seeds dos business services
    └── alembic/               # 🔗 Configurações Alembic compartilhadas
```

## 🔧 **Como Usar a Shared Library**

### **1. Importação Básica**
```python
# Em qualquer microserviço
import sys
import os

# Adicionar shared_lib ao path
SHARED_LIB_PATH = os.path.join(os.path.dirname(__file__), '../../../shared_lib')
if SHARED_LIB_PATH not in sys.path:
    sys.path.insert(0, SHARED_LIB_PATH)

# Importar módulos necessários
from allergens.models.allergen import Allergen
from orders.models.order import Order, OrderStatus
from cart.models.cart import Cart, CartItem
from reviews.models.review import Review, Rating
```

### **2. Importação em Microserviços**

#### **Core Service**
```python
# microservices/core/core_service/app/models/__init__.py
import sys
import os

# Path para shared_lib
SHARED_LIB_PATH = os.path.join(os.path.dirname(__file__), '../../shared_lib')
sys.path.insert(0, SHARED_LIB_PATH)

# Importar modelos necessários para EShop
from orders.models.order import Order, OrderStatus, OrderItem
from cart.models.cart import Cart, CartItem
from reviews.models.review import Review, Rating
from allergens.models.allergen import Allergen

# Modelos específicos do Core Service
from .audit_log import AuditLog
from .help_center import Ticket, Message
```

#### **Restaurant Service**
```python
# microservices/business/restaurant_service/app/models/__init__.py
import sys
import os

SHARED_LIB_PATH = os.path.join(os.path.dirname(__file__), '../../../core/shared_lib')
sys.path.insert(0, SHARED_LIB_PATH)

# Importar modelos necessários para restaurante
from orders.models.order import Order, OrderStatus
from pos.models.pos import POSTransaction
from qr.magicqr.models import MagicQR
from allergens.models.allergen import Allergen
```

#### **Commerce Service (se mantido)**
```python
# microservices/business/commerce_service/app/models/__init__.py
import sys
import os

SHARED_LIB_PATH = os.path.join(os.path.dirname(__file__), '../../../core/shared_lib')
sys.path.insert(0, SHARED_LIB_PATH)

# Importar modelos de e-commerce
from cart.models.cart import Cart, CartItem
from checkout.models.checkout import Checkout, CheckoutSession
from inventory.models.inventory import Inventory, Stock
from shipping.models.shipping import Shipment, ShippingMethod
```

### **3. Schemas Compartilhados**
```python
# Importar schemas Pydantic
from orders.schemas.order import OrderCreate, OrderRead, OrderUpdate
from cart.schemas.cart import CartCreate, CartItemAdd
from reviews.schemas.review import ReviewCreate, ReviewRead
```

### **4. Serviços Compartilhados**
```python
# Importar serviços de negócio
from orders.services.order_service import OrderService
from cart.services.cart_service import CartService
from inventory.services.inventory_service import InventoryService
```

## 🏗️ **Infraestrutura Enterprise Compartilhada (NOVO)**

> **🎉 MIGRAÇÃO CONCLUÍDA**: Todas as configurações de infraestrutura enterprise foram centralizadas na shared_lib para garantir consistência e reduzir duplicação entre microserviços.

### **🔧 Configurações Centralizadas (`infrastructure/config/`)**

#### **📊 Database Configuration (Citus Data)**
```python
# infrastructure/config/database.py
from microservices.core.shared_lib.infrastructure.config.database import (
    CitusDataConfig,
    get_database_url,
    get_shard_count,
    get_replication_factor
)

# Configuração padrão para todos os microserviços
DATABASE_CONFIG = {
    'shard_count': 32,
    'replication_factor': 2,
    'connection_pool_size': 20,
    'max_overflow': 30,
    'pool_timeout': 30,
    'pool_recycle': 3600
}
```

#### **📡 Messaging Configuration (Kafka/RabbitMQ/Redis)**
```python
# infrastructure/config/kafka_config.py
KAFKA_CONFIG = {
    'bootstrap_servers': 'trix-kafka-1:9092,trix-kafka-2:9092,trix-kafka-3:9092',
    'security_protocol': 'SASL_SSL',
    'sasl_mechanism': 'SCRAM-SHA-256',
    'auto_offset_reset': 'earliest',
    'enable_auto_commit': False,
    'max_poll_records': 500
}

# infrastructure/config/redis_config.py
REDIS_CONFIG = {
    'cluster_nodes': ['trix-redis-1:6379', 'trix-redis-2:6379', 'trix-redis-3:6379'],
    'password': '${REDIS_PASSWORD}',
    'decode_responses': True,
    'health_check_interval': 30
}
```

#### **🔐 Security Configuration (Vault/OPA)**
```python
# infrastructure/config/vault.py
VAULT_CONFIG = {
    'addr': 'https://trix-vault:8200',
    'mount_path': 'secret/',
    'auth_method': 'kubernetes',
    'role': 'microservice-role',
    'token_ttl': 3600
}
```

### **📡 Messaging Infrastructure (`infrastructure/messaging/`)**

#### **🔗 Kafka Client Compartilhado**
```python
# infrastructure/messaging/kafka_client.py
from microservices.core.shared_lib.infrastructure.messaging import KafkaClient

class SharedKafkaClient:
    """Cliente Kafka compartilhado para todos os microserviços."""

    def __init__(self, service_name: str):
        self.service_name = service_name
        self.client = KafkaClient(service_name)

    async def publish_event(self, topic: str, event_data: dict, tenant_id: str = None):
        """Publica evento com padrões compartilhados."""
        await self.client.publish_event(topic, event_data, tenant_id)

    async def subscribe_to_events(self, topics: List[str], handler: callable):
        """Subscreve a eventos com padrões compartilhados."""
        await self.client.subscribe_to_events(topics, handler)
```

#### **🔗 RabbitMQ Client Compartilhado**
```python
# infrastructure/messaging/rabbitmq_client.py
class SharedRabbitMQClient:
    """Cliente RabbitMQ compartilhado para mensagens rápidas."""

    async def publish_notification(self, exchange: str, routing_key: str, message: dict):
        """Publica notificação com padrões compartilhados."""
        pass

    async def setup_queues(self, service_name: str):
        """Configura filas padrão para o microserviço."""
        pass
```

### **📊 Observability Infrastructure (`infrastructure/observability/`)**

#### **🔗 Métricas Prometheus Compartilhadas**
```python
# infrastructure/observability/metrics.py
from prometheus_client import Counter, Histogram, Gauge

class SharedMetrics:
    """Métricas Prometheus compartilhadas entre microserviços."""

    def __init__(self, service_name: str, service_version: str, environment: str):
        self.service_name = service_name

        # Métricas HTTP compartilhadas
        self.http_requests_total = Counter(
            'http_requests_total',
            'Total HTTP requests',
            ['service', 'endpoint', 'method', 'status']
        )

        self.http_request_duration = Histogram(
            'http_request_duration_seconds',
            'HTTP request duration',
            ['service', 'endpoint', 'method']
        )

        # Métricas de banco compartilhadas
        self.database_connections_active = Gauge(
            'database_connections_active',
            'Active database connections',
            ['service', 'shard']
        )

        # Métricas de messaging compartilhadas
        self.kafka_messages_produced = Counter(
            'kafka_messages_produced_total',
            'Total Kafka messages produced',
            ['service', 'topic', 'partition']
        )
```

#### **🔗 Tracing Distribuído Compartilhado**
```python
# infrastructure/observability/tracing.py
from opentelemetry import trace
from opentelemetry.exporter.jaeger.thrift import JaegerExporter

class SharedTracing:
    """Tracing distribuído compartilhado."""

    def __init__(self, service_name: str):
        self.service_name = service_name
        self.tracer = trace.get_tracer(service_name)

    def start_span(self, operation_name: str, **kwargs):
        """Inicia span com padrões compartilhados."""
        return self.tracer.start_span(operation_name, **kwargs)
```

### **🔐 Security Infrastructure (`infrastructure/security/`)**

#### **🔗 JWT Handling Compartilhado**
```python
# infrastructure/security/jwt.py
class SharedJWTHandler:
    """JWT handling compartilhado entre microserviços."""

    async def validate_token(self, token: str) -> dict:
        """Valida token JWT com Vault."""
        pass

    async def get_user_from_token(self, token: str) -> dict:
        """Extrai dados do usuário do token."""
        pass
```

#### **🔗 Rate Limiting Compartilhado**
```python
# infrastructure/security/rate_limiter.py
class SharedRateLimiter:
    """Rate limiting compartilhado usando Redis."""

    async def check_rate_limit(self, key: str, limit: int, window: int) -> bool:
        """Verifica rate limit com padrões compartilhados."""
        pass
```

### **🗄️ Database Infrastructure (`infrastructure/database/`)**

#### **🔗 Sharding Citus Data Compartilhado**
```python
# infrastructure/database/sharding.py
class SharedSharding:
    """Sharding Citus Data compartilhado."""

    def get_shard_for_tenant(self, tenant_id: str) -> str:
        """Determina shard baseado no tenant_id."""
        pass

    def setup_distributed_tables(self, tables: List[str]):
        """Configura tabelas distribuídas."""
        pass
```

#### **🔗 Connection Management Compartilhado**
```python
# infrastructure/database/connection.py
class SharedConnectionManager:
    """Gerenciamento de conexões compartilhado."""

    async def get_session(self, shard: str = None):
        """Obtém sessão de banco com pooling."""
        pass

    async def health_check(self) -> bool:
        """Health check das conexões."""
        pass
```

## 🎯 **Módulos Principais Detalhados**

### **📋 Orders (Pedidos)**
- **Função**: Sistema completo de gestão de pedidos
- **Modelos**: Order, OrderItem, OrderStatus
- **Usado por**: Core Service, Restaurant Service, Commerce Service
- **Features**: Status tracking, items management, pricing

### **🛒 Cart (Carrinho)**
- **Função**: Sistema de carrinho de compras
- **Modelos**: Cart, CartItem
- **Usado por**: Core Service, Commerce Service, Restaurant Service
- **Features**: Add/remove items, pricing calculation, session management

### **☸️ Kubernetes Infrastructure (`infrastructure/kubernetes/`)**

#### **🔗 Base Manifests Compartilhados**
```yaml
# infrastructure/kubernetes/base_manifests/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.serviceName }}
  namespace: {{ .Values.namespace }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app: {{ .Values.serviceName }}
  template:
    metadata:
      labels:
        app: {{ .Values.serviceName }}
        version: {{ .Values.version }}
    spec:
      serviceAccountName: {{ .Values.serviceAccount.name }}
      containers:
      - name: {{ .Values.serviceName }}
        image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
        ports:
        - containerPort: {{ .Values.service.targetPort }}
        env:
        - name: SERVICE_NAME
          value: {{ .Values.serviceName }}
        - name: ENVIRONMENT
          value: {{ .Values.environment }}
        resources:
          limits:
            cpu: {{ .Values.resources.limits.cpu }}
            memory: {{ .Values.resources.limits.memory }}
          requests:
            cpu: {{ .Values.resources.requests.cpu }}
            memory: {{ .Values.resources.requests.memory }}
```

#### **🔗 Helm Templates Compartilhados**
```yaml
# infrastructure/kubernetes/helm_templates/values-base.yaml
# Valores base compartilhados entre todos os microserviços
replicaCount: 3

image:
  repository: trix
  pullPolicy: IfNotPresent

service:
  type: ClusterIP
  port: 80

autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 100
  targetCPUUtilizationPercentage: 70
  targetMemoryUtilizationPercentage: 80

resources:
  limits:
    cpu: 2000m
    memory: 4Gi
  requests:
    cpu: 500m
    memory: 1Gi

# Service Mesh (Istio)
istio:
  enabled: true
  virtualService:
    enabled: true
  destinationRule:
    enabled: true
  peerAuthentication:
    enabled: true
    mtls:
      mode: STRICT

# Monitoring
monitoring:
  enabled: true
  serviceMonitor:
    enabled: true
    interval: 30s
    path: /metrics
  prometheusRule:
    enabled: true

# Security
security:
  podSecurityPolicy:
    enabled: true
  networkPolicy:
    enabled: true

# Vault Integration
vault:
  enabled: true
  serviceAccount: vault-auth
```

#### **🔗 Istio Service Mesh Compartilhado**
```yaml
# infrastructure/kubernetes/istio_configs/virtual-service-template.yaml
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: {{ .Values.serviceName }}-vs
  namespace: {{ .Values.namespace }}
spec:
  hosts:
  - {{ .Values.serviceName }}.{{ .Values.domain }}
  gateways:
  - istio-system/trix-gateway
  http:
  - match:
    - uri:
        prefix: /
    route:
    - destination:
        host: {{ .Values.serviceName }}
        port:
          number: {{ .Values.service.port }}
    timeout: 30s
    retries:
      attempts: 3
      perTryTimeout: 10s
```

### **🌱 Migration & Seed System (`migration/`)**

#### **🔗 Sistema de Seed Distribuído**
```python
# migration/seed/distributed_main.py
class DistributedSeedOrchestrator:
    """Orquestrador central de seeds para todos os microserviços."""

    MICROSERVICES_CONFIG = {
        'auth': {
            'module': 'core_services.auth',
            'priority': 1,
            'depends_on': [],
            'description': 'Autenticação e autorização'
        },
        'users': {
            'module': 'core_services.users',
            'priority': 2,
            'depends_on': ['auth'],
            'description': 'Gestão de usuários'
        },
        'tenants': {
            'module': 'core_services.tenants',
            'priority': 3,
            'depends_on': ['users'],
            'description': 'Gestão de tenants'
        },
        'domains': {
            'module': 'core_services.domains',
            'priority': 4,
            'depends_on': ['tenants'],
            'description': 'Gestão de domínios'
        }
    }

    async def run_seeds(self, microservices: List[str] = None):
        """Executa seeds em ordem de dependência."""
        pass
```

### **📧 Email System Compartilhado (`email/`)**

#### **🔗 Email Service Compartilhado**
```python
# email/services/email_service.py
from microservices.core.shared_lib.email.services import SharedEmailService

class SharedEmailService:
    """Serviço de email compartilhado entre microserviços."""

    async def send_notification(self, to: str, template: str, data: dict):
        """Envia notificação usando template compartilhado."""
        pass

    async def send_domain_verification(self, to: str, domain: str, verification_code: str):
        """Envia email de verificação de domínio."""
        pass

    async def send_ssl_expiry_warning(self, to: str, domain: str, expires_at: datetime):
        """Envia aviso de expiração de SSL."""
        pass
```

### **🔐 Auth System Compartilhado (`auth/`)**

#### **🔗 Auth Dependencies Compartilhadas**
```python
# auth/dependencies/auth_dependencies.py
from microservices.core.shared_lib.auth.dependencies import (
    get_current_user,
    require_system_role,
    require_tenant_permission
)

# Dependências compartilhadas entre microserviços
CurrentUserDep = Annotated[User, Depends(get_current_user)]
AdminUserDep = Annotated[User, Depends(require_system_role(['admin']))]
TenantOwnerDep = Annotated[User, Depends(require_tenant_permission(['owner']))]
```

### **⭐ Reviews (Avaliações)**
- **Função**: Sistema de avaliações e comentários
- **Modelos**: Review, Rating
- **Usado por**: Core Service, Restaurant Service, Commerce Service
- **Features**: Star rating, comments, moderation

### **🥜 Allergens (Alérgenos)**
- **Função**: Gestão de alérgenos alimentares
- **Modelos**: Allergen
- **Usado por**: Restaurant Service, Core Service
- **Features**: Allergen tracking, safety compliance

### **📦 Inventory (Estoque)**
- **Função**: Gestão de estoque e inventário
- **Modelos**: Inventory, Stock, StockMovement
- **Usado por**: Commerce Service, Restaurant Service
- **Features**: Stock tracking, low stock alerts, movements

### **🚚 Shipping (Entrega)**
- **Função**: Sistema de entrega e logística
- **Modelos**: Shipment, ShippingMethod, DeliveryTracking
- **Usado por**: Commerce Service, Core Service
- **Features**: Shipping calculation, tracking, delivery status

### **🏪 POS (Point of Sale)**
- **Função**: Sistema de ponto de venda
- **Modelos**: POSTransaction, POSSession
- **Usado por**: Restaurant Service, Core Service
- **Features**: Transaction processing, session management

### **📱 QR (QR Code)**
- **Função**: Sistema de QR Code para pedidos e pagamentos
- **Modelos**: MagicQR, StandardQR
- **Usado por**: Restaurant Service, Core Service
- **Features**: QR generation, scanning, magic QR functionality

### **🎁 Offers (Ofertas)**
- **Função**: Sistema de ofertas e promoções
- **Modelos**: Offer, Promotion, Discount
- **Usado por**: Core Service, Commerce Service, Restaurant Service
- **Features**: Discount calculation, promotion management

### **🎬 Media System (Sistema de Mídia)**
- **Função**: Gestão de arquivos de mídia
- **Modelos**: Media, MediaFile, MediaCategory
- **Usado por**: Todos os serviços
- **Features**: File upload, image processing, media management

### **💳 Checkout (Finalização)**
- **Função**: Processo de checkout e pagamento
- **Modelos**: Checkout, CheckoutSession, PaymentMethod
- **Usado por**: Commerce Service, Restaurant Service
- **Features**: Payment processing, checkout flow

### **🎨 Customizations (Customizações)**
- **Função**: Sistema de customizações de produtos
- **Modelos**: Customization, CustomizationOption
- **Usado por**: Restaurant Service, Commerce Service
- **Features**: Product customization, option management

## 🔄 **Integração com Ghost Function Service**

A Shared Library pode ser distribuída via Ghost Function Service para garantir sincronização:

```python
# ghost_function_service/app/shared_modules/distributor.py
class SharedLibDistributor:
    """Distribui módulos da shared_lib para microserviços"""

    async def sync_shared_lib_to_service(self, service_name: str, modules: List[str]):
        """Sincroniza módulos específicos para um serviço"""
        shared_lib_path = "/app/shared_lib"
        service_path = f"/services/{service_name}/shared_lib"

        for module in modules:
            await self.copy_module(
                f"{shared_lib_path}/{module}",
                f"{service_path}/{module}"
            )

        await self.notify_service_update(service_name, modules)

    async def get_required_modules(self, service_name: str) -> List[str]:
        """Retorna lista de módulos necessários para um serviço"""
        service_modules = {
            "core_service": ["orders", "cart", "reviews", "allergens"],
            "restaurant_service": ["orders", "pos", "qr", "allergens", "customizations"],
            "commerce_service": ["cart", "checkout", "inventory", "shipping", "offers"],
            "user_service": ["reviews", "orders"],
            "payment_service": ["checkout", "orders"]
        }
        return service_modules.get(service_name, [])
```

## 📚 **Documentação de Módulos**

Cada módulo da shared_lib possui sua própria documentação:
- `shared_lib/orders/README.md` - Documentação do sistema de pedidos
- `shared_lib/cart/README.md` - Documentação do carrinho
- `shared_lib/reviews/README.md` - Documentação de avaliações
- `shared_lib/allergens/README.md` - Documentação de alérgenos
- `shared_lib/inventory/README.md` - Documentação de estoque
- `shared_lib/shipping/README.md` - Documentação de entrega
- `shared_lib/pos/README.md` - Documentação do PDV
- `shared_lib/qr/README.md` - Documentação do QR Code
- `shared_lib/offers/README.md` - Documentação de ofertas
- `shared_lib/media_system/README.md` - Documentação de mídia
- `shared_lib/checkout/README.md` - Documentação de checkout
- `shared_lib/customizations/README.md` - Documentação de customizações

## 🚀 **Vantagens da Shared Library**

1. ✅ **Zero Duplicação**: Código escrito uma vez, usado em múltiplos serviços
2. ✅ **Manutenção Centralizada**: Correções e melhorias em um local
3. ✅ **Consistência**: Mesma lógica de negócio em todos os serviços
4. ✅ **Performance**: Sem overhead de comunicação entre serviços
5. ✅ **Flexibilidade**: Cada serviço importa apenas o necessário
6. ✅ **Versionamento**: Controle de versões centralizado
7. ✅ **Testabilidade**: Testes centralizados para funcionalidades comuns
8. ✅ **Desenvolvimento Ágil**: Mudanças propagam automaticamente
9. ✅ **Arquitetura Limpa**: Separação clara entre funcionalidades comuns e específicas
10. ✅ **Economia de Recursos**: Menos código duplicado = menos bugs

## ⚠️ **Considerações Importantes**

### **Não é um Microserviço**
- É uma biblioteca de código compartilhado
- Não possui endpoints próprios
- Não roda como serviço independente
- Não tem banco de dados próprio

### **Import Local**
- Cada serviço importa os módulos necessários
- Funcionalidades executam localmente no serviço
- Sem comunicação de rede entre shared_lib e serviços

### **Versionamento Cuidadoso**
- Mudanças podem afetar múltiplos serviços
- Testes obrigatórios antes de qualquer alteração
- Versionamento semântico recomendado
- Backward compatibility sempre que possível

### **Responsabilidades**
- **Shared Library**: Funcionalidades comuns e reutilizáveis
- **Microserviços**: Lógica de negócio específica e endpoints
- **Ghost Function Service**: Distribuição e sincronização

## 🔧 **Configuração de Desenvolvimento**

### **Setup Inicial**
```bash
# Navegar para shared_lib
cd microservices/core/shared_lib

# Instalar dependências (se necessário)
pip install -r requirements.txt

# Executar testes
python -m pytest tests/ -v

# Verificar qualidade do código
flake8 .
black .
isort .
```

### **Adicionando Novo Módulo**
```bash
# Criar estrutura do módulo
mkdir -p new_module/{models,schemas,services,api,tests}

# Criar arquivos base
touch new_module/__init__.py
touch new_module/models/__init__.py
touch new_module/schemas/__init__.py
touch new_module/services/__init__.py
touch new_module/api/__init__.py
touch new_module/tests/__init__.py
touch new_module/README.md
```

### **Testando Integração**
```python
# test_integration.py
import sys
import os

# Adicionar shared_lib ao path
SHARED_LIB_PATH = os.path.join(os.path.dirname(__file__), '../shared_lib')
sys.path.insert(0, SHARED_LIB_PATH)

def test_import_modules():
    """Testa se todos os módulos podem ser importados"""
    try:
        from orders.models.order import Order
        from cart.models.cart import Cart
        from reviews.models.review import Review
        assert True
    except ImportError as e:
        assert False, f"Erro ao importar módulos: {e}"
```

## 🔮 **Roadmap**

### **Fase 1: Consolidação (Atual)**
- ✅ Migração do commerce_service para shared_lib
- ✅ Documentação completa
- ✅ Estruturação de módulos
- ✅ Definição de padrões de importação

### **Fase 2: Otimização**
- 🔄 Criação de package Python interno
- 🔄 Sistema de versionamento automático
- 🔄 Testes automatizados para todos os módulos
- 🔄 CI/CD para shared_lib
- 🔄 Documentação automática (Sphinx)

### **Fase 3: Distribuição Inteligente**
- 🔄 Integração com Ghost Function Service
- 🔄 Sincronização automática entre serviços
- 🔄 Cache distribuído de módulos
- 🔄 Hot reload de módulos
- 🔄 Métricas de uso por módulo

### **Fase 4: Evolução Avançada**
- 🔄 Lazy loading de módulos
- 🔄 Dependency injection automático
- 🔄 Plugin system para extensões
- 🔄 Versionamento por módulo individual
- 🔄 Rollback automático em caso de problemas

## 📊 **Métricas e Monitoramento**

### **Métricas Importantes**
- Número de importações por módulo
- Tempo de carregamento dos módulos
- Uso de memória por módulo
- Frequência de atualizações
- Cobertura de testes por módulo

### **Alertas Configurados**
- Falha na importação de módulos críticos
- Aumento significativo no tempo de carregamento
- Quebra de compatibilidade entre versões
- Testes falhando em módulos compartilhados

## 🤝 **Contribuição**

### **Guidelines para Contribuição**
1. **Testes Obrigatórios**: Todo código deve ter testes
2. **Documentação**: Atualizar README.md do módulo
3. **Backward Compatibility**: Manter compatibilidade sempre que possível
4. **Code Review**: Revisão obrigatória para mudanças
5. **Versionamento**: Seguir semantic versioning

### **Processo de Mudanças**
1. Criar branch para a feature/fix
2. Implementar mudanças com testes
3. Atualizar documentação
4. Executar todos os testes
5. Criar pull request
6. Code review
7. Merge após aprovação

---

**📝 Nota Final**: A Shared Library é o **coração da reutilização de código** na plataforma Trix. Ela garante que funcionalidades comuns sejam implementadas uma única vez e utilizadas por todos os microserviços que necessitam, mantendo a **consistência**, **performance** e **facilidade de manutenção** em toda a arquitetura.

**🎯 Objetivo**: Transformar o antigo commerce_service (monolito) em uma biblioteca modular e reutilizável que serve como base para todos os microserviços da plataforma, eliminando duplicação de código e centralizando funcionalidades essenciais.

**🔗 Integração**: Funciona perfeitamente com o Ghost Function Service para distribuição inteligente e com todos os microserviços para máxima reutilização de código.