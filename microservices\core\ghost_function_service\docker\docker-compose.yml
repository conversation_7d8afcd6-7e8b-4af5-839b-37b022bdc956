# Ghost Function Service - Docker Compose
# Ambiente completo para desenvolvimento e testes

services:
  # === GHOST FUNCTION SERVICE ===
  ghost-function-service:
    build:
      context: ../../..
      dockerfile: microservices/core/ghost_function_service/Dockerfile
    container_name: ghost-function-service
    ports:
      - "8026:8026"
    environment:
      # Configurações básicas
      - ENVIRONMENT=development
      - DEBUG=true
      - PORT=8026
      
      # Database
      - DATABASE_URL=postgresql+asyncpg://ghost:ghost123@postgres:5432/ghost_function_db
      - DATABASE_ECHO=false
      
      # Redis
      - REDIS_URL=redis://redis:6379/0
      
      # Kafka
      - KAFKA_BOOTSTRAP_SERVERS=["kafka:9092"]
      
      # RabbitMQ
      - RABBITMQ_URL=amqp://guest:guest@rabbitmq:5672/
      
      # Monitoramento
      - PROMETHEUS_ENABLED=true
      - JAEGER_ENABLED=true
      - JAEGER_ENDPOINT=http://jaeger:14268/api/traces
      
      # Configurações específicas
      - MONITORING_ENABLED=true
      - GOLDEN_SNAPSHOT_ENABLED=true
      - CHAOS_ENGINEERING_ENABLED=false
    
    depends_on:
      - postgres
      - redis
      - kafka
      - rabbitmq
    
    volumes:
      - ./app:/app/app:ro
      - ./logs:/app/logs
    
    networks:
      - trix-network
    
    restart: unless-stopped
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8026/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # === POSTGRESQL ===
  postgres:
    image: postgres:15-alpine
    container_name: ghost-postgres
    environment:
      - POSTGRES_DB=ghost_function_db
      - POSTGRES_USER=ghost
      - POSTGRES_PASSWORD=ghost123
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    
    ports:
      - "5433:5432"
    
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    
    networks:
      - trix-network
    
    restart: unless-stopped
    
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ghost -d ghost_function_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # === REDIS ===
  redis:
    image: redis:7-alpine
    container_name: ghost-redis
    ports:
      - "6380:6379"
    
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf:ro
    
    command: redis-server /usr/local/etc/redis/redis.conf
    
    networks:
      - trix-network
    
    restart: unless-stopped
    
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  # === KAFKA ===
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: ghost-zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    
    volumes:
      - zookeeper_data:/var/lib/zookeeper/data
      - zookeeper_logs:/var/lib/zookeeper/log
    
    networks:
      - trix-network
    
    restart: unless-stopped

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: ghost-kafka
    depends_on:
      - zookeeper
    
    ports:
      - "9093:9092"
    
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: true
      KAFKA_LOG_RETENTION_HOURS: 168
      KAFKA_LOG_SEGMENT_BYTES: **********
    
    volumes:
      - kafka_data:/var/lib/kafka/data
    
    networks:
      - trix-network
    
    restart: unless-stopped
    
    healthcheck:
      test: ["CMD", "kafka-broker-api-versions", "--bootstrap-server", "localhost:9092"]
      interval: 30s
      timeout: 10s
      retries: 3

  # === RABBITMQ ===
  rabbitmq:
    image: rabbitmq:3.12-management-alpine
    container_name: ghost-rabbitmq
    ports:
      - "5673:5672"
      - "15673:15672"
    
    environment:
      - RABBITMQ_DEFAULT_USER=guest
      - RABBITMQ_DEFAULT_PASS=guest
    
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    
    networks:
      - trix-network
    
    restart: unless-stopped
    
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # === PROMETHEUS ===
  prometheus:
    image: prom/prometheus:latest
    container_name: ghost-prometheus
    ports:
      - "9091:9090"
    
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    
    networks:
      - trix-network
    
    restart: unless-stopped

  # === GRAFANA ===
  grafana:
    image: grafana/grafana:latest
    container_name: ghost-grafana
    ports:
      - "3001:3000"
    
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./config/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    
    networks:
      - trix-network
    
    restart: unless-stopped

# === VOLUMES ===
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  kafka_data:
    driver: local
  zookeeper_data:
    driver: local
  zookeeper_logs:
    driver: local
  rabbitmq_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

# === NETWORKS ===
networks:
  trix-network:
    external: true
    name: trix-network
