# ============================================================================
# EMAIL SERVICE - Microserviço de Email
# ============================================================================
# Porta: 8012 | Database: postgres-email (5445) | Redis: DB 12
# Submódulos: email_management, email_templates, email_delivery, email_analytics, webmail
# ============================================================================

services:
  # ============================================================================
  # EMAIL SERVICE - Serviço Principal
  # ============================================================================
  trix-shared-email:
    build:
      context: ../
      dockerfile: docker/Dockerfile
    container_name: trix-shared-email
    ports:
      - "8012:8000"
    environment:
      - DATABASE_URL=postgresql+asyncpg://${EMAIL_DB_USER}:${EMAIL_DB_PASSWORD}@trix-citus-shared-coordinator:5432/${EMAIL_DB_NAME}
      - REDIS_URL=redis://:${REDIS_PASSWORD}@trix-redis:6379/12
      - AUTH_SERVICE_URL=http://trix-core-auth:8001
      - SECRET_KEY=${JWT_SECRET_KEY}
      - DEBUG=${DEBUG}
      - SERVICE_NAME=email-service
      - SERVICE_VERSION=1.0.0
      - CELERY_BROKER_URL=redis://:${REDIS_PASSWORD}@trix-redis:6379/12
      - CELERY_RESULT_BACKEND=redis://:${REDIS_PASSWORD}@trix-redis:6379/12
      - WEBSOCKET_ENABLED=true
      # Email Provider Configurations
      - SENDGRID_API_KEY=${SENDGRID_API_KEY}
      - AWS_SES_ACCESS_KEY=${AWS_SES_ACCESS_KEY}
      - AWS_SES_SECRET_KEY=${AWS_SES_SECRET_KEY}
      - AWS_SES_REGION=${AWS_SES_REGION}
      - MAILGUN_API_KEY=${MAILGUN_API_KEY}
      - MAILGUN_DOMAIN=${MAILGUN_DOMAIN}
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_PORT=${SMTP_PORT}
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASSWORD=${SMTP_PASSWORD}
    depends_on:
      - trix-redis
    volumes:
      - trix_email_data:/app/data
      - trix_email_templates:/app/templates
      - trix_email_attachments:/app/attachments
      - ./logs:/app/logs
    networks:
      - trix-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # ============================================================================
  # EMAIL DATABASE - PostgreSQL para Email Service
  # ============================================================================
  trix-db-email:
    image: postgres:15-alpine
    container_name: trix-db-email
    environment:
      - POSTGRES_DB=${EMAIL_DB_NAME}
      - POSTGRES_USER=${EMAIL_DB_USER}
      - POSTGRES_PASSWORD=${EMAIL_DB_PASSWORD}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    ports:
      - "5445:5432"
    volumes:
      - trix_email_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - trix-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${EMAIL_DB_USER} -d ${EMAIL_DB_NAME}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # ============================================================================
  # EMAIL CELERY WORKER - Processamento Assíncrono
  # ============================================================================
  trix-email-worker:
    build:
      context: ../
      dockerfile: Dockerfile
    container_name: trix-email-worker
    command: celery -A app.celery_app worker --loglevel=info --concurrency=4
    environment:
      - DATABASE_URL=postgresql+asyncpg://${EMAIL_DB_USER}:${EMAIL_DB_PASSWORD}@trix-citus-shared-coordinator:5432/${EMAIL_DB_NAME}
      - REDIS_URL=redis://:${REDIS_PASSWORD}@trix-redis:6379/12
      - CELERY_BROKER_URL=redis://:${REDIS_PASSWORD}@trix-redis:6379/12
      - CELERY_RESULT_BACKEND=redis://:${REDIS_PASSWORD}@trix-redis:6379/12
      - AUTH_SERVICE_URL=http://trix-core-auth:8001
      - MEDIA_SERVICE_URL=http://trix-media-system:8020
      - FINANCIAL_SERVICE_URL=http://trix-shared-financial:8013
      - SECRET_KEY=${JWT_SECRET_KEY}
      - DEBUG=${DEBUG}
      - SERVICE_NAME=email-worker
      # Email Provider Configurations
      - SENDGRID_API_KEY=${SENDGRID_API_KEY}
      - AWS_SES_ACCESS_KEY=${AWS_SES_ACCESS_KEY}
      - AWS_SES_SECRET_KEY=${AWS_SES_SECRET_KEY}
      - AWS_SES_REGION=${AWS_SES_REGION}
      - MAILGUN_API_KEY=${MAILGUN_API_KEY}
      - MAILGUN_DOMAIN=${MAILGUN_DOMAIN}
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_PORT=${SMTP_PORT}
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASSWORD=${SMTP_PASSWORD}
    depends_on:
      - trix-redis
      - trix-shared-email
    volumes:
      - trix_email_data:/app/data
      - trix_email_attachments:/app/attachments
      - ./logs:/app/logs
    networks:
      - trix-network
    restart: unless-stopped

  # ============================================================================
  # EMAIL CELERY BEAT - Agendador de Tarefas
  # ============================================================================
  trix-email-beat:
    build:
      context: ../
      dockerfile: Dockerfile
    container_name: trix-email-beat
    command: celery -A app.celery_app beat --loglevel=info
    environment:
      - DATABASE_URL=postgresql+asyncpg://${EMAIL_DB_USER}:${EMAIL_DB_PASSWORD}@trix-citus-shared-coordinator:5432/${EMAIL_DB_NAME}
      - REDIS_URL=redis://:${REDIS_PASSWORD}@trix-redis:6379/12
      - CELERY_BROKER_URL=redis://:${REDIS_PASSWORD}@trix-redis:6379/12
      - CELERY_RESULT_BACKEND=redis://:${REDIS_PASSWORD}@trix-redis:6379/12
      - SECRET_KEY=${JWT_SECRET_KEY}
      - DEBUG=${DEBUG}
      - SERVICE_NAME=email-beat
    depends_on:
      - trix-redis
      - trix-shared-email
    volumes:
      - trix_email_data:/app/data
      - ./logs:/app/logs
    networks:
      - trix-network
    restart: unless-stopped

# ============================================================================
# NETWORKS
# ============================================================================
networks:
  trix-network:
    external: true
