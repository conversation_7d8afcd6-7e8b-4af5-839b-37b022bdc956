#!/usr/bin/env python3
"""
🚀 Trix Citus Cluster Setup Script
Configura e inicializa o cluster Citus distribuído por microserviço
"""

import asyncio
import asyncpg
import logging
import json
import os
from typing import Dict, List, Any
from dataclasses import dataclass
from pathlib import Path

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class CitusCoordinator:
    """Configuração de um coordenador Citus"""
    name: str
    host: str
    port: int
    database: str
    domain: str
    microservices: List[str]
    workers: List[str]

@dataclass
class CitusWorker:
    """Configuração de um worker Citus"""
    name: str
    host: str
    port: int
    worker_id: int

class CitusClusterManager:
    """Gerenciador do cluster Citus distribuído"""
    
    def __init__(self):
        self.coordinators = self._load_coordinators_config()
        self.workers = self._load_workers_config()
        self.microservices_mapping = self._load_microservices_mapping()
        
    def _load_coordinators_config(self) -> Dict[str, CitusCoordinator]:
        """Carrega configuração dos coordenadores"""
        return {
            'core': CitusCoordinator(
                name='trix-citus-core-coordinator',
                host='trix-citus-core-coordinator',
                port=5432,
                database='postgres',
                domain='core',
                microservices=['auth_service', 'user_service', 'tenant_service', 'core_service', 'i18n_service'],
                workers=['trix-citus-worker-1', 'trix-citus-worker-2']
            ),
            'tenant': CitusCoordinator(
                name='trix-citus-tenant-coordinator',
                host='trix-citus-tenant-coordinator',
                port=5433,
                database='postgres',
                domain='tenant',
                microservices=['restaurant_module', 'consultancy_module', 'shop_module'],
                workers=['trix-citus-worker-1', 'trix-citus-worker-2']
            ),
            'shared': CitusCoordinator(
                name='trix-citus-shared-coordinator',
                host='trix-citus-shared-coordinator',
                port=5434,
                database='postgres',
                domain='shared',
                microservices=['hr_module', 'crm_module', 'email_module', 'financial_module', 'notifications_module'],
                workers=['trix-citus-worker-1', 'trix-citus-worker-2']
            )
        }
    
    def _load_workers_config(self) -> Dict[str, CitusWorker]:
        """Carrega configuração dos workers"""
        return {
            'worker-1': CitusWorker(
                name='trix-citus-worker-1',
                host='trix-citus-worker-1',
                port=5435,
                worker_id=1
            ),
            'worker-2': CitusWorker(
                name='trix-citus-worker-2',
                host='trix-citus-worker-2',
                port=5436,
                worker_id=2
            )
        }
    
    def _load_microservices_mapping(self) -> Dict[str, Dict[str, Any]]:
        """Carrega mapeamento de microserviços para coordenadores"""
        return {
            # Core Services
            'auth_service': {
                'coordinator': 'core',
                'database': 'auth_db',
                'distribution_key': 'user_id',
                'tables': ['users', 'auth_sessions', 'auth_tokens', 'roles', 'permissions'],
                'shard_count': 32,
                'replication_factor': 2
            },
            'user_service': {
                'coordinator': 'core',
                'database': 'users_db',
                'distribution_key': 'user_id',
                'tables': ['user_profiles', 'user_preferences', 'user_addresses'],
                'shard_count': 32,
                'replication_factor': 2
            },
            'tenant_service': {
                'coordinator': 'core',
                'database': 'tenants_db',
                'distribution_key': 'tenant_id',
                'tables': ['tenants', 'tenant_settings', 'tenant_user_associations'],
                'shard_count': 16,
                'replication_factor': 2
            },
            
            # Tenant Services
            'restaurant_module': {
                'coordinator': 'tenant',
                'database': 'restaurant_db',
                'distribution_key': 'tenant_id',
                'tables': ['restaurant_menus', 'restaurant_orders', 'restaurant_tables', 'restaurant_inventories'],
                'shard_count': 64,
                'replication_factor': 2
            },
            'consultancy_module': {
                'coordinator': 'tenant',
                'database': 'consultancy_db',
                'distribution_key': 'tenant_id',
                'tables': ['consultancy_cases', 'consultancy_billings', 'consultancy_workflows'],
                'shard_count': 32,
                'replication_factor': 2
            },
            
            # Shared Services
            'hr_module': {
                'coordinator': 'shared',
                'database': 'hr_db',
                'distribution_key': 'tenant_id',
                'tables': ['hr_employees', 'hr_departments', 'hr_lms_courses'],
                'shard_count': 32,
                'replication_factor': 2
            },
            'crm_module': {
                'coordinator': 'shared',
                'database': 'crm_db',
                'distribution_key': 'tenant_id',
                'tables': ['crm_accounts', 'crm_contacts', 'crm_interactions'],
                'shard_count': 32,
                'replication_factor': 2
            }
        }
    
    async def setup_cluster(self):
        """Configura o cluster Citus completo"""
        logger.info("🚀 Iniciando configuração do cluster Citus distribuído...")
        
        try:
            # 1. Verificar conectividade
            await self._check_connectivity()
            
            # 2. Configurar coordenadores
            await self._setup_coordinators()
            
            # 3. Configurar workers
            await self._setup_workers()
            
            # 4. Adicionar workers aos coordenadores
            await self._add_workers_to_coordinators()
            
            # 5. Criar bancos de dados por microserviço
            await self._create_microservice_databases()
            
            # 6. Configurar distribuição de tabelas
            await self._setup_distributed_tables()
            
            # 7. Verificar configuração
            await self._verify_cluster_setup()
            
            logger.info("✅ Cluster Citus configurado com sucesso!")
            
        except Exception as e:
            logger.error(f"❌ Erro na configuração do cluster: {e}")
            raise
    
    async def _check_connectivity(self):
        """Verifica conectividade com todos os nós"""
        logger.info("🔍 Verificando conectividade com nós do cluster...")
        
        # Verificar coordenadores
        for coord_name, coord in self.coordinators.items():
            try:
                conn = await asyncpg.connect(
                    host=coord.host,
                    port=coord.port,
                    user='postgres',
                    password='TrixSuperSecure2024!',
                    database=coord.database
                )
                await conn.execute('SELECT 1')
                await conn.close()
                logger.info(f"✅ Coordenador {coord_name} conectado")
            except Exception as e:
                logger.error(f"❌ Falha ao conectar coordenador {coord_name}: {e}")
                raise
        
        # Verificar workers
        for worker_name, worker in self.workers.items():
            try:
                conn = await asyncpg.connect(
                    host=worker.host,
                    port=worker.port,
                    user='postgres',
                    password='TrixSuperSecure2024!',
                    database='postgres'
                )
                await conn.execute('SELECT 1')
                await conn.close()
                logger.info(f"✅ Worker {worker_name} conectado")
            except Exception as e:
                logger.error(f"❌ Falha ao conectar worker {worker_name}: {e}")
                raise
    
    async def _setup_coordinators(self):
        """Configura os coordenadores Citus"""
        logger.info("⚙️ Configurando coordenadores Citus...")
        
        for coord_name, coord in self.coordinators.items():
            logger.info(f"Configurando coordenador {coord_name}...")
            
            conn = await asyncpg.connect(
                host=coord.host,
                port=coord.port,
                user='postgres',
                password='TrixSuperSecure2024!',
                database=coord.database
            )
            
            try:
                # Criar extensão Citus
                await conn.execute('CREATE EXTENSION IF NOT EXISTS citus;')
                
                # Configurar como coordenador
                await conn.execute("SELECT citus_set_coordinator_host($1, $2);", coord.host, coord.port)
                
                logger.info(f"✅ Coordenador {coord_name} configurado")
                
            finally:
                await conn.close()
    
    async def _setup_workers(self):
        """Configura os workers Citus"""
        logger.info("⚙️ Configurando workers Citus...")
        
        for worker_name, worker in self.workers.items():
            logger.info(f"Configurando worker {worker_name}...")
            
            conn = await asyncpg.connect(
                host=worker.host,
                port=worker.port,
                user='postgres',
                password='TrixSuperSecure2024!',
                database='postgres'
            )
            
            try:
                # Criar extensão Citus
                await conn.execute('CREATE EXTENSION IF NOT EXISTS citus;')
                
                logger.info(f"✅ Worker {worker_name} configurado")
                
            finally:
                await conn.close()
    
    async def _add_workers_to_coordinators(self):
        """Adiciona workers aos coordenadores"""
        logger.info("🔗 Adicionando workers aos coordenadores...")
        
        for coord_name, coord in self.coordinators.items():
            logger.info(f"Adicionando workers ao coordenador {coord_name}...")
            
            conn = await asyncpg.connect(
                host=coord.host,
                port=coord.port,
                user='postgres',
                password='TrixSuperSecure2024!',
                database=coord.database
            )
            
            try:
                for worker_name in coord.workers:
                    worker = self.workers[worker_name.split('-')[-1]]
                    
                    # Verificar se worker já existe
                    existing = await conn.fetchval(
                        "SELECT COUNT(*) FROM pg_dist_node WHERE nodename = $1 AND nodeport = $2",
                        worker.host, worker.port
                    )
                    
                    if existing == 0:
                        await conn.execute(
                            "SELECT citus_add_node($1, $2);",
                            worker.host, worker.port
                        )
                        logger.info(f"✅ Worker {worker_name} adicionado ao coordenador {coord_name}")
                    else:
                        logger.info(f"ℹ️ Worker {worker_name} já existe no coordenador {coord_name}")
                
            finally:
                await conn.close()
    
    async def _create_microservice_databases(self):
        """Cria bancos de dados específicos por microserviço"""
        logger.info("🗄️ Criando bancos de dados por microserviço...")
        
        for service_name, config in self.microservices_mapping.items():
            coord_name = config['coordinator']
            coord = self.coordinators[coord_name]
            database_name = config['database']
            
            logger.info(f"Criando banco {database_name} para {service_name}...")
            
            conn = await asyncpg.connect(
                host=coord.host,
                port=coord.port,
                user='postgres',
                password='TrixSuperSecure2024!',
                database='postgres'
            )
            
            try:
                # Verificar se banco já existe
                existing = await conn.fetchval(
                    "SELECT 1 FROM pg_database WHERE datname = $1",
                    database_name
                )
                
                if not existing:
                    await conn.execute(f'CREATE DATABASE "{database_name}";')
                    logger.info(f"✅ Banco {database_name} criado")
                else:
                    logger.info(f"ℹ️ Banco {database_name} já existe")
                
            finally:
                await conn.close()

if __name__ == "__main__":
    async def main():
        manager = CitusClusterManager()
        await manager.setup_cluster()
    
    asyncio.run(main())
