FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    libpq-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy shared_lib for shared components (build context must include shared_lib)
# Note: Build from parent directory: docker build -f tenant_service/docker/Dockerfile .
COPY shared_lib/ ./microservices/core/shared_lib/

# Copy requirements and install Python dependencies
COPY tenant_service/requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY tenant_service/app/ ./app/
COPY tenant_service/migrations/ ./migrations/
COPY tenant_service/alembic.ini .

# Create non-root user
RUN useradd --create-home --shell /bin/bash app \
    && chown -R app:app /app
USER app

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Run the application
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
