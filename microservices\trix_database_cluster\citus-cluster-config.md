# 🚀 Configuração Citus Coordenado por Microserviço - Trix

## 📋 Visão Geral

Este documento detalha a configuração de um cluster Citus distribuído onde cada microserviço tem seu próprio nó coordenador, permitindo escalabilidade horizontal e isolamento de dados por domínio de negócio.

## 🏗️ Arquitetura Citus Distribuída

### Estratégia Híbrida: Coordenadores por Domínio + Workers Compartilhados

```mermaid
graph TB
    subgraph "Core Services Coordinator"
        CC[Citus Coordinator - Core]
        CC --> CW1[Worker 1]
        CC --> CW2[Worker 2]
    end
    
    subgraph "Tenant Services Coordinator"
        TC[Citus Coordinator - Tenant]
        TC --> TW1[Worker 3]
        TC --> TW2[Worker 4]
    end
    
    subgraph "Shared Services Coordinator"
        SC[Citus Coordinator - Shared]
        SC --> SW1[Worker 5]
        SC --> SW2[Worker 6]
    end
    
    subgraph "Microserviços"
        AUTH[auth_service] --> CC
        USER[user_service] --> CC
        TENANT[tenant_service] --> CC
        
        REST[restaurant_module] --> TC
        CONS[consultancy_module] --> TC
        SHOP[shop_module] --> TC
        
        HR[hr_module] --> SC
        CRM[crm_module] --> SC
        EMAIL[email_module] --> SC
    end
```

## 🎯 Configuração por Domínio

### 1. Core Services Coordinator (Porta 5432)
**Microserviços**: auth, user, tenant, core, i18n, ghost_function
**Características**:
- Alta consistência (ACID completo)
- Baixa latência para autenticação
- Replicação síncrona para dados críticos

### 2. Tenant Services Coordinator (Porta 5433)
**Microserviços**: restaurant, consultancy, shop
**Características**:
- Particionamento por tenant_id
- Escalabilidade horizontal por tenant
- Isolamento de dados por cliente

### 3. Shared Services Coordinator (Porta 5434)
**Microserviços**: hr, crm, email, financial, notifications
**Características**:
- Particionamento por funcionalidade
- Balanceamento de carga entre workers
- Otimização para analytics e relatórios

## 📦 Estrutura Docker Compose Citus

### Arquivo: `docker-compose.citus.yml`

```yaml
version: '3.9'

services:
  # ============================================================================
  # CORE SERVICES COORDINATOR
  # ============================================================================
  trix-citus-core-coordinator:
    image: citusdata/citus:12.1
    container_name: trix-citus-core-coordinator
    hostname: trix-citus-core-coordinator
    restart: unless-stopped
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: TrixSuperSecure2024!
      CITUS_HOST: trix-citus-core-coordinator
      CITUS_PORT: 5432
      PGUSER: postgres
      PGPASSWORD: TrixSuperSecure2024!
    ports:
      - "5432:5432"
    volumes:
      - citus_core_coordinator_data:/var/lib/postgresql/data
      - ./citus-config/core-coordinator.conf:/etc/postgresql/postgresql.conf:ro
      - ./citus-config/pg_hba.conf:/etc/postgresql/pg_hba.conf:ro
      - ./citus-init-scripts/core:/docker-entrypoint-initdb.d:ro
    command: >
      postgres
      -c shared_preload_libraries=citus
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c wal_level=replica
      -c max_wal_senders=10
      -c max_replication_slots=10
    networks:
      - trix-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d postgres"]
      interval: 30s
      timeout: 10s
      retries: 3
    labels:
      - "trix.citus.role=coordinator"
      - "trix.citus.domain=core"

  # ============================================================================
  # TENANT SERVICES COORDINATOR
  # ============================================================================
  trix-citus-tenant-coordinator:
    image: citusdata/citus:12.1
    container_name: trix-citus-tenant-coordinator
    hostname: trix-citus-tenant-coordinator
    restart: unless-stopped
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: TrixSuperSecure2024!
      CITUS_HOST: trix-citus-tenant-coordinator
      CITUS_PORT: 5432
      PGUSER: postgres
      PGPASSWORD: TrixSuperSecure2024!
    ports:
      - "5433:5432"
    volumes:
      - citus_tenant_coordinator_data:/var/lib/postgresql/data
      - ./citus-config/tenant-coordinator.conf:/etc/postgresql/postgresql.conf:ro
      - ./citus-config/pg_hba.conf:/etc/postgresql/pg_hba.conf:ro
      - ./citus-init-scripts/tenant:/docker-entrypoint-initdb.d:ro
    command: >
      postgres
      -c shared_preload_libraries=citus
      -c max_connections=300
      -c shared_buffers=512MB
      -c effective_cache_size=2GB
      -c wal_level=replica
      -c max_wal_senders=10
      -c max_replication_slots=10
    networks:
      - trix-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d postgres"]
      interval: 30s
      timeout: 10s
      retries: 3
    labels:
      - "trix.citus.role=coordinator"
      - "trix.citus.domain=tenant"

  # ============================================================================
  # SHARED SERVICES COORDINATOR
  # ============================================================================
  trix-citus-shared-coordinator:
    image: citusdata/citus:12.1
    container_name: trix-citus-shared-coordinator
    hostname: trix-citus-shared-coordinator
    restart: unless-stopped
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: TrixSuperSecure2024!
      CITUS_HOST: trix-citus-shared-coordinator
      CITUS_PORT: 5432
      PGUSER: postgres
      PGPASSWORD: TrixSuperSecure2024!
    ports:
      - "5434:5432"
    volumes:
      - citus_shared_coordinator_data:/var/lib/postgresql/data
      - ./citus-config/shared-coordinator.conf:/etc/postgresql/postgresql.conf:ro
      - ./citus-config/pg_hba.conf:/etc/postgresql/pg_hba.conf:ro
      - ./citus-init-scripts/shared:/docker-entrypoint-initdb.d:ro
    command: >
      postgres
      -c shared_preload_libraries=citus
      -c max_connections=250
      -c shared_buffers=384MB
      -c effective_cache_size=1.5GB
      -c wal_level=replica
      -c max_wal_senders=10
      -c max_replication_slots=10
    networks:
      - trix-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d postgres"]
      interval: 30s
      timeout: 10s
      retries: 3
    labels:
      - "trix.citus.role=coordinator"
      - "trix.citus.domain=shared"

  # ============================================================================
  # CITUS WORKERS (Compartilhados entre coordenadores)
  # ============================================================================
  trix-citus-worker-1:
    image: citusdata/citus:12.1
    container_name: trix-citus-worker-1
    hostname: trix-citus-worker-1
    restart: unless-stopped
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: TrixSuperSecure2024!
      PGUSER: postgres
      PGPASSWORD: TrixSuperSecure2024!
    ports:
      - "5435:5432"
    volumes:
      - citus_worker1_data:/var/lib/postgresql/data
      - ./citus-config/worker.conf:/etc/postgresql/postgresql.conf:ro
      - ./citus-config/pg_hba.conf:/etc/postgresql/pg_hba.conf:ro
    command: >
      postgres
      -c shared_preload_libraries=citus
      -c max_connections=100
      -c shared_buffers=512MB
      -c effective_cache_size=2GB
    networks:
      - trix-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d postgres"]
      interval: 30s
      timeout: 10s
      retries: 3
    labels:
      - "trix.citus.role=worker"
      - "trix.citus.worker.id=1"

  trix-citus-worker-2:
    image: citusdata/citus:12.1
    container_name: trix-citus-worker-2
    hostname: trix-citus-worker-2
    restart: unless-stopped
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: TrixSuperSecure2024!
      PGUSER: postgres
      PGPASSWORD: TrixSuperSecure2024!
    ports:
      - "5436:5432"
    volumes:
      - citus_worker2_data:/var/lib/postgresql/data
      - ./citus-config/worker.conf:/etc/postgresql/postgresql.conf:ro
      - ./citus-config/pg_hba.conf:/etc/postgresql/pg_hba.conf:ro
    command: >
      postgres
      -c shared_preload_libraries=citus
      -c max_connections=100
      -c shared_buffers=512MB
      -c effective_cache_size=2GB
    networks:
      - trix-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d postgres"]
      interval: 30s
      timeout: 10s
      retries: 3
    labels:
      - "trix.citus.role=worker"
      - "trix.citus.worker.id=2"

networks:
  trix-network:
    external: true

volumes:
  citus_core_coordinator_data:
    driver: local
    name: trix_citus_core_coordinator_data
  citus_tenant_coordinator_data:
    driver: local
    name: trix_citus_tenant_coordinator_data
  citus_shared_coordinator_data:
    driver: local
    name: trix_citus_shared_coordinator_data
  citus_worker1_data:
    driver: local
    name: trix_citus_worker1_data
  citus_worker2_data:
    driver: local
    name: trix_citus_worker2_data
```

## 🔧 Configurações Específicas

### Core Coordinator (postgresql.conf)
```ini
# Configurações específicas para Core Services
shared_preload_libraries = 'citus'
max_connections = 200
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB

# Configurações Citus
citus.shard_count = 32
citus.shard_replication_factor = 2
citus.use_secondary_nodes = 'never'
citus.cluster_name = 'trix-core'

# Configurações de replicação
wal_level = replica
max_wal_senders = 10
max_replication_slots = 10
hot_standby = on

# Configurações de performance para dados críticos
synchronous_commit = on
fsync = on
full_page_writes = on
```

### Tenant Coordinator (postgresql.conf)
```ini
# Configurações específicas para Tenant Services
shared_preload_libraries = 'citus'
max_connections = 300
shared_buffers = 512MB
effective_cache_size = 2GB
work_mem = 8MB
maintenance_work_mem = 128MB

# Configurações Citus para multi-tenant
citus.shard_count = 64
citus.shard_replication_factor = 2
citus.use_secondary_nodes = 'always'
citus.cluster_name = 'trix-tenant'

# Otimizações para particionamento por tenant
citus.enable_repartition_joins = on
citus.enable_fast_path_router_planner = on
citus.multi_shard_modify_mode = 'parallel'
```

## 📊 Mapeamento de Microserviços para Coordenadores

### Core Services (Porta 5432)
```python
CORE_SERVICES_MAPPING = {
    'auth_service': {
        'coordinator': 'trix-citus-core-coordinator:5432',
        'database': 'auth_db',
        'distribution_key': 'user_id',
        'tables': ['users', 'auth_sessions', 'auth_tokens', 'roles', 'permissions']
    },
    'user_service': {
        'coordinator': 'trix-citus-core-coordinator:5432',
        'database': 'users_db',
        'distribution_key': 'user_id',
        'tables': ['user_profiles', 'user_preferences', 'user_addresses']
    },
    'tenant_service': {
        'coordinator': 'trix-citus-core-coordinator:5432',
        'database': 'tenants_db',
        'distribution_key': 'tenant_id',
        'tables': ['tenants', 'tenant_settings', 'tenant_user_associations']
    }
}
```

### Tenant Services (Porta 5433)
```python
TENANT_SERVICES_MAPPING = {
    'restaurant_module': {
        'coordinator': 'trix-citus-tenant-coordinator:5433',
        'database': 'restaurant_db',
        'distribution_key': 'tenant_id',
        'tables': ['restaurant_menus', 'restaurant_orders', 'restaurant_tables', 'restaurant_inventories']
    },
    'consultancy_module': {
        'coordinator': 'trix-citus-tenant-coordinator:5433',
        'database': 'consultancy_db',
        'distribution_key': 'tenant_id',
        'tables': ['consultancy_cases', 'consultancy_billings', 'consultancy_workflows']
    }
}
```

### Shared Services (Porta 5434)
```python
SHARED_SERVICES_MAPPING = {
    'hr_module': {
        'coordinator': 'trix-citus-shared-coordinator:5434',
        'database': 'hr_db',
        'distribution_key': 'tenant_id',
        'tables': ['hr_employees', 'hr_departments', 'hr_lms_courses']
    },
    'crm_module': {
        'coordinator': 'trix-citus-shared-coordinator:5434',
        'database': 'crm_db',
        'distribution_key': 'tenant_id',
        'tables': ['crm_accounts', 'crm_contacts', 'crm_interactions']
    }
}
```
