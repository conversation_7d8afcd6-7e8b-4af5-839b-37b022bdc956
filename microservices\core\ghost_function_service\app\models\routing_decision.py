"""
Ghost Function Service - Routing Decision Model

Modelo para registrar decisões de roteamento do proxy inteligente.
Armazena histórico de decisões para análise e otimização.

Author: Trix Platform Team
"""

import enum
from datetime import datetime
from typing import Dict, Optional
from uuid import UUID, uuid4


from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    <PERSON>umn,
    DateTime,
    Float,
    Integer,
    String,
    Text,
)
from sqlalchemy.dialects.postgresql import JSON<PERSON>, UUID as PGUUID
from sqlalchemy.ext.hybrid import hybrid_property

from app.core.database import Base
from microservices.core.shared_lib.config import get_logger

logger = get_logger("ghost-function-service")


class DecisionType(enum.Enum):
    """Tipos de decisão de roteamento."""
    ROUTE_TO_ORIGINAL = "route_to_original"
    ROUTE_TO_GHOST = "route_to_ghost"
    FAILOVER_TO_GHOST = "failover_to_ghost"
    RECOVERY_TO_ORIGINAL = "recovery_to_original"


class DecisionReason(enum.Enum):
    """Motivos para decisões de roteamento."""
    SERVICE_HEALTHY = "service_healthy"
    SERVICE_DOWN = "service_down"
    SERVICE_DEGRADED = "service_degraded"
    HIGH_LATENCY = "high_latency"
    ERROR_THRESHOLD_EXCEEDED = "error_threshold_exceeded"
    MANUAL_OVERRIDE = "manual_override"
    CIRCUIT_BREAKER_OPEN = "circuit_breaker_open"
    LOAD_BALANCING = "load_balancing"
    MAINTENANCE_MODE = "maintenance_mode"


class RoutingDecision(Base):
    """
    Registra decisões de roteamento do proxy inteligente.
    
    Armazena o histórico completo de decisões para análise,
    otimização e auditoria do comportamento do proxy.
    """
    __tablename__ = 'routing_decisions'

    # === IDENTIFICAÇÃO ===
    id: UUID = Column(PGUUID(as_uuid=True), primary_key=True, default=uuid4)
    service_name: str = Column(String(100), index=True, nullable=False)
    decision_timestamp: datetime = Column(DateTime, default=datetime.utcnow, index=True, nullable=False)
    
    # === DECISÃO ===
    decision_type: str = Column(String(50), nullable=False)  # DecisionType enum
    decision_reason: str = Column(String(50), nullable=False)  # DecisionReason enum
    target_selected: str = Column(String(20), nullable=False)  # "original" or "ghost"
    confidence_score: float = Column(Float, default=1.0)  # 0.0 to 1.0
    
    # === CONTEXTO DA DECISÃO ===
    request_path: Optional[str] = Column(String(500))
    request_method: Optional[str] = Column(String(10))
    client_ip: Optional[str] = Column(String(45))  # IPv6 compatible
    user_agent: Optional[str] = Column(Text)
    
    # === MÉTRICAS NO MOMENTO DA DECISÃO ===
    service_response_time_ms: Optional[int] = Column(Integer)
    service_error_rate: Optional[float] = Column(Float)
    service_cpu_usage: Optional[float] = Column(Float)
    service_memory_usage: Optional[float] = Column(Float)
    active_connections: Optional[int] = Column(Integer)
    
    # === INFORMAÇÕES DO GHOST ===
    ghost_version_used: Optional[str] = Column(String(50))
    ghost_last_sync: Optional[datetime] = Column(DateTime)
    ghost_data_freshness_minutes: Optional[int] = Column(Integer)
    
    # === RESULTADO DA DECISÃO ===
    execution_successful: Optional[bool] = Column(Boolean)
    actual_response_time_ms: Optional[int] = Column(Integer)
    actual_status_code: Optional[int] = Column(Integer)
    error_message: Optional[str] = Column(Text)
    
    # === METADADOS ===
    decision_duration_ms: Optional[int] = Column(Integer)  # Tempo para tomar a decisão
    circuit_breaker_state: Optional[str] = Column(String(20))
    load_balancer_weight: Optional[float] = Column(Float)
    
    # === DADOS ADICIONAIS ===
    additional_data: Dict = Column(JSONB, default={})
    
    @hybrid_property
    def was_ghost_used(self) -> bool:
        """Retorna True se foi usado o serviço fantasma."""
        return self.target_selected == "ghost"
    
    @hybrid_property
    def was_successful(self) -> bool:
        """Retorna True se a execução foi bem-sucedida."""
        return self.execution_successful is True
    
    @hybrid_property
    def decision_age_minutes(self) -> Optional[float]:
        """Retorna a idade da decisão em minutos."""
        if not self.decision_timestamp:
            return None
        
        delta = datetime.utcnow() - self.decision_timestamp
        return delta.total_seconds() / 60
    
    @hybrid_property
    def is_recent_decision(self) -> bool:
        """Retorna True se a decisão foi tomada nos últimos 5 minutos."""
        age = self.decision_age_minutes
        return age is not None and age <= 5
    
    def to_dict(self) -> Dict:
        """
        Converte o modelo para dicionário.
        
        Returns:
            Dict: Representação em dicionário
        """
        return {
            "id": str(self.id),
            "service_name": self.service_name,
            "decision_timestamp": self.decision_timestamp.isoformat(),
            "decision_type": self.decision_type,
            "decision_reason": self.decision_reason,
            "target_selected": self.target_selected,
            "confidence_score": self.confidence_score,
            "request_path": self.request_path,
            "request_method": self.request_method,
            "client_ip": self.client_ip,
            "service_response_time_ms": self.service_response_time_ms,
            "service_error_rate": self.service_error_rate,
            "ghost_version_used": self.ghost_version_used,
            "ghost_last_sync": self.ghost_last_sync.isoformat() if self.ghost_last_sync else None,
            "execution_successful": self.execution_successful,
            "actual_response_time_ms": self.actual_response_time_ms,
            "actual_status_code": self.actual_status_code,
            "decision_duration_ms": self.decision_duration_ms,
            "circuit_breaker_state": self.circuit_breaker_state,
            "additional_data": self.additional_data
        }
    
    def calculate_decision_quality_score(self) -> float:
        """
        Calcula um score de qualidade da decisão baseado no resultado.
        
        Returns:
            float: Score de 0.0 a 1.0
        """
        if not self.execution_successful:
            return 0.0
        
        score = self.confidence_score
        
        # Penalizar se usou ghost quando original estava disponível
        if (self.target_selected == "ghost" and 
            self.decision_reason not in [DecisionReason.SERVICE_DOWN.value, 
                                       DecisionReason.SERVICE_DEGRADED.value,
                                       DecisionReason.ERROR_THRESHOLD_EXCEEDED.value]):
            score *= 0.8
        
        # Bonificar se a resposta foi rápida
        if self.actual_response_time_ms and self.actual_response_time_ms < 500:
            score *= 1.1
        
        # Penalizar se a resposta foi lenta
        if self.actual_response_time_ms and self.actual_response_time_ms > 2000:
            score *= 0.7
        
        # Bonificar se o status code foi de sucesso
        if self.actual_status_code and 200 <= self.actual_status_code < 300:
            score *= 1.05
        
        return min(score, 1.0)
    
    @classmethod
    def create_decision_record(
        cls,
        service_name: str,
        decision_type: DecisionType,
        decision_reason: DecisionReason,
        target_selected: str,
        confidence_score: float = 1.0,
        **kwargs
    ) -> "RoutingDecision":
        """
        Cria um novo registro de decisão.
        
        Args:
            service_name: Nome do serviço
            decision_type: Tipo da decisão
            decision_reason: Motivo da decisão
            target_selected: Target selecionado
            confidence_score: Score de confiança
            **kwargs: Campos adicionais
            
        Returns:
            RoutingDecision: Nova instância
        """
        return cls(
            service_name=service_name,
            decision_type=decision_type.value,
            decision_reason=decision_reason.value,
            target_selected=target_selected,
            confidence_score=confidence_score,
            **kwargs
        )
    
    def __repr__(self) -> str:
        return (
            f"<RoutingDecision("
            f"service='{self.service_name}', "
            f"decision='{self.decision_type}', "
            f"target='{self.target_selected}', "
            f"reason='{self.decision_reason}', "
            f"timestamp='{self.decision_timestamp}'"
            f")>"
        )
