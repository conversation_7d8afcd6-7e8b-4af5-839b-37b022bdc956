"""
Media System Specific Metrics
==============================

Métricas específicas do Media System, complementando as métricas
compartilhadas da shared_lib.

✅ MIGRAÇÃO CONCLUÍDA - Usando shared_lib para infraestrutura base
"""

from prometheus_client import Counter, Histogram, Gauge, Info
from microservices.core.shared_lib.infrastructure.observability import MetricsCollector

from ..config.settings import settings


# ===== MÉTRICAS ESPECÍFICAS DO MEDIA SYSTEM =====

# Upload/Download Metrics
media_uploads_counter = Counter(
    'media_uploads_total',
    'Total number of media uploads',
    ['tenant_id', 'context', 'media_type', 'status']
)

media_downloads_counter = Counter(
    'media_downloads_total', 
    'Total number of media downloads',
    ['tenant_id', 'context', 'media_type', 'access_type']
)

media_upload_size_histogram = Histogram(
    'media_upload_size_bytes',
    'Size of uploaded media files in bytes',
    ['tenant_id', 'context', 'media_type'],
    buckets=[1024, 10240, 102400, 1048576, 10485760, 104857600, 1073741824]  # 1KB to 1GB
)

# Processing Metrics
processing_duration_histogram = Histogram(
    'media_processing_duration_seconds',
    'Time spent processing media files',
    ['tenant_id', 'job_type', 'status'],
    buckets=[1, 5, 10, 30, 60, 300, 600, 1800, 3600]  # 1s to 1h
)

processing_queue_size_gauge = Gauge(
    'media_processing_queue_size',
    'Number of jobs in processing queue',
    ['job_type', 'priority']
)

processing_jobs_counter = Counter(
    'media_processing_jobs_total',
    'Total number of processing jobs',
    ['tenant_id', 'job_type', 'status']
)

# Storage Metrics
storage_usage_gauge = Gauge(
    'media_storage_usage_bytes',
    'Storage usage in bytes',
    ['tenant_id', 'context', 'region']
)

storage_operations_counter = Counter(
    'media_storage_operations_total',
    'Total storage operations',
    ['operation', 'region', 'status']  # operation: upload, download, delete
)

# CDN Metrics
cdn_hit_ratio_gauge = Gauge(
    'media_cdn_hit_ratio',
    'CDN cache hit ratio',
    ['region', 'content_type']
)

cdn_requests_counter = Counter(
    'media_cdn_requests_total',
    'Total CDN requests',
    ['region', 'status', 'cache_status']  # cache_status: hit, miss, bypass
)

cdn_bandwidth_counter = Counter(
    'media_cdn_bandwidth_bytes_total',
    'Total CDN bandwidth usage',
    ['region', 'content_type']
)

# Error Metrics
media_errors_counter = Counter(
    'media_errors_total',
    'Total media system errors',
    ['error_type', 'operation', 'tenant_id']
)

# Security Metrics
media_security_events_counter = Counter(
    'media_security_events_total',
    'Total security events',
    ['event_type', 'threat_level', 'action_taken']
)

antivirus_scans_counter = Counter(
    'media_antivirus_scans_total',
    'Total antivirus scans',
    ['result', 'scanner']  # result: clean, infected, error
)

# AI/OCR Metrics
ai_processing_counter = Counter(
    'media_ai_processing_total',
    'Total AI processing operations',
    ['ai_type', 'status']  # ai_type: ocr, classification, moderation
)

ocr_accuracy_gauge = Gauge(
    'media_ocr_accuracy_percentage',
    'OCR accuracy percentage',
    ['language', 'document_type']
)

# Quota Metrics
quota_usage_gauge = Gauge(
    'media_quota_usage_percentage',
    'Quota usage percentage',
    ['tenant_id', 'context']
)

quota_violations_counter = Counter(
    'media_quota_violations_total',
    'Total quota violations',
    ['tenant_id', 'context', 'violation_type']
)

# Performance Metrics
media_response_time_histogram = Histogram(
    'media_response_time_seconds',
    'Media API response time',
    ['endpoint', 'method', 'status'],
    buckets=[0.1, 0.25, 0.5, 1, 2.5, 5, 10]
)

thumbnail_generation_time_histogram = Histogram(
    'media_thumbnail_generation_seconds',
    'Time to generate thumbnails',
    ['size', 'format'],
    buckets=[0.5, 1, 2, 5, 10, 30]
)


class MediaMetricsManager:
    """Gerenciador de métricas específicas do Media System."""
    
    def __init__(self):
        # Usar MetricsCollector da shared_lib para métricas comuns
        self.shared_metrics = MetricsCollector(
            service_name=settings.SERVICE_NAME,
            service_version="2.0.0",
            environment=settings.ENVIRONMENT
        )
    
    def record_upload(self, tenant_id: str, context: str, media_type: str, 
                     file_size: int, status: str = "success"):
        """Registra métricas de upload."""
        media_uploads_counter.labels(
            tenant_id=tenant_id,
            context=context,
            media_type=media_type,
            status=status
        ).inc()
        
        if status == "success":
            media_upload_size_histogram.labels(
                tenant_id=tenant_id,
                context=context,
                media_type=media_type
            ).observe(file_size)
    
    def record_download(self, tenant_id: str, context: str, media_type: str, access_type: str):
        """Registra métricas de download."""
        media_downloads_counter.labels(
            tenant_id=tenant_id,
            context=context,
            media_type=media_type,
            access_type=access_type
        ).inc()
    
    def record_processing(self, tenant_id: str, job_type: str, duration: float, status: str):
        """Registra métricas de processamento."""
        processing_duration_histogram.labels(
            tenant_id=tenant_id,
            job_type=job_type,
            status=status
        ).observe(duration)
        
        processing_jobs_counter.labels(
            tenant_id=tenant_id,
            job_type=job_type,
            status=status
        ).inc()
    
    def update_storage_usage(self, tenant_id: str, context: str, region: str, bytes_used: int):
        """Atualiza métricas de uso de storage."""
        storage_usage_gauge.labels(
            tenant_id=tenant_id,
            context=context,
            region=region
        ).set(bytes_used)
    
    def record_error(self, error_type: str, operation: str, tenant_id: str):
        """Registra métricas de erro."""
        media_errors_counter.labels(
            error_type=error_type,
            operation=operation,
            tenant_id=tenant_id
        ).inc()
    
    def update_cdn_metrics(self, region: str, content_type: str, hit_ratio: float):
        """Atualiza métricas de CDN."""
        cdn_hit_ratio_gauge.labels(
            region=region,
            content_type=content_type
        ).set(hit_ratio)
    
    def record_security_event(self, event_type: str, threat_level: str, action_taken: str):
        """Registra eventos de segurança."""
        media_security_events_counter.labels(
            event_type=event_type,
            threat_level=threat_level,
            action_taken=action_taken
        ).inc()


# Instância global do gerenciador de métricas
media_metrics_manager = MediaMetricsManager()
