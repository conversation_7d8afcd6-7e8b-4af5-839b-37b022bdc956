"""
Media System Settings - Integração com Shared Lib
==================================================

Configurações do Media System utilizando componentes compartilhados da shared_lib
para garantir consistência e reduzir duplicação entre microserviços.

✅ MIGRAÇÃO CONCLUÍDA - Usando configurações da shared_lib
"""

from pydantic import Field
from microservices.core.shared_lib.config.media_config import MediaServiceSettings


class MediaSystemSettings(MediaServiceSettings):
    """
    Configurações do Media System com integração às configurações compartilhadas.

    Herda configurações comuns de:
    - MediaServiceSettings: Configurações específicas do Media System
    - VaultBaseSettings: Configurações do HashiCorp Vault
    - Configurações de ambiente (DEBUG, LOG_LEVEL, etc.)
    """

    # ===== CONFIGURAÇÕES ESPECÍFICAS DO MEDIA SYSTEM =====

    # API Configuration
    API_V1_PREFIX: str = Field(default="/api/v1", env="API_V1_PREFIX")
    DOCS_URL: str = Field(default="/docs", env="DOCS_URL")
    REDOC_URL: str = Field(default="/redoc", env="REDOC_URL")

    # CORS Configuration
    CORS_ORIGINS: list = Field(default=["*"], env="CORS_ORIGINS")
    CORS_METHODS: list = Field(default=["*"], env="CORS_METHODS")
    CORS_HEADERS: list = Field(default=["*"], env="CORS_HEADERS")

    # Rate Limiting (específico do Media System)
    RATE_LIMIT_ENABLED: bool = Field(default=True, env="MEDIA_RATE_LIMIT_ENABLED")
    RATE_LIMIT_REQUESTS: int = Field(default=1000, env="MEDIA_RATE_LIMIT_REQUESTS")
    RATE_LIMIT_WINDOW: int = Field(default=3600, env="MEDIA_RATE_LIMIT_WINDOW")  # 1 hour

    # Service Discovery
    CONSUL_ENABLED: bool = Field(default=False, env="CONSUL_ENABLED")
    CONSUL_HOST: str = Field(default="consul", env="CONSUL_HOST")
    CONSUL_PORT: int = Field(default=8500, env="CONSUL_PORT")

    # ===== CONFIGURAÇÕES COMPARTILHADAS (via shared_lib) =====
    # Todas as configurações de storage, processing, AI, CDN e security
    # são herdadas de MediaServiceSettings da shared_lib

    class Config:
        env_file = ".env"
        case_sensitive = True
        extra = "forbid"


# Global settings instance
settings = MediaSystemSettings()
