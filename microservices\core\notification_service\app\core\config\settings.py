"""
Enterprise-grade settings for Notification Service.
Supports Citus Data, Vault, Service Mesh, and full observability stack.
"""

import os
from typing import List, Optional, Dict, Any
from pydantic import Field, validator
from pydantic_settings import BaseSettings
from functools import lru_cache


class DatabaseSettings(BaseSettings):
    """Citus Data database configuration."""
    
    # Citus Data Coordinator
    coordinator_url: str = Field(
        default="postgresql+asyncpg://notifications_user:notifications_pass@trix-postgres-citus-coordinator:5432/notifications_db",
        env="DATABASE_URL"
    )
    
    # Connection pooling with PgBouncer
    pool_size: int = Field(default=20, env="DATABASE_POOL_SIZE")
    max_overflow: int = Field(default=30, env="DATABASE_MAX_OVERFLOW")
    pool_timeout: int = Field(default=30, env="DATABASE_POOL_TIMEOUT")
    pool_recycle: int = Field(default=3600, env="DATABASE_POOL_RECYCLE")
    
    # Citus Data specific settings
    shard_count: int = Field(default=32, env="CITUS_SHARD_COUNT")
    replication_factor: int = Field(default=2, env="CITUS_REPLICATION_FACTOR")
    
    # Read replicas for query optimization
    read_replica_urls: List[str] = Field(
        default_factory=lambda: [
            "postgresql+asyncpg://notifications_user:notifications_pass@trix-postgres-read-replica-1:5432/notifications_db",
            "postgresql+asyncpg://notifications_user:notifications_pass@trix-postgres-read-replica-2:5432/notifications_db"
        ],
        env="DATABASE_READ_REPLICAS"
    )
    
    class Config:
        env_prefix = "DATABASE_"


class MessagingSettings(BaseSettings):
    """Event-driven messaging configuration."""
    
    # Apache Kafka for critical events
    kafka_brokers: List[str] = Field(
        default_factory=lambda: ["trix-kafka-0:9092", "trix-kafka-1:9092", "trix-kafka-2:9092"],
        env="KAFKA_BROKERS"
    )
    kafka_topic_notifications: str = Field(default="notification-events", env="KAFKA_TOPIC_NOTIFICATIONS")
    kafka_topic_delivery: str = Field(default="notification-delivery", env="KAFKA_TOPIC_DELIVERY")
    kafka_consumer_group: str = Field(default="notification-service", env="KAFKA_CONSUMER_GROUP")
    
    # RabbitMQ for fast messaging
    rabbitmq_url: str = Field(
        default="amqp://notifications_user:notifications_pass@trix-rabbitmq:5672/",
        env="RABBITMQ_URL"
    )
    rabbitmq_exchange: str = Field(default="notification-updates", env="RABBITMQ_EXCHANGE")
    
    # Redis Streams for real-time updates
    redis_cluster_nodes: List[str] = Field(
        default_factory=lambda: [
            "trix-redis-cluster-0:6379",
            "trix-redis-cluster-1:6379", 
            "trix-redis-cluster-2:6379"
        ],
        env="REDIS_CLUSTER_NODES"
    # Configurações do Vault herdadas de StandardVaultConfig
    # VAULT_URL, VAULT_TOKEN, VAULT_ENABLED, etc. já estão definidas
    
    )
    redis_password: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    redis_db: int = Field(default=17, env="REDIS_DB")
    
    class Config:
        env_prefix = "MESSAGING_"


class SecuritySettings(BaseSettings):
    """Enterprise security configuration."""    vault_addr: str = Field(default="https://vault.trix.local", env="VAULT_ADDR")
    vault_role: str = Field(default="notification-service", env="VAULT_ROLE")
    vault_token: Optional[str] = Field(default=None, env="VAULT_TOKEN")
    vault_namespace: Optional[str] = Field(default=None, env="VAULT_NAMESPACE")
    
    # Open Policy Agent
    opa_endpoint: str = Field(default="http://opa:8181", env="OPA_ENDPOINT")
    opa_policy_path: str = Field(default="notification_permissions", env="OPA_POLICY_PATH")
    
    # Service mesh mTLS
    mtls_enabled: bool = Field(default=True, env="MTLS_ENABLED")
    cert_path: str = Field(default="/etc/ssl/certs", env="CERT_PATH")
    key_path: str = Field(default="/etc/ssl/private", env="KEY_PATH")
    ca_path: str = Field(default="/etc/ssl/ca", env="CA_PATH")
    
    # JWT settings
    jwt_algorithm: str = Field(default="RS256", env="JWT_ALGORITHM")
    jwt_public_key_path: str = Field(default="/etc/ssl/jwt/public.pem", env="JWT_PUBLIC_KEY_PATH")
    
    class Config:
        env_prefix = "SECURITY_"


class ObservabilitySettings(BaseSettings):
    """Enterprise observability configuration."""
    
    # Prometheus metrics
    prometheus_enabled: bool = Field(default=True, env="PROMETHEUS_ENABLED")
    prometheus_port: int = Field(default=8019, env="PROMETHEUS_PORT")
    prometheus_path: str = Field(default="/metrics", env="PROMETHEUS_PATH")
    
    # Jaeger distributed tracing
    jaeger_enabled: bool = Field(default=True, env="JAEGER_ENABLED")
    jaeger_endpoint: str = Field(
        default="http://jaeger-collector:14268/api/traces",
        env="JAEGER_ENDPOINT"
    )
    jaeger_service_name: str = Field(default="notification-service", env="JAEGER_SERVICE_NAME")
    jaeger_sample_rate: float = Field(default=0.1, env="JAEGER_SAMPLE_RATE")
    
    # ELK Stack logging
    elk_enabled: bool = Field(default=True, env="ELK_ENABLED")
    elasticsearch_url: str = Field(default="http://elasticsearch:9200", env="ELASTICSEARCH_URL")
    logstash_host: str = Field(default="logstash", env="LOGSTASH_HOST")
    logstash_port: int = Field(default=5000, env="LOGSTASH_PORT")
    
    # Structured logging
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_format: str = Field(default="json", env="LOG_FORMAT")
    
    class Config:
        env_prefix = "OBSERVABILITY_"


class ServiceSettings(BaseSettings):
    """Core service configuration."""
    
    # Service identity
    service_name: str = Field(default="notification-service", env="SERVICE_NAME")
    service_version: str = Field(default="2.0.0", env="SERVICE_VERSION")
    service_port: int = Field(default=8019, env="SERVICE_PORT")
    websocket_port: int = Field(default=8080, env="WEBSOCKET_PORT")
    
    # Environment
    environment: str = Field(default="development", env="ENVIRONMENT")
    debug: bool = Field(default=False, env="DEBUG")
    
    # Performance settings
    max_workers: int = Field(default=4, env="MAX_WORKERS")
    worker_timeout: int = Field(default=30, env="WORKER_TIMEOUT")
    
    # Notification limits for scale
    max_notifications_per_user_per_hour: int = Field(default=1000, env="MAX_NOTIFICATIONS_PER_USER_PER_HOUR")
    notification_retention_days: int = Field(default=90, env="NOTIFICATION_RETENTION_DAYS")
    batch_notification_size: int = Field(default=1000, env="BATCH_NOTIFICATION_SIZE")
    notification_timeout_seconds: int = Field(default=30, env="NOTIFICATION_TIMEOUT_SECONDS")
    
    # WebSocket settings for massive scale
    websocket_max_connections: int = Field(default=1000000, env="WEBSOCKET_MAX_CONNECTIONS")
    websocket_heartbeat_interval: int = Field(default=30, env="WEBSOCKET_HEARTBEAT_INTERVAL")
    
    class Config:
        env_prefix = "SERVICE_"


class IntegrationSettings(BaseSettings):
    """Microservices integration configuration."""
    
    # Core services
    auth_service_url: str = Field(default="http://trix-core-auth:8001", env="AUTH_SERVICE_URL")
    user_service_url: str = Field(default="http://trix-core-user:8002", env="USER_SERVICE_URL")
    tenant_service_url: str = Field(default="http://trix-core-tenant:8003", env="TENANT_SERVICE_URL")
    core_service_url: str = Field(default="http://trix-core-core:8004", env="CORE_SERVICE_URL")
    supplier_service_url: str = Field(default="http://trix-core-supplier:8005", env="SUPPLIER_SERVICE_URL")
    i18n_service_url: str = Field(default="http://trix-core-i18n:8018", env="I18N_SERVICE_URL")
    
    # Business services
    commerce_service_url: str = Field(default="http://trix-core-commerce:8020", env="COMMERCE_SERVICE_URL")
    media_service_url: str = Field(default="http://trix-core-media:8021", env="MEDIA_SERVICE_URL")
    payment_service_url: str = Field(default="http://trix-core-payment:8022", env="PAYMENT_SERVICE_URL")
    cdn_service_url: str = Field(default="http://trix-core-cdn:8023", env="CDN_SERVICE_URL")
    domain_service_url: str = Field(default="http://trix-core-domain:8015", env="DOMAIN_SERVICE_URL")
    
    # Service authentication
    service_client_id: str = Field(default="notification_service", env="SERVICE_CLIENT_ID")
    service_client_secret: str = Field(default="notification_service_secret", env="SERVICE_CLIENT_SECRET")
    
    # Request timeouts
    request_timeout: int = Field(default=30, env="REQUEST_TIMEOUT")
    max_retries: int = Field(default=3, env="MAX_RETRIES")
    
    class Config:
        env_prefix = "INTEGRATION_"


class NotificationSettings(BaseSettings):
    """Notification-specific configuration."""
    
    # Email configuration (from Vault)
    smtp_server: Optional[str] = Field(default=None, env="SMTP_SERVER")
    smtp_port: int = Field(default=587, env="SMTP_PORT")
    smtp_use_tls: bool = Field(default=True, env="SMTP_USE_TLS")
    email_from: str = Field(default="<EMAIL>", env="EMAIL_FROM")
    
    # Push notification configuration (from Vault)
    fcm_server_key: Optional[str] = Field(default=None, env="FCM_SERVER_KEY")
    apns_key_id: Optional[str] = Field(default=None, env="APNS_KEY_ID")
    apns_team_id: Optional[str] = Field(default=None, env="APNS_TEAM_ID")
    apns_bundle_id: str = Field(default="com.trix.app", env="APNS_BUNDLE_ID")
    push_batch_size: int = Field(default=500, env="PUSH_BATCH_SIZE")
    
    # SMS configuration (from Vault)
    twilio_account_sid: Optional[str] = Field(default=None, env="TWILIO_ACCOUNT_SID")
    twilio_auth_token: Optional[str] = Field(default=None, env="TWILIO_AUTH_TOKEN")
    twilio_phone_number: Optional[str] = Field(default=None, env="TWILIO_PHONE_NUMBER")
    
    # Template settings
    template_cache_ttl: int = Field(default=3600, env="TEMPLATE_CACHE_TTL")
    template_render_timeout: int = Field(default=5, env="TEMPLATE_RENDER_TIMEOUT")
    
    class Config:
        env_prefix = "NOTIFICATION_"


class Settings(BaseSettings, StandardVaultConfig):
    """Main enterprise settings aggregator."""
    
    # Sub-settings
    database: DatabaseSettings = DatabaseSettings()
    messaging: MessagingSettings = MessagingSettings()
    security: SecuritySettings = SecuritySettings()
    observability: ObservabilitySettings = ObservabilitySettings()
    service: ServiceSettings = ServiceSettings()
    integration: IntegrationSettings = IntegrationSettings()
    notification: NotificationSettings = NotificationSettings()
    
    @validator('service')
    def validate_service_settings(cls, v):
        """Validate service settings for production."""
        if v.environment == "production":
            if v.debug:
                raise ValueError("Debug mode cannot be enabled in production")
        return v
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance."""
    return Settings()


# Global settings instance
settings = get_settings()
