"""
Ghost Function Service - Seed Data

Cria configurações padrão para o sistema de resiliência e proxy inteligente.
Configurações de timeout, hibernação e failover para todos os microserviços.

Author: Trix Platform Team
"""

import asyncio
import uuid
from datetime import datetime
from typing import Dict, List, Any

from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker

from ..distributed_base import MicroserviceSeedModule


class GhostFunctionSeed(MicroserviceSeedModule):
    """Seed para Ghost Function Service - Sistema de Resiliência."""

    def __init__(self):
        super().__init__(
            microservice_name="ghost_function",
            module_name="ghost_function"
        )

    async def check_existing_data(self) -> bool:
        """Check if ghost function data already exists."""
        try:
            session = await self.get_database_session()
            async with session:
                # Verificar se a tabela de configurações existe e tem dados
                result = await session.execute(text("""
                    SELECT COUNT(*) FROM information_schema.tables
                    WHERE table_name = 'ghost_service_configurations'
                """))
                table_exists = result.scalar() > 0

                if table_exists:
                    result = await session.execute(text("SELECT COUNT(*) FROM ghost_service_configurations"))
                    config_count = result.scalar()
                    return config_count > 0

                return False
        except Exception as e:
            self.logger.error(f"Error checking existing data: {e}")
            return False

    async def create_data(self) -> None:
        """Create ghost function seed data."""
        session = await self.get_database_session()
        async with session:
            await self.create_tables(session)
            await self.seed_data(session)

    async def create_tables(self, session: AsyncSession) -> None:
        """Cria tabelas necessárias para o Ghost Function Service."""
        
        # Verificar se a extensão UUID está disponível
        await session.execute(text("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\""))
        
        # Tabela de configurações de serviços
        await session.execute(text("""
            DROP TABLE IF EXISTS ghost_service_configurations CASCADE
        """))

        await session.execute(text("""
            CREATE TABLE ghost_service_configurations (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                service_name VARCHAR(100) NOT NULL UNIQUE,
                primary_timeout_ms INTEGER NOT NULL DEFAULT 3000,
                hibernation_level VARCHAR(50) NOT NULL DEFAULT 'LIGHT_SLEEP',
                fallback_strategy VARCHAR(50) NOT NULL DEFAULT 'CACHED_RESPONSE',
                circuit_breaker_threshold INTEGER NOT NULL DEFAULT 5,
                priority VARCHAR(20) NOT NULL DEFAULT 'medium',
                enabled BOOLEAN NOT NULL DEFAULT true,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            )
        """))
        
        # Tabela de health status dos serviços
        await session.execute(text("""
            DROP TABLE IF EXISTS service_health_status CASCADE
        """))

        await session.execute(text("""
            CREATE TABLE service_health_status (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                service_name VARCHAR(100) NOT NULL,
                status VARCHAR(20) NOT NULL DEFAULT 'UNKNOWN',
                last_check TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                response_time_ms INTEGER,
                error_count INTEGER DEFAULT 0,
                consecutive_failures INTEGER DEFAULT 0,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            )
        """))
        
        # Tabela de eventos de failover
        await session.execute(text("""
            DROP TABLE IF EXISTS failover_events CASCADE
        """))

        await session.execute(text("""
            CREATE TABLE failover_events (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                service_name VARCHAR(100) NOT NULL,
                event_type VARCHAR(50) NOT NULL,
                from_source VARCHAR(50),
                to_source VARCHAR(50),
                reason TEXT,
                duration_ms INTEGER,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            )
        """))
        
        # Índices para performance
        await session.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_ghost_service_name 
            ON ghost_service_configurations(service_name)
        """))
        
        await session.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_health_service_name 
            ON service_health_status(service_name)
        """))
        
        await session.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_health_last_check 
            ON service_health_status(last_check)
        """))
        
        await session.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_failover_service_name 
            ON failover_events(service_name)
        """))
        
        await session.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_failover_created_at 
            ON failover_events(created_at)
        """))

    async def seed_data(self, session: AsyncSession) -> Dict[str, Any]:
        """Cria dados padrão para o Ghost Function Service."""
        
        # Configurações padrão dos serviços
        service_configs = [
            {
                'service_name': 'auth_service',
                'primary_timeout_ms': 2000,
                'hibernation_level': 'STANDBY',
                'fallback_strategy': 'IMMEDIATE',
                'circuit_breaker_threshold': 3,
                'priority': 'critical'
            },
            {
                'service_name': 'user_service',
                'primary_timeout_ms': 3000,
                'hibernation_level': 'LIGHT_SLEEP',
                'fallback_strategy': 'CACHED_RESPONSE',
                'circuit_breaker_threshold': 5,
                'priority': 'high'
            },
            {
                'service_name': 'tenant_service',
                'primary_timeout_ms': 3000,
                'hibernation_level': 'LIGHT_SLEEP',
                'fallback_strategy': 'CACHED_RESPONSE',
                'circuit_breaker_threshold': 5,
                'priority': 'high'
            },
            {
                'service_name': 'core_service',
                'primary_timeout_ms': 4000,
                'hibernation_level': 'WARM',
                'fallback_strategy': 'DEGRADED_MODE',
                'circuit_breaker_threshold': 7,
                'priority': 'medium'
            },
            {
                'service_name': 'notification_service',
                'primary_timeout_ms': 5000,
                'hibernation_level': 'DEEP_SLEEP',
                'fallback_strategy': 'QUEUE_FOR_LATER',
                'circuit_breaker_threshold': 10,
                'priority': 'low'
            },
            {
                'service_name': 'media_system',
                'primary_timeout_ms': 8000,
                'hibernation_level': 'DEEP_SLEEP',
                'fallback_strategy': 'QUEUE_FOR_LATER',
                'circuit_breaker_threshold': 15,
                'priority': 'low'
            }
        ]
        
        created_configs = {}
        
        for config in service_configs:
            # Verificar se já existe
            result = await session.execute(
                text("SELECT id FROM ghost_service_configurations WHERE service_name = :service_name"),
                {"service_name": config['service_name']}
            )
            
            if result.fetchone() is None:
                # Inserir nova configuração
                config_id = str(uuid.uuid4())
                await session.execute(
                    text("""
                        INSERT INTO ghost_service_configurations 
                        (id, service_name, primary_timeout_ms, hibernation_level, 
                         fallback_strategy, circuit_breaker_threshold, priority)
                        VALUES (:id, :service_name, :primary_timeout_ms, :hibernation_level,
                                :fallback_strategy, :circuit_breaker_threshold, :priority)
                    """),
                    {
                        'id': config_id,
                        'service_name': config['service_name'],
                        'primary_timeout_ms': config['primary_timeout_ms'],
                        'hibernation_level': config['hibernation_level'],
                        'fallback_strategy': config['fallback_strategy'],
                        'circuit_breaker_threshold': config['circuit_breaker_threshold'],
                        'priority': config['priority']
                    }
                )
                
                created_configs[config['service_name']] = {
                    'id': config_id,
                    'config': config
                }
                
                self.logger.info(f"✅ Created ghost config: {config['service_name']}")
            else:
                self.logger.info(f"⚠️ Ghost config already exists: {config['service_name']}")
        
        # Inicializar status de health para todos os serviços
        for service_name in [config['service_name'] for config in service_configs]:
            result = await session.execute(
                text("SELECT id FROM service_health_status WHERE service_name = :service_name"),
                {"service_name": service_name}
            )
            
            if result.fetchone() is None:
                health_id = str(uuid.uuid4())
                await session.execute(
                    text("""
                        INSERT INTO service_health_status 
                        (id, service_name, status, last_check)
                        VALUES (:id, :service_name, :status, :last_check)
                    """),
                    {
                        'id': health_id,
                        'service_name': service_name,
                        'status': 'UNKNOWN',
                        'last_check': datetime.utcnow()
                    }
                )
        
        await session.commit()
        
        self.logger.info(f"✅ Ghost Function seed completed successfully! Created {len(created_configs)} service configurations.")
        
        return {
            'created_configs': created_configs,
            'total_services': len(service_configs),
            'service_names': [config['service_name'] for config in service_configs]
        }

    async def health_check(self) -> bool:
        """Verifica se o Ghost Function Service está saudável."""
        try:
            engine = create_async_engine(self.db_url)
            async_session = async_sessionmaker(engine, expire_on_commit=False)
            
            async with async_session() as session:
                # Verificar conexão básica
                await session.execute(text("SELECT 1"))
                
                # Verificar se as tabelas existem
                result = await session.execute(text("""
                    SELECT COUNT(*) FROM information_schema.tables 
                    WHERE table_name IN ('ghost_service_configurations', 'service_health_status', 'failover_events')
                """))
                table_count = result.scalar()
                
                if table_count != 3:
                    self.logger.error(f"Expected 3 tables, found {table_count}")
                    return False
                
                # Verificar se há configurações
                result = await session.execute(text("SELECT COUNT(*) FROM ghost_service_configurations"))
                config_count = result.scalar()
                
                if config_count == 0:
                    self.logger.warning("No service configurations found")
                    return False
                
                self.logger.info(f"✅ Ghost Function health check passed. {config_count} configurations found.")
                return True
                
        except Exception as e:
            self.logger.error(f"❌ Ghost Function health check failed: {e}")
            return False
        finally:
            if 'engine' in locals():
                await engine.dispose()


# Função principal para execução do seed
async def seed() -> bool:
    """Executa o seed do Ghost Function Service."""
    ghost_seed = GhostFunctionSeed()
    return await ghost_seed.run()


# Para execução direta
if __name__ == "__main__":
    asyncio.run(seed())
