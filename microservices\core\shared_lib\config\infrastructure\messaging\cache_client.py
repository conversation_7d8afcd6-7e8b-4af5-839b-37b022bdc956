"""
Shared Cache Client for Trix Microservices
==========================================

Centralized Redis cache implementation for all microservices.
Provides consistent caching with fallback support and performance monitoring.

Moved from individual microservices to reduce duplication.
"""

import json
import time
from typing import Any, Dict, Optional, Union, List
from functools import lru_cache
import asyncio

try:
    import redis.asyncio as aioredis
    from redis.asyncio import Redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    aioredis = None
    Redis = None

from microservices.core.shared_lib.config.redis_config import RedisSettings
from microservices.core.shared_lib.config.infrastructure.observability.logging import get_logger


class CacheClient:
    """
    Shared Redis cache client for all microservices.
    
    Provides:
    - Async Redis operations
    - Fallback responses for Ghost Function Service
    - Performance monitoring
    - Connection pooling
    - Cluster support
    """
    
    def __init__(self, service_name: str):
        self.service_name = service_name
        self.settings = RedisSettings()
        self.logger = get_logger(service_name)
        self._redis_pool: Optional[Redis] = None
        self._initialized = False
    
    async def initialize(self) -> bool:
        """
        Initialize Redis connection.
        
        Returns:
            bool: True if successful, False otherwise
        """
        if not REDIS_AVAILABLE:
            self.logger.warning("Redis not available, cache disabled")
            return False
        
        try:
            # Build Redis URL
            redis_url = self._build_redis_url()
            
            # Create Redis connection
            self._redis_pool = aioredis.from_url(
                redis_url,
                encoding="utf-8",
                decode_responses=True,
                max_connections=self.settings.REDIS_MAX_CONNECTIONS,
                socket_timeout=self.settings.REDIS_SOCKET_TIMEOUT,
                socket_connect_timeout=self.settings.REDIS_CONNECTION_TIMEOUT,
                retry_on_timeout=self.settings.REDIS_RETRY_ON_TIMEOUT
            )
            
            # Test connection
            await self._redis_pool.ping()
            
            self._initialized = True
            self.logger.info(
                "✅ Redis cache initialized",
                redis_url=redis_url.split('@')[-1] if '@' in redis_url else redis_url,
                max_connections=self.settings.REDIS_MAX_CONNECTIONS
            )
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Redis cache: {e}")
            self._initialized = False
            return False
    
    def _build_redis_url(self) -> str:
        """Build Redis connection URL."""
        if self.settings.REDIS_URL:
            return self.settings.REDIS_URL
        
        # Build URL from components
        auth = f":{self.settings.REDIS_PASSWORD}@" if self.settings.REDIS_PASSWORD else ""
        ssl_scheme = "rediss" if self.settings.REDIS_SSL else "redis"
        
        return f"{ssl_scheme}://{auth}{self.settings.REDIS_HOST}:{self.settings.REDIS_PORT}/{self.settings.REDIS_DB}"
    
    async def get(self, key: str, default: Any = None) -> Any:
        """
        Get value from cache.
        
        Args:
            key: Cache key
            default: Default value if key not found
            
        Returns:
            Cached value or default
        """
        if not self._initialized:
            return default
        
        try:
            start_time = time.time()
            value = await self._redis_pool.get(key)
            duration_ms = (time.time() - start_time) * 1000
            
            if value is None:
                self.logger.debug(f"Cache miss: {key}", duration_ms=duration_ms)
                return default
            
            # Try to deserialize JSON
            try:
                result = json.loads(value)
            except (json.JSONDecodeError, TypeError):
                result = value
            
            self.logger.debug(f"Cache hit: {key}", duration_ms=duration_ms)
            return result
            
        except Exception as e:
            self.logger.error(f"Cache get error for key {key}: {e}")
            return default
    
    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        nx: bool = False,
        xx: bool = False
    ) -> bool:
        """
        Set value in cache.
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: Time to live in seconds
            nx: Only set if key doesn't exist
            xx: Only set if key exists
            
        Returns:
            bool: True if successful
        """
        if not self._initialized:
            return False
        
        try:
            start_time = time.time()
            
            # Serialize value
            if isinstance(value, (dict, list)):
                serialized_value = json.dumps(value)
            else:
                serialized_value = str(value)
            
            # Set with options
            result = await self._redis_pool.set(
                key,
                serialized_value,
                ex=ttl or self.settings.CACHE_TTL,
                nx=nx,
                xx=xx
            )
            
            duration_ms = (time.time() - start_time) * 1000
            self.logger.debug(f"Cache set: {key}", duration_ms=duration_ms, ttl=ttl)
            
            return bool(result)
            
        except Exception as e:
            self.logger.error(f"Cache set error for key {key}: {e}")
            return False
    
    async def delete(self, *keys: str) -> int:
        """
        Delete keys from cache.
        
        Args:
            keys: Keys to delete
            
        Returns:
            int: Number of keys deleted
        """
        if not self._initialized or not keys:
            return 0
        
        try:
            start_time = time.time()
            result = await self._redis_pool.delete(*keys)
            duration_ms = (time.time() - start_time) * 1000
            
            self.logger.debug(f"Cache delete: {keys}", duration_ms=duration_ms, deleted=result)
            return result
            
        except Exception as e:
            self.logger.error(f"Cache delete error for keys {keys}: {e}")
            return 0
    
    async def exists(self, key: str) -> bool:
        """
        Check if key exists in cache.
        
        Args:
            key: Cache key
            
        Returns:
            bool: True if key exists
        """
        if not self._initialized:
            return False
        
        try:
            result = await self._redis_pool.exists(key)
            return bool(result)
            
        except Exception as e:
            self.logger.error(f"Cache exists error for key {key}: {e}")
            return False
    
    async def increment(self, key: str, amount: int = 1) -> Optional[int]:
        """
        Increment numeric value in cache.
        
        Args:
            key: Cache key
            amount: Amount to increment
            
        Returns:
            New value or None if error
        """
        if not self._initialized:
            return None
        
        try:
            result = await self._redis_pool.incrby(key, amount)
            return result
            
        except Exception as e:
            self.logger.error(f"Cache increment error for key {key}: {e}")
            return None
    
    async def set_hash(self, key: str, mapping: Dict[str, Any], ttl: Optional[int] = None) -> bool:
        """
        Set hash in cache.
        
        Args:
            key: Hash key
            mapping: Hash fields and values
            ttl: Time to live in seconds
            
        Returns:
            bool: True if successful
        """
        if not self._initialized:
            return False
        
        try:
            # Serialize values
            serialized_mapping = {}
            for field, value in mapping.items():
                if isinstance(value, (dict, list)):
                    serialized_mapping[field] = json.dumps(value)
                else:
                    serialized_mapping[field] = str(value)
            
            await self._redis_pool.hset(key, mapping=serialized_mapping)
            
            if ttl:
                await self._redis_pool.expire(key, ttl)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Cache set hash error for key {key}: {e}")
            return False
    
    async def get_hash(self, key: str, field: Optional[str] = None) -> Any:
        """
        Get hash from cache.
        
        Args:
            key: Hash key
            field: Specific field (optional)
            
        Returns:
            Hash value(s) or None
        """
        if not self._initialized:
            return None
        
        try:
            if field:
                value = await self._redis_pool.hget(key, field)
                if value is None:
                    return None
                
                try:
                    return json.loads(value)
                except (json.JSONDecodeError, TypeError):
                    return value
            else:
                hash_data = await self._redis_pool.hgetall(key)
                if not hash_data:
                    return None
                
                # Deserialize values
                result = {}
                for k, v in hash_data.items():
                    try:
                        result[k] = json.loads(v)
                    except (json.JSONDecodeError, TypeError):
                        result[k] = v
                
                return result
                
        except Exception as e:
            self.logger.error(f"Cache get hash error for key {key}: {e}")
            return None
    
    async def close(self):
        """Close Redis connection."""
        if self._redis_pool:
            await self._redis_pool.close()
            self._initialized = False
            self.logger.info("Redis cache connection closed")


# Global cache instances
_cache_clients: Dict[str, CacheClient] = {}


@lru_cache()
def get_cache_client(service_name: str) -> CacheClient:
    """
    Get or create a cache client for a service.
    
    Args:
        service_name: Name of the service
        
    Returns:
        CacheClient: Configured cache client
    """
    if service_name not in _cache_clients:
        _cache_clients[service_name] = CacheClient(service_name)
    return _cache_clients[service_name]


async def initialize_cache(service_name: str) -> CacheClient:
    """
    Initialize cache for a service.
    
    Args:
        service_name: Name of the service
        
    Returns:
        CacheClient: Initialized cache client
    """
    cache_client = get_cache_client(service_name)
    await cache_client.initialize()
    return cache_client
