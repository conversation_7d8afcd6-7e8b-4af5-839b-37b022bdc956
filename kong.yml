_format_version: "3.0"

services:
  - name: auth-service
    url: http://auth-service:8001
    routes:
      - name: auth-route
        paths:
          - /api/v1/auth
  - name: user-service
    url: http://user-service:8002
    routes:
      - name: user-route
        paths:
          - /api/v1/users
  - name: tenant-service
    url: http://tenant-service:8000
    routes:
      - name: tenant-route
        paths:
          - /api/v1/tenants
  - name: frontend
    url: http://frontend:3000
    routes:
      - name: frontend-route
        paths:
          - /