# ============================================================================
# I18N SERVICE - Microserviço de Internacionalização
# ============================================================================
# Porta: 8008 | Database: postgres-i18n (5437) | Redis: DB 4
# Submódulos: Language Management, Translation Management, Translation Keys, Translation Suggestions, Translation Changes
# ============================================================================

services:
  # ============================================================================
  # I18N SERVICE - Serviço Principal
  # ============================================================================
  trix-core-i18n:
    build:
      context: ../
      dockerfile: docker/Dockerfile
    container_name: trix-core-i18n
    ports:
      - "8008:8000"
    environment:
      - DATABASE_URL=postgresql+asyncpg://postgres:TrixSuperSecure2024!@trix-citus-core-coordinator:5432/i18n_db
      - REDIS_URL=redis://:${REDIS_PASSWORD}@trix-redis:6379/4
      - AUTH_SERVICE_URL=http://trix-core-auth:8001
      - USER_SERVICE_URL=http://trix-core-user:8002
      - TENANT_SERVICE_URL=http://trix-core-tenant:8003
      - NOTIFICATION_SERVICE_URL=http://trix-core-notification:8006
      - SECRET_KEY=${JWT_SECRET_KEY}
      - DEBUG=${DEBUG}
      - SERVICE_NAME=i18n-service
      - SERVICE_VERSION=2.0.0
      - SERVICE_PORT=8008
      - WEBSOCKET_ENABLED=true
      - ENABLE_SMART_SYNC=true
      - DEFAULT_LANGUAGE=en
      - FALLBACK_LANGUAGE=en
      - CACHE_TTL=3600
      # Vault settings (inherited from shared_lib)
      - VAULT_URL=${VAULT_URL}
      - VAULT_TOKEN=${VAULT_TOKEN}
      - VAULT_ENABLED=${VAULT_ENABLED}
    external_links:
      - trix-citus-core-coordinator:trix-citus-core-coordinator
    volumes:
      - ../:/app
      - ../../shared_lib:/app/microservices/core/shared_lib:ro
    networks:
      - trix-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    labels:
      - "trix.category=core"
      - "trix.service=microservice"
      - "trix.module=i18n"
      - "trix.port=8008"
      - "trix.version=2.0.0"

  # ============================================================================
  # I18N DATABASE - Usando banco principal consolidado
  # ============================================================================
  # Banco removido - usando banco principal 'db' do docker-compose.yml raiz

networks:
  trix-network:
    external: true
