"""
Enterprise Settings Configuration for Auth Service
=================================================
Comprehensive configuration management following auth_service.md enterprise specification.
Supports Vault integration, service mesh, observability, and multi-environment deployment.
Migrado para usar shared_lib para configurações comuns.
"""

import os
import secrets
from typing import List, Optional, Dict, Any, Union
from pathlib import Path

from pydantic import Field, field_validator, ConfigDict
from pydantic_settings import BaseSettings, SettingsConfigDict
from microservices.core.shared_lib.config import (
    VaultBaseSettings,
    DatabaseSettings,
    RedisSettings,
    SecuritySettings,
    LoggingSettings
)
from microservices.core.shared_lib.config.security_config import get_security_settings


class JaegerSettings(BaseSettings):
    """Jaeger tracing configuration."""

    model_config = SettingsConfigDict(
        env_prefix="JAEGER_",
        case_sensitive=True
    )

    # Jaeger agent settings
    agent_host: str = Field(default="localhost", description="Jaeger agent host")
    agent_port: int = Field(default=6831, ge=1, le=65535, description="Jaeger agent port")
    enabled: bool = Field(default=True, description="Enable Jaeger tracing")


class VaultSettings(BaseSettings):
    """HashiCorp Vault configuration."""

    model_config = SettingsConfigDict(
        case_sensitive=False
    )

    # Vault connection
    vault_enabled: bool = Field(default=False, env="VAULT_ENABLED", description="Enable Vault integration")
    vault_url: Optional[str] = Field(None, env="VAULT_URL", description="Vault server URL")
    vault_token: Optional[str] = Field(None, env="VAULT_TOKEN", description="Vault authentication token")
    vault_mount_point: str = Field(default="secret", env="VAULT_MOUNT_POINT", description="Vault mount point")
    vault_secret_path: str = Field(default="auth-service", env="VAULT_SECRET_PATH", description="Vault secret path")


class OPASettings(BaseSettings):
    """Open Policy Agent configuration."""

    model_config = SettingsConfigDict(
        case_sensitive=False
    )

    # OPA connection
    opa_enabled: bool = Field(default=True, env="OPA_ENABLED", description="Enable OPA integration")
    opa_url: str = Field(default="http://opa:8181", env="OPA_URL", description="OPA server URL")
    opa_policy_path: str = Field(default="/v1/data/trix/auth", env="OPA_POLICY_PATH", description="OPA policy path")
    opa_timeout: int = Field(default=30, env="OPA_TIMEOUT", description="OPA request timeout in seconds")


class Settings(VaultBaseSettings):
    """
    Enterprise Auth Service settings with comprehensive configuration management.
    Inherits from VaultBaseSettings for consistent Vault integration.
    """

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=True,
        extra="ignore"
    )

    # Service identification
    service_name: str = Field(default="auth-service", description="Service name")
    service_version: str = Field(default="v2.0.0", description="Service version")
    service_port: int = Field(default=8001, ge=1024, le=65535, description="Service port")
    service_host: str = Field(default="0.0.0.0", description="Service host")

    # Environment
    environment: str = Field(default="development", description="Environment: development, staging, production")
    debug: bool = Field(default=False, description="Debug mode")
    testing: bool = Field(default=False, description="Testing mode")

    # Logging
    LOG_LEVEL: str = Field(default="INFO", description="Logging level")

    # Shared configurations (from shared_lib)
    database_config: DatabaseSettings = Field(default_factory=DatabaseSettings)
    redis_config: RedisSettings = Field(default_factory=RedisSettings)
    security_config: SecuritySettings = Field(default_factory=SecuritySettings)
    logging_config: LoggingSettings = Field(default_factory=LoggingSettings)

    # Auth Service specific configurations
    jaeger: JaegerSettings = Field(default_factory=JaegerSettings)
    vault_config: VaultSettings = Field(default_factory=VaultSettings)
    opa_config: OPASettings = Field(default_factory=OPASettings)

    # External service URLs
    user_service_url: Optional[str] = Field(None, description="User Service URL")
    tenant_service_url: Optional[str] = Field(None, description="Tenant Service URL")
    notification_service_url: Optional[str] = Field(None, description="Notification Service URL")
    core_service_url: Optional[str] = Field(None, description="Core Service URL")

    # External service API keys
    user_service_api_key: Optional[str] = Field(None, description="User Service API key")
    tenant_service_api_key: Optional[str] = Field(None, description="Tenant Service API key")
    notification_service_api_key: Optional[str] = Field(None, description="Notification Service API key")
    core_service_api_key: Optional[str] = Field(None, description="Core Service API key")

    # Messaging configuration
    kafka_enabled: bool = Field(default=True, description="Enable Kafka messaging")
    kafka_bootstrap_servers: List[str] = Field(
        default=["kafka-1:9092", "kafka-2:9092", "kafka-3:9092"],
        description="Kafka bootstrap servers"
    )

    rabbitmq_enabled: bool = Field(default=False, description="Enable RabbitMQ messaging")
    rabbitmq_url: str = Field(
        default="amqp://guest:guest@rabbitmq:5672/",
        description="RabbitMQ connection URL"
    )

    @field_validator('environment')
    @classmethod
    def validate_environment(cls, v: str) -> str:
        """Validate environment value."""
        allowed_environments = ['development', 'staging', 'production', 'testing']
        if v not in allowed_environments:
            raise ValueError(f'Environment must be one of: {allowed_environments}')
        return v

    def get_database_url(self) -> str:
        """Get database URL with environment-specific overrides."""
        return self.database_config.get_database_url(async_driver=True, service_name="auth")

    def get_redis_url(self) -> str:
        """Get Redis URL for single instance mode."""
        return self.redis_config.url

    # Propriedades de acesso rápido ao Vault (delegação)
    @property
    def vault_url(self) -> str:
        return self.vault_config.vault_url

    @property
    def vault_token(self) -> str:
        return self.vault_config.vault_token

    @property
    def vault_enabled(self) -> bool:
        return self.vault_config.vault_enabled

    @property
    def vault_mount_point(self) -> str:
        return self.vault_config.vault_mount_point

    @property
    def vault_secret_path(self) -> str:
        return self.vault_config.vault_secret_path

    # Propriedades de acesso rápido ao OPA (delegação)
    @property
    def opa_url(self) -> str:
        return self.opa_config.opa_url

    @property
    def opa_enabled(self) -> bool:
        return self.opa_config.opa_enabled

    @property
    def opa_policy_path(self) -> str:
        return self.opa_config.opa_policy_path

    @property
    def opa_timeout(self) -> int:
        return self.opa_config.opa_timeout

    @property
    def shared_security_settings(self):
        """Get shared security settings from shared_lib."""
        return get_security_settings()

    def get_auth_service_config(self) -> Dict[str, Any]:
        """Get auth service specific configuration."""
        from microservices.core.shared_lib.config.security_config import get_service_security_config
        return get_service_security_config("auth_service")


# Global settings instance
settings = Settings()

# External service API keys
USER_SERVICE_API_KEY = settings.user_service_api_key
TENANT_SERVICE_API_KEY = settings.tenant_service_api_key
NOTIFICATION_SERVICE_API_KEY = settings.notification_service_api_key
CORE_SERVICE_API_KEY = settings.core_service_api_key
