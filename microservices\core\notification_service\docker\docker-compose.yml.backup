# ============================================================================
# NOTIFICATION SERVICE - Microserviço de Notificações
# ============================================================================
# Porta: 8019 | Database: postgres-notifications (5450) | Redis: DB 17
# Submódulos: real_time_notifications, notification_queue, notification_metrics, notification_templates, email_notifications, push_notifications, websocket_management
# ============================================================================

services:
  # ============================================================================
  # NOTIFICATION SERVICE - Serviço Principal
  # ============================================================================
  trix-infrastructure-notifications:
    build:
      context: ../
      dockerfile: Dockerfile
    container_name: trix-infrastructure-notifications
    ports:
      - "8019:8000"
    environment:
      - DATABASE_URL=postgresql+asyncpg://${NOTIFICATIONS_DB_USER}:${NOTIFICATIONS_DB_PASSWORD}@trix-db-notifications:5432/${NOTIFICATIONS_DB_NAME}
      - REDIS_URL=redis://:${REDIS_PASSWORD}@trix-redis:6379/17
      - AUTH_SERVICE_URL=http://trix-core-auth:8001
      - EMAIL_SERVICE_URL=http://trix-shared-email:8012
      - SECRET_KEY=${JWT_SECRET_KEY}
      - DEBUG=${DEBUG}
      - SERVICE_NAME=notification-service
      - SERVICE_VERSION=1.0.0
      - CELERY_BROKER_URL=redis://:${REDIS_PASSWORD}@trix-redis:6379/17
      - CELERY_RESULT_BACKEND=redis://:${REDIS_PASSWORD}@trix-redis:6379/17
      - WEBSOCKET_ENABLED=true
      - WEBSOCKET_PORT=8119
      - PUSH_NOTIFICATIONS_ENABLED=true
      - FCM_SERVER_KEY=${FCM_SERVER_KEY}
      - APNS_CERTIFICATE_PATH=/app/certificates/apns.pem
      - APNS_SANDBOX=false
    depends_on:
    
