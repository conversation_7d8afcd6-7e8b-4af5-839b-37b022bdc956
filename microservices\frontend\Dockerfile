# Use Node.js LTS as the base image
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
WORKDIR /app

# Create user early to avoid permission issues
RUN addgroup --system --gid 1001 nodejs && \
    adduser --system --uid 1001 nextjs

# Copy package.json and package-lock.json (if available)
COPY package.json package-lock.json* ./

# Install dependencies with cache optimization
RUN npm install --ignore-scripts && \
    npm cache clean --force

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app

# Create user in builder stage too
RUN addgroup --system --gid 1001 nodejs && \
    adduser --system --uid 1001 nextjs

# Copy dependencies from deps stage
COPY --from=deps /app/node_modules ./node_modules
COPY --from=deps /app/package.json ./package.json

# Copy source code
COPY . .

# Set environment variables for development
ENV NEXT_TELEMETRY_DISABLED=1
ENV NODE_ENV=development

# Switch to nextjs user
USER nextjs

# Expose the port
EXPOSE 3000

# Start the development server
CMD ["npm", "run", "dev"]
