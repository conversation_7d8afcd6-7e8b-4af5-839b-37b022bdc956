"""
User Schemas - Enterprise Edition
=================================
Pydantic schemas for User Service with enterprise features and validation.
"""

import uuid
from datetime import datetime
from typing import Optional, List, Dict, Any

from pydantic import BaseModel, EmailStr, ConfigDict, Field, field_validator


class CredentialsValidation(BaseModel):
    """Schema for credentials validation by Auth Service."""
    email: EmailStr = Field(..., description="User email")
    password: str = Field(..., description="User password")


class UserBase(BaseModel):
    """Base user schema with enterprise fields."""
    email: EmailStr = Field(..., description="User email address")
    full_name: str = Field(..., min_length=1, max_length=255, description="User full name")
    phone_number: Optional[str] = Field(None, max_length=20, description="Phone number")
    region: str = Field(default="global", max_length=50, description="Geographic region")
    system_role: str = Field(default="user", description="System role")
    data_sharing_consent: bool = Field(default=False, description="LGPD/GDPR consent")

    @field_validator('system_role', mode='before')
    @classmethod
    def validate_system_role(cls, v):
        allowed_roles = ['user', 'admin', 'superadmin']
        if v not in allowed_roles:
            raise ValueError(f'System role must be one of: {allowed_roles}')
        return v

    @field_validator('phone_number', mode='before')
    @classmethod
    def validate_phone_number(cls, v):
        if v and not v.replace('+', '').replace('-', '').replace(' ', '').replace('(', '').replace(')', '').isdigit():
            raise ValueError('Invalid phone number format')
        return v


class UserCreate(UserBase):
    """Schema for creating new users."""
    password: str = Field(..., min_length=8, max_length=128, description="User password")

    @field_validator('password', mode='before')
    @classmethod
    def validate_password_strength(cls, v):
        # Basic password validation
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(c.isupper() for c in v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not any(c.islower() for c in v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        return v


class UserUpdate(BaseModel):
    """Schema for updating user information."""
    email: Optional[EmailStr] = None
    full_name: Optional[str] = Field(None, min_length=1, max_length=255)
    phone_number: Optional[str] = Field(None, max_length=20)
    password: Optional[str] = Field(None, min_length=8, max_length=128)
    region: Optional[str] = Field(None, max_length=50)
    system_role: Optional[str] = None
    data_sharing_consent: Optional[bool] = None
    is_active: Optional[bool] = None
    is_verified: Optional[bool] = None

    @field_validator('system_role', mode='before')
    @classmethod
    def validate_system_role(cls, v):
        if v is not None:
            allowed_roles = ['user', 'admin', 'superadmin']
            if v not in allowed_roles:
                raise ValueError(f'System role must be one of: {allowed_roles}')
        return v


class UserResponse(BaseModel):
    """Schema for user response data."""
    id: uuid.UUID
    tenant_id: uuid.UUID
    email: EmailStr
    full_name: str
    phone_number: Optional[str]
    region: str
    is_active: bool
    is_verified: bool
    system_role: str
    data_sharing_consent: bool
    created_at: datetime
    updated_at: datetime
    last_login: Optional[datetime]

    model_config = ConfigDict(from_attributes=True)


class UserInDBBase(UserBase):
    """Base schema for users in database."""
    id: uuid.UUID
    tenant_id: uuid.UUID
    is_active: bool = True
    is_verified: bool = False
    created_at: datetime
    updated_at: datetime
    last_login: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


class User(UserInDBBase):
    pass


class UserWithTenants(User):
    tenants: List["Tenant"] = []


# Schema for paginated user response
class UserListResponse(BaseModel):
    users: List[User]
    total: int
    page: int
    limit: int
    total_pages: int


# Schema for user response
class UserResponse(User):
    """Schema para resposta de usuário."""
    pass


# Enterprise Bulk Operations
class BulkUserCreate(BaseModel):
    """Schema for bulk user creation."""
    users: List[UserCreate] = Field(..., min_items=1, max_items=1000, description="List of users to create")

    @field_validator('users', mode='before')
    @classmethod
    def validate_unique_emails(cls, v):
        emails = [user.email for user in v]
        if len(emails) != len(set(emails)):
            raise ValueError('Duplicate emails in bulk creation request')
        return v


class BulkUserResponse(BaseModel):
    """Schema for bulk user creation response."""
    created: List[UserResponse] = Field(default_factory=list, description="Successfully created users")
    failed: List[Dict[str, Any]] = Field(default_factory=list, description="Failed user creations with errors")
    total_requested: int = Field(..., description="Total users requested for creation")
    total_created: int = Field(..., description="Total users successfully created")
    total_failed: int = Field(..., description="Total users that failed to create")


UserSchema = User
