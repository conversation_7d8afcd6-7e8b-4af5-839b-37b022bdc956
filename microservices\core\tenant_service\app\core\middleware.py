"""
Middleware for tenant service.
"""

import time
import logging
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from microservices.core.shared_lib.config.infrastructure.observability.metrics import MetricsCollector

logger = logging.getLogger(__name__)

# Initialize metrics collector
metrics_collector = MetricsCollector(service_name="tenant-service")


class MetricsMiddleware(BaseHTTPMiddleware):
    """Middleware to collect API metrics."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request and collect metrics."""
        start_time = time.time()
        
        # Extract method and path
        method = request.method
        path = request.url.path
        
        # Normalize path for metrics (remove IDs)
        normalized_path = self._normalize_path(path)
        
        try:
            response = await call_next(request)
            status_code = response.status_code
            
        except Exception as e:
            logger.error(f"Request failed: {e}")
            status_code = 500
            raise
            
        finally:
            # Record metrics
            duration = time.time() - start_time

            # Record request count
            metrics_collector.increment_counter(
                "api_requests_total",
                labels={
                    "method": method,
                    "endpoint": normalized_path,
                    "status_code": str(status_code)
                }
            )

            # Record request duration
            metrics_collector.record_histogram(
                "api_request_duration_seconds",
                duration,
                labels={
                    "method": method,
                    "endpoint": normalized_path
                }
            )
        
        return response
    
    def _normalize_path(self, path: str) -> str:
        """Normalize path for metrics by replacing IDs with placeholders."""
        import re
        
        # Replace UUIDs with {id}
        path = re.sub(
            r'/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}',
            '/{id}',
            path,
            flags=re.IGNORECASE
        )
        
        # Replace numeric IDs with {id}
        path = re.sub(r'/\d+', '/{id}', path)
        
        return path


class TenantContextMiddleware(BaseHTTPMiddleware):
    """Middleware to extract and validate tenant context."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request and extract tenant context."""
        
        # Extract tenant ID from various sources
        tenant_id = self._extract_tenant_id(request)
        
        if tenant_id:
            # Add tenant context to request state
            request.state.tenant_id = tenant_id
            
            # Add tenant ID to logs
            logger.info(f"Request for tenant {tenant_id}: {request.method} {request.url.path}")
        
        response = await call_next(request)
        
        # Add tenant ID to response headers for debugging
        if tenant_id:
            response.headers["X-Tenant-ID"] = tenant_id
        
        return response
    
    def _extract_tenant_id(self, request: Request) -> str:
        """Extract tenant ID from request."""
        
        # Try to get from path parameters
        path_parts = request.url.path.split('/')
        for i, part in enumerate(path_parts):
            if part == 'tenants' and i + 1 < len(path_parts):
                potential_id = path_parts[i + 1]
                if self._is_valid_uuid(potential_id):
                    return potential_id
        
        # Try to get from query parameters
        tenant_id = request.query_params.get('tenant_id')
        if tenant_id and self._is_valid_uuid(tenant_id):
            return tenant_id
        
        # Try to get from headers
        tenant_id = request.headers.get('X-Tenant-ID')
        if tenant_id and self._is_valid_uuid(tenant_id):
            return tenant_id
        
        return None
    
    def _is_valid_uuid(self, value: str) -> bool:
        """Check if value is a valid UUID."""
        import uuid
        try:
            uuid.UUID(value)
            return True
        except ValueError:
            return False


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Middleware to add security headers."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Add security headers to response."""
        response = await call_next(request)
        
        # Add security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["Content-Security-Policy"] = "default-src 'self'"
        
        # Add service identification
        response.headers["X-Service"] = "tenant-service"
        response.headers["X-Version"] = "1.0.0"
        
        return response


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for structured request logging."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Log request details."""
        start_time = time.time()
        
        # Log request
        logger.info(
            "Request started",
            extra={
                "method": request.method,
                "path": request.url.path,
                "query_params": str(request.query_params),
                "user_agent": request.headers.get("user-agent"),
                "client_ip": request.client.host if request.client else None,
                "tenant_id": getattr(request.state, 'tenant_id', None),
            }
        )
        
        try:
            response = await call_next(request)
            
            # Log response
            duration = time.time() - start_time
            logger.info(
                "Request completed",
                extra={
                    "method": request.method,
                    "path": request.url.path,
                    "status_code": response.status_code,
                    "duration_ms": duration * 1000,
                    "tenant_id": getattr(request.state, 'tenant_id', None),
                }
            )
            
            return response
            
        except Exception as e:
            # Log error
            duration = time.time() - start_time
            logger.error(
                "Request failed",
                extra={
                    "method": request.method,
                    "path": request.url.path,
                    "error": str(e),
                    "duration_ms": duration * 1000,
                    "tenant_id": getattr(request.state, 'tenant_id', None),
                },
                exc_info=True
            )
            raise


class CacheControlMiddleware(BaseHTTPMiddleware):
    """Middleware to add cache control headers."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Add cache control headers based on endpoint."""
        response = await call_next(request)
        
        path = request.url.path
        
        # Set cache headers based on endpoint type
        if '/health' in path or '/metrics' in path:
            # Health and metrics endpoints - no cache
            response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
            response.headers["Pragma"] = "no-cache"
            response.headers["Expires"] = "0"
        elif '/api/v1/tenants/search' in path:
            # Search results - short cache
            response.headers["Cache-Control"] = "public, max-age=120"  # 2 minutes
        elif '/api/v1/tenants/' in path and request.method == 'GET':
            # Tenant data - medium cache
            response.headers["Cache-Control"] = "public, max-age=300"  # 5 minutes
        elif '/api/v1/public/' in path:
            # Public data - longer cache
            response.headers["Cache-Control"] = "public, max-age=600"  # 10 minutes
        else:
            # Default - no cache for mutations
            response.headers["Cache-Control"] = "no-cache"
        
        return response
