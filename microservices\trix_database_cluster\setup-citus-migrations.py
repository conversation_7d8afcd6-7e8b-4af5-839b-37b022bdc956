#!/usr/bin/env python3
"""
🔄 Setup Citus Migrations
Configura e executa migrations distribuídas no cluster Citus
"""

import asyncio
import asyncpg
import logging
import os
from pathlib import Path
from typing import Dict, List

# Configuração de logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Mapeamento de coordenadores e bancos
COORDINATORS_CONFIG = {
    'core': {
        'host': 'localhost',
        'port': 5432,
        'databases': [
            'auth_db', 'users_db', 'tenants_db', 'core_db', 'i18n_db',
            'ghost_function_db', 'cdn_db', 'dns_bridge_db', 'domain_db',
            'notification_db', 'payment_db', 'supplier_db', 'synapse_ai_db', 'media_system_db'
        ]
    },
    'shared': {
        'host': 'localhost',
        'port': 5433,
        'databases': ['crm_db', 'email_db', 'financial_db', 'hr_db']
    },
    'tenant': {
        'host': 'localhost',
        'port': 5434,
        'databases': ['restaurant_db', 'consultancy_db']
    }
}

class CitusMigrationSetup:
    """Configurador de migrations para Citus"""
    
    def __init__(self):
        self.user = 'postgres'
        self.password = 'TrixSuperSecure2024!'
        
    async def setup_citus_cluster(self):
        """Configura o cluster Citus e executa migrations"""
        logger.info("🚀 Iniciando configuração do cluster Citus...")
        
        try:
            # 1. Configurar coordenadores
            await self._setup_coordinators()
            
            # 2. Configurar workers
            await self._setup_workers()
            
            # 3. Executar migrations consolidadas
            await self._run_consolidated_migrations()
            
            # 4. Configurar distribuição de tabelas
            await self._setup_distributed_tables()
            
            logger.info("✅ Cluster Citus configurado com sucesso!")
            
        except Exception as e:
            logger.error(f"❌ Erro na configuração: {e}")
            raise
    
    async def _setup_coordinators(self):
        """Configura os coordenadores Citus"""
        logger.info("⚙️ Configurando coordenadores...")
        
        for coord_name, config in COORDINATORS_CONFIG.items():
            logger.info(f"📋 Configurando coordenador {coord_name}...")
            
            try:
                # Conectar ao postgres padrão
                conn = await asyncpg.connect(
                    host=config['host'],
                    port=config['port'],
                    user=self.user,
                    password=self.password,
                    database='postgres'
                )
                
                # Criar extensão Citus
                await conn.execute('CREATE EXTENSION IF NOT EXISTS citus;')
                
                # Criar bancos de dados
                for db_name in config['databases']:
                    try:
                        await conn.execute(f'CREATE DATABASE "{db_name}";')
                        logger.info(f"✅ Banco {db_name} criado")
                    except asyncpg.DuplicateDatabaseError:
                        logger.info(f"ℹ️ Banco {db_name} já existe")
                    except Exception as e:
                        logger.warning(f"⚠️ Erro ao criar {db_name}: {e}")
                
                await conn.close()
                logger.info(f"✅ Coordenador {coord_name} configurado")
                
            except Exception as e:
                logger.error(f"❌ Erro no coordenador {coord_name}: {e}")
                raise
    
    async def _setup_workers(self):
        """Configura workers nos coordenadores"""
        logger.info("🔗 Configurando workers...")
        
        workers = [
            {'host': 'localhost', 'port': 5435},
            {'host': 'localhost', 'port': 5436}
        ]
        
        for coord_name, config in COORDINATORS_CONFIG.items():
            logger.info(f"📋 Adicionando workers ao coordenador {coord_name}...")
            
            try:
                conn = await asyncpg.connect(
                    host=config['host'],
                    port=config['port'],
                    user=self.user,
                    password=self.password,
                    database='postgres'
                )
                
                for worker in workers:
                    try:
                        # Verificar se worker já existe
                        existing = await conn.fetchval(
                            "SELECT COUNT(*) FROM pg_dist_node WHERE nodename = $1 AND nodeport = $2",
                            worker['host'], worker['port']
                        )
                        
                        if existing == 0:
                            await conn.execute(
                                "SELECT citus_add_node($1, $2);",
                                worker['host'], worker['port']
                            )
                            logger.info(f"✅ Worker {worker['host']}:{worker['port']} adicionado")
                        else:
                            logger.info(f"ℹ️ Worker {worker['host']}:{worker['port']} já existe")
                            
                    except Exception as e:
                        logger.warning(f"⚠️ Erro ao adicionar worker: {e}")
                
                await conn.close()
                
            except Exception as e:
                logger.error(f"❌ Erro ao configurar workers para {coord_name}: {e}")
    
    async def _run_consolidated_migrations(self):
        """Executa migrations consolidadas em cada banco"""
        logger.info("🔄 Executando migrations consolidadas...")
        
        # Verificar se migration consolidada existe
        migration_file = Path("migrations/versions/consolidated_20250721_144511_all_microservices.py")
        if not migration_file.exists():
            logger.warning("⚠️ Migration consolidada não encontrada, criando tabelas básicas...")
            await self._create_basic_tables()
            return
        
        # Executar migration consolidada em cada banco
        for coord_name, config in COORDINATORS_CONFIG.items():
            for db_name in config['databases']:
                await self._run_migration_for_database(coord_name, config, db_name)
    
    async def _run_migration_for_database(self, coord_name: str, config: Dict, db_name: str):
        """Executa migration para um banco específico"""
        logger.info(f"📋 Executando migration para {db_name} no coordenador {coord_name}...")
        
        try:
            conn = await asyncpg.connect(
                host=config['host'],
                port=config['port'],
                user=self.user,
                password=self.password,
                database=db_name
            )
            
            # Criar extensão Citus no banco
            await conn.execute('CREATE EXTENSION IF NOT EXISTS citus;')
            
            # Verificar se migration já foi executada
            try:
                await conn.execute('SELECT 1 FROM alembic_version LIMIT 1')
                logger.info(f"ℹ️ Migration já executada para {db_name}")
            except:
                # Criar tabela alembic_version
                await conn.execute('''
                    CREATE TABLE IF NOT EXISTS alembic_version (
                        version_num VARCHAR(32) NOT NULL,
                        CONSTRAINT alembic_version_pkc PRIMARY KEY (version_num)
                    )
                ''')
                
                # Inserir versão
                await conn.execute(
                    "INSERT INTO alembic_version (version_num) VALUES ($1)",
                    "consolidated_20250721_144511"
                )
                
                logger.info(f"✅ Migration executada para {db_name}")
            
            await conn.close()
            
        except Exception as e:
            logger.error(f"❌ Erro na migration de {db_name}: {e}")
    
    async def _create_basic_tables(self):
        """Cria tabelas básicas se migration não existir"""
        logger.info("🏗️ Criando tabelas básicas...")
        
        # Tabelas básicas por tipo de serviço
        basic_tables = {
            'auth_db': [
                '''CREATE TABLE IF NOT EXISTS users (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    email VARCHAR(255) UNIQUE NOT NULL,
                    password_hash VARCHAR(255) NOT NULL,
                    is_active BOOLEAN DEFAULT true,
                    created_at TIMESTAMP DEFAULT NOW()
                )''',
                '''CREATE TABLE IF NOT EXISTS auth_sessions (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    user_id UUID NOT NULL,
                    session_token VARCHAR(255) UNIQUE NOT NULL,
                    expires_at TIMESTAMP NOT NULL,
                    created_at TIMESTAMP DEFAULT NOW()
                )'''
            ],
            'tenants_db': [
                '''CREATE TABLE IF NOT EXISTS tenants (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    name VARCHAR(255) NOT NULL,
                    domain VARCHAR(100) UNIQUE,
                    is_active BOOLEAN DEFAULT true,
                    created_at TIMESTAMP DEFAULT NOW()
                )'''
            ],
            'restaurant_db': [
                '''CREATE TABLE IF NOT EXISTS restaurant_menus (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    tenant_id UUID NOT NULL,
                    name VARCHAR(255) NOT NULL,
                    price DECIMAL(10,2),
                    is_active BOOLEAN DEFAULT true,
                    created_at TIMESTAMP DEFAULT NOW()
                )'''
            ]
        }
        
        for coord_name, config in COORDINATORS_CONFIG.items():
            for db_name in config['databases']:
                if db_name in basic_tables:
                    await self._create_tables_for_database(config, db_name, basic_tables[db_name])
    
    async def _create_tables_for_database(self, config: Dict, db_name: str, tables: List[str]):
        """Cria tabelas para um banco específico"""
        try:
            conn = await asyncpg.connect(
                host=config['host'],
                port=config['port'],
                user=self.user,
                password=self.password,
                database=db_name
            )
            
            for table_sql in tables:
                await conn.execute(table_sql)
            
            logger.info(f"✅ Tabelas básicas criadas para {db_name}")
            await conn.close()
            
        except Exception as e:
            logger.error(f"❌ Erro ao criar tabelas para {db_name}: {e}")
    
    async def _setup_distributed_tables(self):
        """Configura distribuição de tabelas no Citus"""
        logger.info("🔗 Configurando distribuição de tabelas...")
        
        # Configurações de distribuição por banco
        distribution_config = {
            'auth_db': [
                {'table': 'users', 'key': 'id', 'shards': 32},
                {'table': 'auth_sessions', 'key': 'user_id', 'shards': 32}
            ],
            'tenants_db': [
                {'table': 'tenants', 'key': 'id', 'shards': 16}
            ],
            'restaurant_db': [
                {'table': 'restaurant_menus', 'key': 'tenant_id', 'shards': 64}
            ]
        }
        
        for coord_name, config in COORDINATORS_CONFIG.items():
            for db_name in config['databases']:
                if db_name in distribution_config:
                    await self._distribute_tables_for_database(config, db_name, distribution_config[db_name])
    
    async def _distribute_tables_for_database(self, config: Dict, db_name: str, tables: List[Dict]):
        """Distribui tabelas para um banco específico"""
        try:
            conn = await asyncpg.connect(
                host=config['host'],
                port=config['port'],
                user=self.user,
                password=self.password,
                database=db_name
            )
            
            for table_config in tables:
                table_name = table_config['table']
                dist_key = table_config['key']
                shard_count = table_config['shards']
                
                # Verificar se tabela existe
                table_exists = await conn.fetchval(
                    "SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = $1)",
                    table_name
                )
                
                if table_exists:
                    # Verificar se já está distribuída
                    is_distributed = await conn.fetchval(
                        "SELECT EXISTS (SELECT 1 FROM pg_dist_partition WHERE logicalrelid = $1::regclass)",
                        table_name
                    )
                    
                    if not is_distributed:
                        await conn.execute(
                            f"SELECT create_distributed_table($1, $2, shard_count => $3)",
                            table_name, dist_key, shard_count
                        )
                        logger.info(f"✅ Tabela {table_name} distribuída com chave {dist_key}")
                    else:
                        logger.info(f"ℹ️ Tabela {table_name} já está distribuída")
            
            await conn.close()
            
        except Exception as e:
            logger.error(f"❌ Erro ao distribuir tabelas para {db_name}: {e}")

async def main():
    """Função principal"""
    setup = CitusMigrationSetup()
    await setup.setup_citus_cluster()

if __name__ == "__main__":
    asyncio.run(main())
