"""
Database Configuration for Trix Microservices
=============================================

Configurações compartilhadas de banco de dados para todos os microserviços.
Suporta PostgreSQL, Citus Data, connection pooling e sharding.
"""

from typing import List, Dict, Any, Optional
from pydantic import Field, field_validator
from pydantic_settings import BaseSettings
import os


class DatabaseSettings(BaseSettings):
    """Configurações compartilhadas de banco de dados."""
    
    # Basic Database Configuration
    DB_HOST: str = Field(default="localhost", env="DB_HOST")
    DB_PORT: int = Field(default=5432, env="DB_PORT")
    DB_NAME: str = Field(default="trix_db", env="DB_NAME")
    DB_USER: str = Field(default="trix_user", env="DB_USER")
    DB_PASSWORD: str = Field(default="", env="DB_PASSWORD")
    DB_SCHEMA: str = Field(default="public", env="DB_SCHEMA")
    
    # Database URL (takes precedence over individual settings)
    DATABASE_URL: Optional[str] = Field(default=None, env="DATABASE_URL")
    
    # Connection Pool Settings
    DB_POOL_SIZE: int = Field(default=20, env="DB_POOL_SIZE")
    DB_MAX_OVERFLOW: int = Field(default=30, env="DB_MAX_OVERFLOW")
    DB_POOL_TIMEOUT: int = Field(default=30, env="DB_POOL_TIMEOUT")
    DB_POOL_RECYCLE: int = Field(default=3600, env="DB_POOL_RECYCLE")  # 1 hour
    DB_POOL_PRE_PING: bool = Field(default=True, env="DB_POOL_PRE_PING")
    
    # Citus Data Configuration (for distributed databases)
    CITUS_ENABLED: bool = Field(default=False, env="CITUS_ENABLED")
    CITUS_COORDINATOR_HOST: str = Field(default="citus-coordinator", env="CITUS_COORDINATOR_HOST")
    CITUS_COORDINATOR_PORT: int = Field(default=5432, env="CITUS_COORDINATOR_PORT")
    CITUS_WORKER_NODES: Optional[List[str]] = Field(
        default=None,
        env="CITUS_WORKER_NODES"
    )
    CITUS_SHARD_COUNT: int = Field(default=32, env="CITUS_SHARD_COUNT")
    CITUS_REPLICATION_FACTOR: int = Field(default=2, env="CITUS_REPLICATION_FACTOR")
    
    # Read Replicas Configuration
    READ_REPLICAS_ENABLED: bool = Field(default=False, env="READ_REPLICAS_ENABLED")
    READ_REPLICA_URLS: List[str] = Field(default=[], env="READ_REPLICA_URLS")
    
    # SSL Configuration
    DB_SSL_MODE: str = Field(default="prefer", env="DB_SSL_MODE")  # disable, allow, prefer, require
    DB_SSL_CERT: Optional[str] = Field(default=None, env="DB_SSL_CERT")
    DB_SSL_KEY: Optional[str] = Field(default=None, env="DB_SSL_KEY")
    DB_SSL_ROOT_CERT: Optional[str] = Field(default=None, env="DB_SSL_ROOT_CERT")
    
    # Performance Settings
    DB_ECHO: bool = Field(default=False, env="DB_ECHO")  # Log SQL queries
    DB_ECHO_POOL: bool = Field(default=False, env="DB_ECHO_POOL")  # Log connection pool
    DB_QUERY_TIMEOUT: int = Field(default=30, env="DB_QUERY_TIMEOUT")  # seconds
    DB_STATEMENT_TIMEOUT: int = Field(default=60, env="DB_STATEMENT_TIMEOUT")  # seconds
    
    # Migration Settings
    MIGRATION_AUTO_UPGRADE: bool = Field(default=False, env="MIGRATION_AUTO_UPGRADE")
    MIGRATION_TIMEOUT: int = Field(default=300, env="MIGRATION_TIMEOUT")  # 5 minutes
    
    @field_validator('CITUS_WORKER_NODES', mode='before')
    @classmethod
    def parse_worker_nodes(cls, v):
        """Parse worker nodes from string or list."""
        try:
            if v is None:
                return None
            if isinstance(v, str):
                # Handle empty string
                if not v.strip():
                    return None
                return [node.strip() for node in v.split(',') if node.strip()]
            elif isinstance(v, list):
                return v if v else None
            else:
                # Convert to string and try again
                return cls.parse_worker_nodes(str(v))
        except Exception:
            # If all else fails, return None
            return None
    
    @field_validator('READ_REPLICA_URLS', mode='before')
    @classmethod
    def parse_replica_urls(cls, v):
        """Parse replica URLs from string or list."""
        try:
            if v is None:
                return []
            if isinstance(v, str):
                if not v.strip():
                    return []
                return [url.strip() for url in v.split(',') if url.strip()]
            elif isinstance(v, list):
                return v
            else:
                return []
        except Exception:
            return []
    
    def get_database_url(self, async_driver: bool = True, service_name: str = None) -> str:
        """Gera URL do banco de dados."""
        # Try service-specific URL first
        if service_name:
            service_url_key = f"{service_name.upper()}_DATABASE_URL"
            service_url = os.getenv(service_url_key)
            if service_url:
                return service_url

        # Fallback to generic DATABASE_URL
        if self.DATABASE_URL:
            return self.DATABASE_URL

        # Choose driver
        driver = "postgresql+asyncpg" if async_driver else "postgresql+psycopg2"

        # Build URL
        password_part = f":{self.DB_PASSWORD}" if self.DB_PASSWORD else ""

        return f"{driver}://{self.DB_USER}{password_part}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"
    
    def get_citus_coordinator_url(self, async_driver: bool = True) -> str:
        """Gera URL do coordenador Citus."""
        driver = "postgresql+asyncpg" if async_driver else "postgresql+psycopg2"
        password_part = f":{self.DB_PASSWORD}" if self.DB_PASSWORD else ""
        
        return f"{driver}://{self.DB_USER}{password_part}@{self.CITUS_COORDINATOR_HOST}:{self.CITUS_COORDINATOR_PORT}/{self.DB_NAME}"
    
    def get_citus_worker_urls(self, async_driver: bool = True) -> List[str]:
        """Gera URLs dos workers Citus."""
        driver = "postgresql+asyncpg" if async_driver else "postgresql+psycopg2"
        password_part = f":{self.DB_PASSWORD}" if self.DB_PASSWORD else ""
        
        urls = []
        for worker in self.CITUS_WORKER_NODES:
            host, port = worker.split(':')
            url = f"{driver}://{self.DB_USER}{password_part}@{host}:{port}/{self.DB_NAME}"
            urls.append(url)
        
        return urls
    
    def get_connection_config(self) -> Dict[str, Any]:
        """Retorna configuração de conexão para SQLAlchemy."""
        config = {
            "pool_size": self.DB_POOL_SIZE,
            "max_overflow": self.DB_MAX_OVERFLOW,
            "pool_timeout": self.DB_POOL_TIMEOUT,
            "pool_recycle": self.DB_POOL_RECYCLE,
            "pool_pre_ping": self.DB_POOL_PRE_PING,
            "echo": self.DB_ECHO,
            "echo_pool": self.DB_ECHO_POOL
        }
        
        # Add SSL configuration if specified
        if self.DB_SSL_MODE != "disable":
            ssl_config = {"sslmode": self.DB_SSL_MODE}
            
            if self.DB_SSL_CERT:
                ssl_config["sslcert"] = self.DB_SSL_CERT
            if self.DB_SSL_KEY:
                ssl_config["sslkey"] = self.DB_SSL_KEY
            if self.DB_SSL_ROOT_CERT:
                ssl_config["sslrootcert"] = self.DB_SSL_ROOT_CERT
            
            config["connect_args"] = ssl_config
        
        return config
    
    def get_service_database_name(self, service_name: str) -> str:
        """Gera nome do banco de dados para um serviço específico."""
        return f"{service_name}_db"
    
    def get_service_schema_name(self, service_name: str) -> str:
        """Gera nome do schema para um serviço específico."""
        return f"{service_name}_schema"
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Global database settings instance
_database_settings: Optional[DatabaseSettings] = None


def get_database_settings() -> DatabaseSettings:
    """Retorna instância global das configurações de banco de dados."""
    global _database_settings
    if _database_settings is None:
        _database_settings = DatabaseSettings()
    return _database_settings


# Database Mappings for different services
DATABASE_MAPPINGS = {
    "auth_service": "auth_db",
    "user_service": "user_db", 
    "tenant_service": "tenant_db",
    "supplier_service": "supplier_db",
    "core_service": "core_db",
    "notification_service": "notification_db",
    "media_service": "media_db",
    "i18n_service": "i18n_db",
    "cdn_service": "cdn_db",
    "commerce_service": "commerce_db",
    "payment_service": "payment_db",
    "domain_service": "domain_db",
    "analytics_service": "analytics_db",
    "hr_service": "hr_db",
    "crm_service": "crm_db",
    "financial_service": "financial_db",
    "email_service": "email_db",
    "ghost_function_service": "ghost_db",
    "dns_bridge_service": "dns_db",
    "shared_database": "shared_db"
}


def get_service_database_name(service_name: str) -> str:
    """Retorna o nome do banco de dados para um serviço específico."""
    return DATABASE_MAPPINGS.get(service_name, f"{service_name}_db")


# Sharding Configuration for Citus Data
SHARDING_CONFIGS = {
    "tenant_service": {
        "shard_key": "tenant_id",
        "shard_count": 32,
        "colocated_tables": ["tenants", "tenant_settings", "player_profiles"]
    },
    "user_service": {
        "shard_key": "user_id",
        "shard_count": 32,
        "colocated_tables": ["users", "user_profiles", "user_associations"]
    },
    "cdn_service": {
        "shard_key": "tenant_id",
        "shard_count": 16,
        "colocated_tables": ["cache_entries", "storage_objects", "dns_records"]
    },
    "commerce_service": {
        "shard_key": "tenant_id",
        "shard_count": 32,
        "colocated_tables": ["products", "orders", "inventory"]
    },
    "restaurant_service": {
        "shard_key": "tenant_id",
        "shard_count": 32,
        "replication_factor": 2,
        "distributed_tables": [
            "menu_items",
            "restaurant_orders",
            "order_items",
            "restaurant_tables",
            "table_reservations",
            "kitchen_orders",
            "delivery_orders",
            "inventory_items",
            "pos_transactions",
            "restaurant_analytics"
        ],
        "reference_tables": [
            "menu_categories",
            "allergens",
            "customization_groups",
            "customization_options",
            "restaurant_areas",
            "kitchen_stations"
        ],
        "colocated_tables": ["menu_items", "order_items", "inventory_items", "restaurant_orders", "pos_transactions", "kitchen_orders", "restaurant_tables", "table_reservations"]
    }
}


# Connection Pool Configurations by Service Type
POOL_CONFIGS = {
    "high_traffic": {  # For services with high traffic (auth, user, tenant)
        "pool_size": 50,
        "max_overflow": 100,
        "pool_timeout": 30,
        "pool_recycle": 3600
    },
    "medium_traffic": {  # For services with medium traffic (commerce, cdn)
        "pool_size": 30,
        "max_overflow": 60,
        "pool_timeout": 30,
        "pool_recycle": 3600
    },
    "low_traffic": {  # For services with low traffic (analytics, hr)
        "pool_size": 10,
        "max_overflow": 20,
        "pool_timeout": 30,
        "pool_recycle": 3600
    }
}


def get_pool_config(traffic_type: str = "medium_traffic") -> Dict[str, Any]:
    """Retorna configuração de pool para um tipo de tráfego específico."""
    return POOL_CONFIGS.get(traffic_type, POOL_CONFIGS["medium_traffic"])


class CitusDataSettings(BaseSettings):
    """Configurações específicas do Citus Data para sharding distribuído."""

    # Citus Data Configuration
    CITUS_ENABLED: bool = Field(default=True, env="CITUS_ENABLED")
    CITUS_SHARD_COUNT: int = Field(default=32, env="CITUS_SHARD_COUNT")
    CITUS_REPLICATION_FACTOR: int = Field(default=2, env="CITUS_REPLICATION_FACTOR")
    CITUS_MAX_WORKER_NODES: int = Field(default=10, env="CITUS_MAX_WORKER_NODES")

    # Citus Performance Settings
    CITUS_ENABLE_REPARTITION_JOINS: bool = Field(default=True, env="CITUS_ENABLE_REPARTITION_JOINS")
    CITUS_ENABLE_FAST_PATH_ROUTER: bool = Field(default=True, env="CITUS_ENABLE_FAST_PATH_ROUTER")
    CITUS_LOG_MULTI_JOIN_ORDER: bool = Field(default=True, env="CITUS_LOG_MULTI_JOIN_ORDER")

    class Config:
        env_prefix = "CITUS_"
        case_sensitive = False


def get_citus_settings() -> CitusDataSettings:
    """Retorna configurações do Citus Data."""
    return CitusDataSettings()


def get_sharding_config(service_name: str) -> Dict[str, Any]:
    """Retorna configuração de sharding para um serviço específico."""
    return SHARDING_CONFIGS.get(service_name, {})
