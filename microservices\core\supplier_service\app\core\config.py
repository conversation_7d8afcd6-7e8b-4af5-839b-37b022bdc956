import os
from typing import List, Optional
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    # Service Info
    SERVICE_NAME: str = "supplier-service"
    SERVICE_VERSION: str = "2.0.0"
    SERVICE_PORT: int = int(os.getenv("SERVICE_PORT", "8017"))

    # Database (Citus Data Sharding)
    DATABASE_URL: str = os.getenv(
        "DATABASE_URL",
        "postgresql+asyncpg://supplier_user:supplier_pass@postgres-supplier:5432/supplier_db"
    )
    DATABASE_REPLICA_URLS: List[str] = os.getenv(
        "DATABASE_REPLICA_URLS",
        ""
    ).split(",") if os.getenv("DATABASE_REPLICA_URLS") else []
    PGBOUNCER_URL: str = os.getenv(
        "PGBOUNCER_URL",
        "postgresql+asyncpg://supplier_user:supplier_pass@pgbouncer-supplier:5432/supplier_db"
    )

    # Redis (Multiple instances for different purposes)
    REDIS_URL: str = os.getenv(
        "REDIS_URL",
        "redis://:redis123@redis:6379/9"
    )
    REDIS_STREAMS_URL: str = os.getenv(
        "REDIS_STREAMS_URL",
        "redis://:redis123@redis-streams:6379/0"
    )

    # Service Discovery (Updated URLs)
    AUTH_SERVICE_URL: str = os.getenv(
        "AUTH_SERVICE_URL",
        "http://trix-core-auth:8001"
    )
    USER_SERVICE_URL: str = os.getenv(
        "USER_SERVICE_URL",
        "http://trix-core-user:8002"
    )
    TENANT_SERVICE_URL: str = os.getenv(
        "TENANT_SERVICE_URL",
        "http://trix-core-tenant:8003"
    )
    CORE_SERVICE_URL: str = os.getenv(
        "CORE_SERVICE_URL",
        "http://trix-core-core:8004"
    )
    COMMERCE_SERVICE_URL: str = os.getenv(
        "COMMERCE_SERVICE_URL",
        "http://trix-core-commerce:8005"
    )
    NOTIFICATION_SERVICE_URL: str = os.getenv(
        "NOTIFICATION_SERVICE_URL",
        "http://trix-core-notification:8006"
    )
    I18N_SERVICE_URL: str = os.getenv(
        "I18N_SERVICE_URL",
        "http://trix-core-i18n:8007"
    )
    CDN_SERVICE_URL: str = os.getenv(
        "CDN_SERVICE_URL",
        "http://trix-core-cdn:8008"
    )
    MEDIA_SERVICE_URL: str = os.getenv(
        "MEDIA_SERVICE_URL",
        "http://trix-core-media:8009"
    )
    PAYMENT_SERVICE_URL: str = os.getenv(
        "PAYMENT_SERVICE_URL",
        "http://trix-core-payment:8010"
    )
    DOMAIN_SERVICE_URL: str = os.getenv(
        "DOMAIN_SERVICE_URL",
        "http://trix-core-domain:8011"
    )
    
    # Supplier Settings
    DEFAULT_SUPPLIER_STATUS: str = os.getenv("DEFAULT_SUPPLIER_STATUS", "active")
    AUTO_APPROVE_SUPPLIERS: bool = os.getenv("AUTO_APPROVE_SUPPLIERS", "false").lower() == "true"
    SUPPLIER_VERIFICATION_REQUIRED: bool = os.getenv("SUPPLIER_VERIFICATION_REQUIRED", "true").lower() == "true"
    
    # TVendor Settings
    TVENDOR_APPROVAL_REQUIRED: bool = os.getenv("TVENDOR_APPROVAL_REQUIRED", "true").lower() == "true"
    TVENDOR_MIN_RATING: float = float(os.getenv("TVENDOR_MIN_RATING", "4.0"))
    TVENDOR_MIN_ORDERS: int = int(os.getenv("TVENDOR_MIN_ORDERS", "10"))
    
    # Purchase Order Settings
    PO_AUTO_NUMBERING: bool = os.getenv("PO_AUTO_NUMBERING", "true").lower() == "true"
    PO_APPROVAL_REQUIRED: bool = os.getenv("PO_APPROVAL_REQUIRED", "true").lower() == "true"
    PO_DEFAULT_PAYMENT_TERMS: int = int(os.getenv("PO_DEFAULT_PAYMENT_TERMS", "30"))  # days
    
    # Performance Settings
    PERFORMANCE_TRACKING_ENABLED: bool = os.getenv("PERFORMANCE_TRACKING_ENABLED", "true").lower() == "true"
    RATING_SYSTEM_ENABLED: bool = os.getenv("RATING_SYSTEM_ENABLED", "true").lower() == "true"
    
    # Notification Settings
    EMAIL_NOTIFICATIONS_ENABLED: bool = os.getenv("EMAIL_NOTIFICATIONS_ENABLED", "true").lower() == "true"
    SMS_NOTIFICATIONS_ENABLED: bool = os.getenv("SMS_NOTIFICATIONS_ENABLED", "false").lower() == "true"
    
    # Integration Settings
    INVENTORY_SYNC_ENABLED: bool = os.getenv("INVENTORY_SYNC_ENABLED", "true").lower() == "true"
    PRODUCT_CATALOG_SYNC: bool = os.getenv("PRODUCT_CATALOG_SYNC", "true").lower() == "true"
    
    # Cache Settings
    CACHE_TTL: int = int(os.getenv("CACHE_TTL", "300"))  # 5 minutes
    SUPPLIER_CACHE_TTL: int = int(os.getenv("SUPPLIER_CACHE_TTL", "7200"))  # 2 hours

    # Messaging (Event-Driven Architecture)
    KAFKA_BOOTSTRAP_SERVERS: List[str] = os.getenv(
        "KAFKA_BOOTSTRAP_SERVERS",
        "kafka-broker-1:9092,kafka-broker-2:9092,kafka-broker-3:9092"
    ).split(",")
    KAFKA_ENABLED: bool = os.getenv("KAFKA_ENABLED", "true").lower() == "true"

    RABBITMQ_URL: str = os.getenv(
        "RABBITMQ_URL",
        "amqp://supplier_user:supplier_pass@rabbitmq:5672/supplier_vhost"
    )
    RABBITMQ_ENABLED: bool = os.getenv("RABBITMQ_ENABLED", "true").lower() == "true"

    # Security (HashiCorp Vault & OPA)
    VAULT_URL: str = os.getenv("VAULT_URL", "http://vault:8200")
    VAULT_TOKEN: str = os.getenv("VAULT_TOKEN", "")
    VAULT_ENABLED: bool = os.getenv("VAULT_ENABLED", "true").lower() == "true"

    OPA_URL: str = os.getenv("OPA_URL", "http://opa-service:8181")
    OPA_ENABLED: bool = os.getenv("OPA_ENABLED", "true").lower() == "true"

    # Service Mesh
    ISTIO_ENABLED: bool = os.getenv("ISTIO_ENABLED", "true").lower() == "true"
    MTLS_ENABLED: bool = os.getenv("MTLS_ENABLED", "true").lower() == "true"

    # Observability
    PROMETHEUS_ENABLED: bool = os.getenv("PROMETHEUS_ENABLED", "true").lower() == "true"
    JAEGER_ENABLED: bool = os.getenv("JAEGER_ENABLED", "true").lower() == "true"
    ELK_ENABLED: bool = os.getenv("ELK_ENABLED", "true").lower() == "true"
    METRICS_PORT: int = int(os.getenv("METRICS_PORT", "9090"))
    TRACING_ENDPOINT: str = os.getenv("TRACING_ENDPOINT", "http://jaeger-collector:14268/api/traces")

    # Performance & Scaling
    MAX_CONNECTIONS: int = int(os.getenv("MAX_CONNECTIONS", "100"))
    CONNECTION_POOL_SIZE: int = int(os.getenv("CONNECTION_POOL_SIZE", "20"))
    RATE_LIMIT_PER_MINUTE: int = int(os.getenv("RATE_LIMIT_PER_MINUTE", "1000"))
    AUTO_SCALING_ENABLED: bool = os.getenv("AUTO_SCALING_ENABLED", "true").lower() == "true"

    # Environment
    ENVIRONMENT: str = os.getenv("ENVIRONMENT", "development")
    DEBUG: bool = ENVIRONMENT == "development"

    class Config:
        env_file = ".env"


settings = Settings()
