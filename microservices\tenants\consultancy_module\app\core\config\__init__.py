"""
Consultancy Service Configuration Module
=======================================
Centralized configuration for the Consultancy Service.
"""

from .settings import (
    ConsultancyServiceSettings,
    get_settings,
    settings
)

from .database import (
    ConsultancyDatabaseConfig,
    consultancy_db_config,
    get_consultancy_db_session,
    get_consultancy_database_config
)

from .service_urls import (
    ServiceURLsConfig,
    service_urls_config,
    get_service_urls_config
)

from .vault import (
    ConsultancyVaultConfig,
    consultancy_vault_config,
    get_consultancy_vault_config
)

__all__ = [
    # Settings
    "ConsultancyServiceSettings",
    "get_settings",
    "settings",
    
    # Database
    "ConsultancyDatabaseConfig",
    "consultancy_db_config",
    "get_consultancy_db_session",
    "get_consultancy_database_config",
    
    # Service URLs
    "ServiceURLsConfig",
    "service_urls_config",
    "get_service_urls_config",
    
    # Vault
    "ConsultancyVaultConfig",
    "consultancy_vault_config",
    "get_consultancy_vault_config",
]
