services:

  # ========================================
  # DATABASE - CITUS DISTRIBUTED POSTGRESQL
  # ========================================

  citus-coordinator:
    image: citusdata/citus:12.1
    container_name: citus_coordinator
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-trix_db}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres}
      POSTGRES_MULTIPLE_DATABASES: "auth_db,users_db,tenants_db,core_db,notifications_db,payments_db,domains_db,media_db,supplier_db,hr_db,financial_db,crm_db,email_db,others_db,commerce_db"
      CITUS_HOST: citus-coordinator
    ports:
      - "5432:5432"
    volumes:
      - citus_coordinator_data:/var/lib/postgresql/data
      - ./scripts/init-multiple-databases.sh:/docker-entrypoint-initdb.d/init-multiple-databases.sh:ro
      - ./postgresql_optimized.conf:/etc/postgresql/postgresql.conf:ro
    networks:
      - network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres} -d ${POSTGRES_DB:-trix_db}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '0.5'

  citus-worker-1:
    image: citusdata/citus:12.1
    container_name: citus_worker_1
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-trix_db}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres}
      POSTGRES_MULTIPLE_DATABASES: "auth_db,users_db,tenants_db,core_db,notifications_db,payments_db,domains_db,media_db,supplier_db,hr_db,financial_db,crm_db,email_db,others_db,commerce_db"
      CITUS_HOST: citus-coordinator
    ports:
      - "5433:5432"
    volumes:
      - citus_worker_1_data:/var/lib/postgresql/data
      - ./scripts/init-multiple-databases.sh:/docker-entrypoint-initdb.d/init-multiple-databases.sh:ro
      - ./postgresql_optimized.conf:/etc/postgresql/postgresql.conf:ro
    networks:
      - network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres} -d ${POSTGRES_DB:-trix_db}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 3G
          cpus: '1.5'
        reservations:
          memory: 768M
          cpus: '0.375'

  citus-worker-2:
    image: citusdata/citus:12.1
    container_name: citus_worker_2
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-trix_db}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres}
      POSTGRES_MULTIPLE_DATABASES: "auth_db,users_db,tenants_db,core_db,notifications_db,payments_db,domains_db,media_db,supplier_db,hr_db,financial_db,crm_db,email_db,others_db,commerce_db"
      CITUS_HOST: citus-coordinator
    ports:
      - "5434:5432"
    volumes:
      - citus_worker_2_data:/var/lib/postgresql/data
      - ./scripts/init-multiple-databases.sh:/docker-entrypoint-initdb.d/init-multiple-databases.sh:ro
      - ./postgresql_optimized.conf:/etc/postgresql/postgresql.conf:ro
    networks:
      - network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-postgres} -d ${POSTGRES_DB:-trix_db}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 3G
          cpus: '1.5'
        reservations:
          memory: 768M
          cpus: '0.375'

  # ========================================
  # PGBOUNCER - CONNECTION POOLING
  # ========================================
  
  pgbouncer:
    build:
      context: ./docker/pgbouncer
      dockerfile: Dockerfile
    container_name: pgbouncer
    environment:
      - DATABASES_HOST=citus-coordinator
      - DATABASES_PORT=5432
      - DATABASES_USER=${POSTGRES_USER:-postgres}
      - DATABASES_PASSWORD=${POSTGRES_PASSWORD:-postgres}
      - DATABASES_DBNAME=${POSTGRES_DB:-trix_db}
      - POOL_MODE=transaction
      - SERVER_RESET_QUERY=DISCARD ALL
      - MAX_CLIENT_CONN=200
      - DEFAULT_POOL_SIZE=25
      - MIN_POOL_SIZE=5
      - RESERVE_POOL_SIZE=5
      - SERVER_LIFETIME=3600
      - SERVER_IDLE_TIMEOUT=600
      - LOG_CONNECTIONS=1
      - LOG_DISCONNECTIONS=1
      - LOG_POOLER_ERRORS=1
      - STATS_PERIOD=60
    ports:
      - "6432:6432"
    depends_on:
      citus-coordinator:
        condition: service_healthy
    networks:
      - network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -h localhost -p 6432 -U ${POSTGRES_USER:-postgres}"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '1.0'
        reservations:
          memory: 128M
          cpus: '0.25'

  # ========================================
  # REDIS - CACHE E SESSÕES
  # ========================================

  redis:
    image: redis:7-alpine
    container_name: redis
    ports:
      - "6379:6379"
    networks:
      - network
    restart: unless-stopped
    command: [
      "redis-server",
      "--maxmemory", "64mb",
      "--maxmemory-policy", "allkeys-lru",
      "--maxclients", "50",
      "--save", "",
      "--appendonly", "no",
      "--tcp-keepalive", "60",
      "--timeout", "300"
    ]
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3
      start_period: 5s
    # Recursos mínimos para desenvolvimento
    deploy:
      resources:
        limits:
          cpus: '0.3'
          memory: 128m
        reservations:
          cpus: '0.1'
          memory: 32m

  # ========================================
  # OBSERVABILITY - JAEGER TRACING
  # ========================================

  jaeger:
    image: jaegertracing/all-in-one:1.51
    container_name: jaeger
    ports:
      - "16686:16686" # Jaeger UI
      - "14268:14268" # HTTP collector
      - "6831:6831/udp" # UDP agent
      - "6832:6832/udp" # UDP agent
    environment:
      - COLLECTOR_OTLP_ENABLED=true
      - COLLECTOR_ZIPKIN_HOST_PORT=:9411
    networks:
      - network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:16686/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    # Recursos mínimos para desenvolvimento
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256m
        reservations:
          cpus: '0.1'
          memory: 64m

  # ========================================
  # MESSAGE BROKER - RABBITMQ
  # ========================================

  rabbitmq:
    image: rabbitmq:3.13-management-alpine
    container_name: rabbitmq
    ports:
      - "5672:5672" # AMQP
      - "15672:15672" # Management UI
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq/
    environment:
      - RABBITMQ_DEFAULT_USER=trixuser
      - RABBITMQ_DEFAULT_PASS=trixpass
    networks:
      - network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 10s
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256m
        reservations:
          cpus: '0.2'
          memory: 128m

  # ========================================
  # KAFKA CLUSTER - MESSAGE STREAMING
  # ========================================

  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
      ZOOKEEPER_SYNC_LIMIT: 2
    ports:
      - "2181:2181"
    volumes:
      - zookeeper_data:/var/lib/zookeeper/data
      - zookeeper_logs:/var/lib/zookeeper/log
    networks:
      - network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "bash", "-c", "echo 'ruok' | nc localhost 2181"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 10s
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256m
        reservations:
          cpus: '0.1'
          memory: 128m

  kafka-1:
    image: confluentinc/cp-kafka:7.4.0
    container_name: kafka_1
    depends_on:
      zookeeper:
        condition: service_healthy
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka-1:9092
      KAFKA_LISTENERS: PLAINTEXT://0.0.0.0:9092
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 3
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 3
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 2
      KAFKA_DEFAULT_REPLICATION_FACTOR: 3
      KAFKA_MIN_IN_SYNC_REPLICAS: 2
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_LOG_RETENTION_HOURS: 168
      KAFKA_LOG_SEGMENT_BYTES: **********
      KAFKA_LOG_RETENTION_CHECK_INTERVAL_MS: 300000
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
      KAFKA_NUM_PARTITIONS: 3
    ports:
      - "9092:9092"
    volumes:
      - kafka_1_data:/var/lib/kafka/data
    networks:
      - network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "kafka-broker-api-versions", "--bootstrap-server", "localhost:9092"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512m
        reservations:
          cpus: '0.2'
          memory: 256m

  kafka-2:
    image: confluentinc/cp-kafka:7.4.0
    container_name: kafka_2
    depends_on:
      zookeeper:
        condition: service_healthy
    environment:
      KAFKA_BROKER_ID: 2
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka-2:9092
      KAFKA_LISTENERS: PLAINTEXT://0.0.0.0:9092
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 3
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 3
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 2
      KAFKA_DEFAULT_REPLICATION_FACTOR: 3
      KAFKA_MIN_IN_SYNC_REPLICAS: 2
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_LOG_RETENTION_HOURS: 168
      KAFKA_LOG_SEGMENT_BYTES: **********
      KAFKA_LOG_RETENTION_CHECK_INTERVAL_MS: 300000
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
      KAFKA_NUM_PARTITIONS: 3
    ports:
      - "9093:9092"
    volumes:
      - kafka_2_data:/var/lib/kafka/data
    networks:
      - network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "kafka-broker-api-versions", "--bootstrap-server", "localhost:9092"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512m
        reservations:
          cpus: '0.2'
          memory: 256m

  kafka-3:
    image: confluentinc/cp-kafka:7.4.0
    container_name: kafka_3
    depends_on:
      zookeeper:
        condition: service_healthy
    environment:
      KAFKA_BROKER_ID: 3
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka-3:9092
      KAFKA_LISTENERS: PLAINTEXT://0.0.0.0:9092
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 3
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 3
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 2
      KAFKA_DEFAULT_REPLICATION_FACTOR: 3
      KAFKA_MIN_IN_SYNC_REPLICAS: 2
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_LOG_RETENTION_HOURS: 168
      KAFKA_LOG_SEGMENT_BYTES: **********
      KAFKA_LOG_RETENTION_CHECK_INTERVAL_MS: 300000
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
      KAFKA_NUM_PARTITIONS: 3
    ports:
      - "9094:9092"
    volumes:
      - kafka_3_data:/var/lib/kafka/data
    networks:
      - network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "kafka-broker-api-versions", "--bootstrap-server", "localhost:9092"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512m
        reservations:
          cpus: '0.2'
          memory: 256m

  # ========================================
  # API GATEWAY - KONG (USANDO DB PRINCIPAL)
  # ========================================

  kong-migrations:
    image: kong:latest
    container_name: kong_migrations
    command: "kong migrations bootstrap"
    environment:
      - KONG_DATABASE=postgres
      - KONG_PG_HOST=citus-coordinator
      - KONG_PG_USER=${POSTGRES_USER:-postgres}
      - KONG_PG_PASSWORD=${POSTGRES_PASSWORD:-postgres}
      - KONG_PG_DATABASE=${POSTGRES_DB:-trix_db}
    depends_on:
      citus-coordinator:
        condition: service_healthy
    networks:
      - network
    restart: on-failure

  kong:
    image: kong:latest
    container_name: kong_gateway
    environment:
      - KONG_DATABASE=postgres
      - KONG_PG_HOST=citus-coordinator
      - KONG_PG_USER=${POSTGRES_USER:-postgres}
      - KONG_PG_PASSWORD=${POSTGRES_PASSWORD:-postgres}
      - KONG_PG_DATABASE=${POSTGRES_DB:-trix_db}
      - KONG_PROXY_ACCESS_LOG=/dev/stdout
      - KONG_ADMIN_ACCESS_LOG=/dev/stdout
      - KONG_PROXY_ERROR_LOG=/dev/stderr
      - KONG_ADMIN_ERROR_LOG=/dev/stderr
      - KONG_ADMIN_LISTEN=0.0.0.0:8001, 0.0.0.0:8444 ssl
      - KONG_DECLARATIVE_CONFIG=/kong_config/kong.yml
    volumes:
      - ./kong.yml:/kong_config/kong.yml:ro
    depends_on:
      kong-migrations:
        condition: service_completed_successfully
    ports:
      - "8000:8000"
      - "8443:8443"
      - "8001:8001"
      - "8444:8444"
    networks:
      - network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "kong", "health"]
      interval: 10s
      timeout: 5s
      retries: 3

  # ========================================
  # WORKERS CELERY - OTIMIZADOS
  # ========================================

  worker:
    build:
      context: ./microservices/core/core_service
      dockerfile: docker/Dockerfile
      args:
        - BUILDKIT_INLINE_CACHE=1
    container_name: worker
    command: celery -A microservices.core.core_service.app.core.celery_app worker -l info --concurrency=2 --prefetch-multiplier=1
    volumes:
      - ./:/app
      - media_data:/media_data
      - ftp_data:/ftp_data
    env_file:
      - .env.docker
    environment:
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
    restart: unless-stopped
    depends_on:
      citus-coordinator:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - network
    # Recursos mínimos para desenvolvimento
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256m
        reservations:
          cpus: '0.1'
          memory: 64m

  # ========================================
  # CORE MICROSERVICES INDIVIDUAIS
  # ========================================

  # Auth Service
  auth-service:
    build:
      context: ./microservices/core
      dockerfile: auth_service/docker/Dockerfile
      target: production
    container_name: auth_service
    ports:
      - "8003:8001"
    env_file:
      - .env.docker
    volumes:
      - ./microservices/shared:/app/microservices/shared:ro
      - ./microservices/core/shared_lib:/app/microservices/core/shared_lib:ro
    depends_on:
      citus-coordinator:
        condition: service_healthy
      redis:
        condition: service_healthy
      vault:
        condition: service_healthy
    networks:
      - network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # User Service
  user-service:
    build:
      context: ./microservices/core
      dockerfile: user_service/docker/Dockerfile
      target: production
    container_name: user_service
    ports:
      - "8002:8002"
    env_file:
      - .env.docker
    volumes:
      - ./microservices/core/user_service:/app
      - ./microservices/shared:/app/microservices/shared:ro
      - ./microservices/core/shared_lib:/app/microservices/core/shared_lib:ro
    depends_on:
      citus-coordinator:
        condition: service_healthy
      redis:
        condition: service_healthy
      vault:
        condition: service_healthy
      kafka-1:
        condition: service_healthy
      kafka-2:
        condition: service_healthy
      kafka-3:
        condition: service_healthy
    networks:
      - network
    restart: unless-stopped

  # Tenant Service
  tenant-service:
    build:
      context: ./microservices/core
      dockerfile: tenant_service/docker/Dockerfile
    container_name: tenant_service
    ports:
      - "8004:8000"
    env_file:
      - .env.docker
    depends_on:
      citus-coordinator:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - network
    restart: unless-stopped

  # I18N Service
  i18n-service:
    build:
      context: ./microservices/core
      dockerfile: i18n_service/docker/Dockerfile
    container_name: i18n_service
    ports:
      - "8005:8008"
    env_file:
      - .env.docker
    depends_on:
      citus-coordinator:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - network
    restart: unless-stopped

  # Media System (integrado com FTP)
  media-system:
    build:
      context: ./microservices/core/media_system
      dockerfile: docker/Dockerfile
    container_name: media_system
    ports:
      - "8006:8000"
      - "2121:21"   # FTP (porta alternativa para evitar conflito)
      - "20000-20010:20000-20010"  # FTP passive ports
    volumes:
      - media_data:/media_data
      - ftp_data:/ftp_data
    env_file:
      - .env.docker
    depends_on:
      citus-coordinator:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - network
    restart: unless-stopped

  # Notification Service
  notification-service:
    build:
      context: ./microservices/core/notification_service
      dockerfile: docker/Dockerfile
    container_name: notification_service
    ports:
      - "8007:8000"
    env_file:
      - .env.docker
    depends_on:
      citus-coordinator:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    networks:
      - network
    restart: unless-stopped

  # Payment Service
  payment-service:
    build:
      context: ./microservices/core/payment_service
      dockerfile: docker/Dockerfile
    container_name: payment_service
    ports:
      - "8008:8000"
    env_file:
      - .env.docker
    depends_on:
      citus-coordinator:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - network
    restart: unless-stopped

  # Payment Service
  domain-service:
    build:
      context: ./microservices/core/domain_service
      dockerfile: docker/Dockerfile
    container_name: domain_service
    ports:
      - "8015:8015"
    env_file:
      - .env.docker
    depends_on:
      citus-coordinator:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - network
    restart: unless-stopped

  # Domain Service
  cdn-service:
    build:
      context: ./microservices/core/cdn_service
      dockerfile: docker/Dockerfile
    container_name: cdn_service
    ports:
      - "8027:8027"
    env_file:
      - .env.docker
    depends_on:
      citus-coordinator:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - network
    restart: unless-stopped

  beat:
    build:
      context: ./microservices/core/core_service
      dockerfile: docker/Dockerfile
      args:
        - BUILDKIT_INLINE_CACHE=1
    container_name: beat
    command: celery -A microservices.core.core_service.app.core.celery_app beat -l info
    volumes:
      - ./:/app
    env_file:
      - .env.docker
    environment:
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
    restart: unless-stopped
    depends_on:
      citus-coordinator:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - network
    # Recursos mínimos para desenvolvimento
    deploy:
      resources:
        limits:
          cpus: '0.3'
          memory: 128m
        reservations:
          cpus: '0.1'
          memory: 32m

  # Supplier Service
  supplier-service:
    build:
      context: ./microservices/core/supplier_service
      dockerfile: docker/Dockerfile
    container_name: supplier_service
    ports:
      - "8017:8017"
    env_file:
      - .env.docker
    depends_on:
      citus-coordinator:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - network
    restart: unless-stopped

  # Ghost Function Service
  ghost-function-service:
    build:
      context: .
      dockerfile: ./microservices/core/ghost_function_service/docker/Dockerfile
    container_name: ghost-function-service
    ports:
      - "8026:8026"
    env_file:
      - .env.docker
    depends_on:
      citus-coordinator:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    networks:
      - network
    restart: unless-stopped

  # Synapse AI Service
  synapse-ai-service:
    build:
      context: ./microservices/core/synapse_ai_service
      dockerfile: docker/Dockerfile
    container_name: synapse_ai_service
    ports:
      - "8013:8000"
    env_file:
      - .env.docker
    depends_on:
      citus-coordinator:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - network
    restart: unless-stopped

  # ========================================
  # SHARED MODULES
  # ========================================

  # HR Module
  hr-module:
    build:
      context: .
      dockerfile: ./microservices/shared/hr_module/docker/Dockerfile
    container_name: hr_module
    ports:
      - "8020:8000"
    env_file:
      - .env.docker
    environment:
      # Citus Configuration - Production
      - CITUS_COORDINATOR_HOST=citus-coordinator
      - CITUS_COORDINATOR_PORT=5432
      - CITUS_WORKER_NODES=citus-worker-1:5432,citus-worker-2:5432
      - ENABLE_CITUS_SHARDING=true
      - DATABASE_URL=*****************************************************/hr_db
    depends_on:
      citus-coordinator:
        condition: service_healthy
      citus-worker-1:
        condition: service_healthy
      citus-worker-2:
        condition: service_healthy
      pgbouncer:
        condition: service_healthy
      redis:
        condition: service_healthy
      kafka-1:
        condition: service_healthy
      kafka-2:
        condition: service_healthy
      kafka-3:
        condition: service_healthy
    networks:
      - network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Financial Module
  financial-module:
    build:
      context: .
      dockerfile: ./microservices/shared/financial_module/docker/Dockerfile
    container_name: financial_module
    ports:
      - "8021:8000"
    env_file:
      - .env.docker
    depends_on:
      citus-coordinator:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - network
    restart: unless-stopped

  # CRM Module
  crm-module:
    build:
      context: .
      dockerfile: ./microservices/shared/crm_module/docker/Dockerfile
    container_name: crm_module
    ports:
      - "8022:8000"
    env_file:
      - .env.docker
    depends_on:
      citus-coordinator:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - network
    restart: unless-stopped

  # Email Module
  email-module:
    build:
      context: ./microservices/shared/email_module
      dockerfile: docker/Dockerfile
    container_name: email_module
    ports:
      - "8023:8000"
    env_file:
      - .env.docker
    depends_on:
      citus-coordinator:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    networks:
      - network
    restart: unless-stopped

  # ========================================
  # TENANT MODULES
  # ========================================

  # Restaurant Module (with Shared Library Integration)
  restaurant-module:
    build:
      context: ./microservices
      dockerfile: tenants/restaurant_module/docker/Dockerfile
    container_name: restaurant_module
    ports:
      - "8030:8004"
    env_file:
      - .env.docker
    depends_on:
      citus-coordinator:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./microservices/core/shared_lib:/app/microservices/core/shared_lib:ro
    networks:
      - network
    restart: unless-stopped

  # Consultancy Module
  consultancy-module:
    build:
      context: ./microservices
      dockerfile: tenants/consultancy_module/docker/Dockerfile
    container_name: consultancy_module
    ports:
      - "8031:8000"
    env_file:
      - .env.docker
    depends_on:
      citus-coordinator:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - network
    restart: unless-stopped

  # ========================================
  # FRONTEND - NEXT.JS OTIMIZADO
  # ========================================

  frontend:
    container_name: frontend
    build:
      context: ./microservices/frontend
      dockerfile: Dockerfile
      args:
        - BUILDKIT_INLINE_CACHE=1
        - BUILDKIT_PROGRESS=plain
      no_cache: true  # Força rebuild completo para garantir versões atualizadas
    command: npm run dev
    ports:
      - "3000:3000"
    env_file:
      - .env.docker
    environment:
      - NODE_ENV=development
      - NEXT_TELEMETRY_DISABLED=1
      # Optimized file watching for hot reload
      - WATCHPACK_POLLING=true
      - CHOKIDAR_USEPOLLING=true
      - CHOKIDAR_INTERVAL=300
      - WATCHFILES_FORCE_POLLING=true
      - FORCE_COLOR=1
      # Disable inotify (doesn't work with Docker bind mounts)
      - DISABLE_INOTIFY=true
      - NO_PROXY=localhost,127.0.0.1,.network
      - NODE_OPTIONS=--max-old-space-size=3072
      # Turbopack optimizations
      - TURBOPACK=1
      - NEXT_PRIVATE_SKIP_SIZE_LIMIT_CHECK=1
      - TURBO_FORCE_POLLING=true
      - TURBOPACK_WATCH_POLL=300
      # Development specific
      - NODE_NO_WARNINGS=1
    volumes:
      - ./microservices/frontend:/app
      - /app/node_modules
      - /app/.next
      - frontend_next_cache:/app/.next/cache
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 3G
        reservations:
          cpus: '0.5'
          memory: 1G
    networks:
      - network
    depends_on:
      - auth-service
      - user-service
      - tenant-service
      - i18n-service
      - media-system
      - notification-service
      - payment-service
      - domain-service
      - cdn-service
      - supplier-service
      - ghost-function-service
      - synapse-ai-service

  # ========================================
  # ORDERPAD - EXPO REACT NATIVE (DISABLED - NO DOCKERFILE)
  # ========================================

  # orderpad:
  #   container_name: orderpad
  #   build:
  #     context: ./microservices/applications/restaurant/orderpad
  #     dockerfile: Dockerfile.dev
  #     args:
  #       - BUILDKIT_INLINE_CACHE=1
  #       - BUILDKIT_PROGRESS=plain
  #     no_cache: true
  #   ports:
  #     - "8081:8081"  # Metro Bundler
  #     - "19000:19000"  # Expo DevTools
  #     - "19001:19001"  # Expo DevTools WebSocket
  #     - "19002:19002"  # Expo DevTools Tunnel
  #   environment:
  #     - NODE_ENV=development
  #     - EXPO_DEVTOOLS_LISTEN_ADDRESS=0.0.0.0
  #     - EXPO_CLI_NO_CACHE=1
  #     - EXPO_NO_TELEMETRY=1
  #     - EXPO_NO_UPDATE_CHECK=1
  #     # File watching optimizations
  #     - CHOKIDAR_USEPOLLING=true
  #     - CHOKIDAR_INTERVAL=300
  #     - WATCHPACK_POLLING=true
  #     - WATCHFILES_FORCE_POLLING=true
  #     - FORCE_COLOR=1
  #     - NODE_OPTIONS=--max-old-space-size=2048
  #     # Trix API Configuration
  #     - EXPO_PUBLIC_API_URL=http://localhost:8000
  #     - EXPO_PUBLIC_WS_URL=ws://localhost:8000
  #     - EXPO_PUBLIC_ENV=development
  #     - EXPO_PUBLIC_DEBUG=true
  #     - EXPO_PUBLIC_APP_NAME=Trix Orderpad
  #     - EXPO_PUBLIC_APP_VERSION=1.0.0
  #     - EXPO_PUBLIC_TIMEOUT=30000
  #   volumes:
  #     - ./microservices/applications/restaurant/orderpad:/app
  #     - /app/node_modules
  #     - orderpad_expo_cache:/app/.expo
  #   restart: unless-stopped
  #   deploy:
  #     resources:
  #       limits:
  #         cpus: '1.5'
  #         memory: 2G
  #       reservations:
  #         cpus: '0.3'
  #         memory: 512m
  #   networks:
  #     - network
  #   depends_on:
  #     - backend

  # ========================================
  # FRONTEND & ORDERPAD AUTO-WATCHER SERVICE
  # ========================================
  frontend-watcher:
    container_name: frontend_watcher
    image: alpine:latest
    volumes:
      - ./microservices/frontend/src:/watch/frontend/src:ro
      - ./microservices/frontend/public:/watch/frontend/public:ro
      - ./microservices/frontend/components.json:/watch/frontend/components.json:ro
      - ./microservices/frontend/next.config.js:/watch/frontend/next.config.js:ro
      - ./microservices/frontend/tailwind.config.js:/watch/frontend/tailwind.config.js:ro
      - ./microservices/frontend/postcss.config.js:/watch/frontend/postcss.config.js:ro
      - ./microservices/frontend/tsconfig.json:/watch/frontend/tsconfig.json:ro
      # - ./microservices/applications/restaurant/orderpad/src:/watch/orderpad/src:ro
      # - ./microservices/applications/restaurant/orderpad/app:/watch/orderpad/app:ro
      - /var/run/docker.sock:/var/run/docker.sock
    command: >
      sh -c "
        apk add --no-cache docker-cli curl findutils &&
        echo '🚀 Starting automatic file watcher for Frontend...' &&
        echo '📁 Monitoring:' &&
        echo '   - Frontend: /watch/frontend/src, /watch/frontend/public' &&
        echo '   - Config files: next.config.js, tailwind.config.js, tsconfig.json, etc.' &&
        # echo '   - Orderpad: /watch/orderpad/src, /watch/orderpad/app' &&
        echo '🔄 Will restart containers on file changes' &&
        echo '⏱️  Polling every 2 seconds for changes' &&
        echo '----------------------------------------' &&

        # Create initial checksums for frontend (including config files)
        find /watch/frontend -type f \( -name '*.ts' -o -name '*.tsx' -o -name '*.js' -o -name '*.jsx' -o -name '*.css' -o -name '*.scss' -o -name '*.json' \) | sort | xargs md5sum > /tmp/frontend_last_checksums 2>/dev/null || true &&

        # Create initial checksums for orderpad (DISABLED)
        # find /watch/orderpad -type f -name '*.ts' -o -name '*.tsx' -o -name '*.js' -o -name '*.jsx' | sort | xargs md5sum > /tmp/orderpad_last_checksums 2>/dev/null || true &&

        while true; do
          sleep 2 &&

          # Check frontend changes (including config files)
          find /watch/frontend -type f \( -name '*.ts' -o -name '*.tsx' -o -name '*.js' -o -name '*.jsx' -o -name '*.css' -o -name '*.scss' -o -name '*.json' \) | sort | xargs md5sum > /tmp/frontend_current_checksums 2>/dev/null || true &&

          if ! cmp -s /tmp/frontend_last_checksums /tmp/frontend_current_checksums; then
            echo '📝 Frontend file change detected!' &&
            echo '🔄 Restarting frontend container...' &&
            echo '⏱️ Timestamp: '$(date '+%Y-%m-%d %H:%M:%S') &&
            docker restart frontend &&
            echo '✅ Frontend container restarted successfully!' &&
            cp /tmp/frontend_current_checksums /tmp/frontend_last_checksums &&
            echo '⏳ Waiting 5 seconds for Next.js to initialize...' &&
            sleep 5 &&
            echo '🚀 Frontend ready with Turbopack!'
          fi &&

          # Check orderpad changes (DISABLED)
          # find /watch/orderpad -type f -name '*.ts' -o -name '*.tsx' -o -name '*.js' -o -name '*.jsx' | sort | xargs md5sum > /tmp/orderpad_current_checksums 2>/dev/null || true &&

          # if ! cmp -s /tmp/orderpad_last_checksums /tmp/orderpad_current_checksums; then
          #   echo '📱 Orderpad file change detected!' &&
          #   echo '🔄 Restarting orderpad container...' &&
          #   docker restart orderpad &&
          #   echo '✅ Orderpad container restarted successfully!' &&
          #   cp /tmp/orderpad_current_checksums /tmp/orderpad_last_checksums &&
          #   echo '⏳ Waiting 3 seconds...' &&
          #   sleep 3 &&
          #   echo '----------------------------------------'
          # fi
        done
      "
    networks:
      - network
    depends_on:
      - frontend
      # - orderpad
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.1'
          memory: 128M

  # ========================================
  # EMAIL SERVICES - OTIMIZADOS
  # ========================================

  postfix:
    build: ./docker/postfix
    container_name: postfix
    hostname: ${EMAIL_SERVER_HOSTNAME:-mail.localhost}
    environment:
      POSTGRES_HOST: citus-coordinator
      POSTGRES_USER: ${POSTGRES_USER:-trixuser}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-trixpass}
      POSTGRES_DB: ${POSTGRES_DB:-trixdb}
    ports:
      - "25:25"
      - "587:587"
    volumes:
      - ./docker/postfix/config/:/etc/postfix/
      - ./docker/postfix/sql/:/etc/postfix/sql/
      - ssl_certs:/etc/letsencrypt
      - postfix_spool:/var/spool/postfix
    depends_on:
      - citus-coordinator
      - dovecot
    restart: unless-stopped
    networks:
      - network
    # Recursos mínimos para desenvolvimento
    deploy:
      resources:
        limits:
          cpus: '0.3'
          memory: 128m
        reservations:
          cpus: '0.1'
          memory: 32m

  dovecot:
    build: ./docker/dovecot
    container_name: dovecot
    hostname: ${EMAIL_SERVER_HOSTNAME:-mail.localhost}
    environment:
      POSTGRES_HOST: db
      POSTGRES_USER: ${POSTGRES_USER:-trixuser}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-trixpass}
      POSTGRES_DB: ${POSTGRES_DB:-trixdb}
    ports:
      - "143:143"
      - "993:993"
    volumes:
      - ./docker/dovecot/config/:/etc/dovecot/
      - email_storage:/var/vmail
      - ssl_certs:/etc/letsencrypt
    depends_on:
      - citus-coordinator
    restart: unless-stopped
    networks:
      - network
    # Recursos mínimos para desenvolvimento
    deploy:
      resources:
        limits:
          cpus: '0.3'
          memory: 128m
        reservations:
          cpus: '0.1'
          memory: 32m

  rspamd:
    build: ./docker/rspamd
    container_name: rspamd
    hostname: rspamd
    volumes:
      - ./docker/rspamd/config/local.d:/etc/rspamd/local.d
      - ./docker/rspamd/config/override.d:/etc/rspamd/override.d
      - rspamd_data:/var/lib/rspamd
    ports:
      - "11334:11334"
    restart: unless-stopped
    networks:
      - network
    # Recursos mínimos para desenvolvimento
    deploy:
      resources:
        limits:
          cpus: '0.3'
          memory: 128m
        reservations:
          cpus: '0.1'
          memory: 32m

  # ========================================
  # PROXY E LOAD BALANCER
  # ========================================

  traefik:
    image: traefik:v2.10
    container_name: traefik_instance
    command:
      - "--api.insecure=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.web.address=:80"
      - "--entrypoints.websecure.address=:443"
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"
    volumes:
      - "/var/run/docker.sock:/var/run/docker.sock:ro"
      - "traefik_certs:/letsencrypt"
    restart: unless-stopped
    networks:
      - network
    # Recursos mínimos para desenvolvimento
    deploy:
      resources:
        limits:
          cpus: '0.3'
          memory: 128m
        reservations:
          cpus: '0.1'
          memory: 32m

  # ========================================
  # ANTIVIRUS E SEGURANÇA - OTIMIZADOS
  # ========================================

  clamav:
    image: clamav/clamav:latest
    container_name: clamav
    volumes:
      - clamav_data:/var/lib/clamav
    restart: unless-stopped
    networks:
      - network
    # Recursos mínimos para desenvolvimento
    deploy:
      resources:
        limits:
          cpus: '0.3'
          memory: 256m
        reservations:
          cpus: '0.1'
          memory: 64m

  # ========================================
  # FTP SERVER - INTEGRADO AO MEDIA SYSTEM
  # ========================================
  # FTP removido - agora integrado ao media-system (porta 21)

  # ========================================
  # HASHICORP VAULT - SECRETS MANAGEMENT (FREE)
  # ========================================
  vault:
    image: hashicorp/vault:latest
    container_name: vault
    cap_add:
      - IPC_LOCK
    environment:
      - VAULT_DEV_ROOT_TOKEN_ID=dev-root-token
      - VAULT_DEV_LISTEN_ADDRESS=0.0.0.0:8200
      - VAULT_API_ADDR=http://0.0.0.0:8200
      - VAULT_CLUSTER_ADDR=http://0.0.0.0:8201
      - VAULT_COMPANY_ID=d6faa240-1cc7-4271-9223-baa60577f607
    ports:
      - "8200:8200"
    volumes:
      - vault_data:/vault/data
      - vault_logs:/vault/logs
    networks:
      - network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "sh", "-c", "VAULT_ADDR=http://127.0.0.1:8200 vault status"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    # Recursos mínimos para desenvolvimento
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256m
        reservations:
          cpus: '0.1'
          memory: 64m

  # ========================================
  # OPEN POLICY AGENT (OPA) - AUTHORIZATION
  # ========================================
  opa:
    image: openpolicyagent/opa:latest-envoy
    container_name: opa
    command: ["run", "--server", "--addr", "0.0.0.0:8181", "--log-level", "debug"]
    ports:
      - "8181:8181"
    volumes:
      - ./microservices/core/shared_lib/infrastructure/opa/policies:/policies
    networks:
      - network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8181/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 10s
    # Recursos mínimos para desenvolvimento
    deploy:
      resources:
        limits:
          cpus: '0.3'
          memory: 128m
        reservations:
          cpus: '0.1'
          memory: 32m

# ========================================
# VOLUMES - PERSISTÊNCIA DE DADOS
# ========================================
volumes:
  # Citus cluster volumes
  citus_coordinator_data:
    driver: local
  citus_worker_1_data:
    driver: local
  citus_worker_2_data:
    driver: local
  # Message broker volumes
  rabbitmq_data:
    driver: local
  # Kafka cluster volumes
  zookeeper_data:
    driver: local
  zookeeper_logs:
    driver: local
  kafka_1_data:
    driver: local
  kafka_2_data:
    driver: local
  kafka_3_data:
    driver: local
  # Application volumes
  ssl_certs:
    driver: local
  email_storage:
    driver: local
  postfix_spool:
    driver: local
  rspamd_data:
    driver: local
  clamav_data:
    driver: local
  traefik_certs:
    driver: local
  ftp_data:
    driver: local
  ftp_passwd:
    driver: local
  media_data:
    driver: local
  ai_models:
    driver: local
  frontend_next_cache:
    driver: local
  vault_data:
    driver: local
  vault_logs:
    driver: local
  # orderpad_expo_cache:
  #   driver: local

# ========================================
# NETWORKS - COMUNICAÇÃO INTERNA
# ========================================
networks:
  network:
    driver: bridge
    driver_opts:
      com.docker.network.bridge.name: br0