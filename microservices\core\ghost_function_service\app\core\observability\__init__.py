"""
Ghost Function Service - Observability Module

Exports observability components specific to the Ghost Function Service.
Integrates with shared_lib for common observability patterns.

Author: Trix Platform Team
"""

from .metrics import (
    ghost_metrics,
    ghost_requests_counter,
    failover_requests_counter,
    proxy_duration_histogram,
    hibernation_state_gauge,
    circuit_breaker_state_gauge,
    service_health_gauge
)

from .logging import get_ghost_logger
from .tracing import get_ghost_tracer

__all__ = [
    # Metrics
    "ghost_metrics",
    "ghost_requests_counter", 
    "failover_requests_counter",
    "proxy_duration_histogram",
    "hibernation_state_gauge",
    "circuit_breaker_state_gauge",
    "service_health_gauge",
    
    # Logging
    "get_ghost_logger",
    
    # Tracing
    "get_ghost_tracer"
]
