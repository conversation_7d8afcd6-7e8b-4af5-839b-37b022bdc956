apiVersion: v1
kind: ConfigMap
metadata:
  name: supplier-service-ghost-config
  namespace: trix-core
  labels:
    app: supplier-service
    component: ghost-function
    version: v2.0.0
data:
  # Ghost Function Service Configuration for Supplier Service
  service_name: "supplier-service"
  service_port: "8017"
  
  # Monitoring Configuration
  monitoring_interval: "30s"
  health_check_timeout: "10s"
  recovery_timeout: "60s"
  alert_threshold: "3"
  max_retry_attempts: "5"
  
  # Health Check Endpoints
  health_endpoints: |
    - path: "/health"
      method: "GET"
      timeout: "5s"
      critical: true
    - path: "/api/v1/suppliers/health"
      method: "GET"
      timeout: "10s"
      critical: true
    - path: "/api/v1/tvendors/health"
      method: "GET"
      timeout: "10s"
      critical: true
    - path: "/api/v1/purchase-orders/health"
      method: "GET"
      timeout: "10s"
      critical: true
    - path: "/api/v1/metrics/health"
      method: "GET"
      timeout: "10s"
      critical: false
    - path: "/ready"
      method: "GET"
      timeout: "5s"
      critical: false
  
  # Recovery Actions
  recovery_actions: |
    database_connection_lost:
      script: "/scripts/recover_database.sh"
      timeout: "120s"
      max_attempts: 3
    
    kafka_connection_failed:
      script: "/scripts/recover_messaging.sh"
      timeout: "60s"
      max_attempts: 5
    
    high_memory_usage:
      script: "/scripts/clear_cache.sh"
      timeout: "30s"
      max_attempts: 2
    
    slow_response_time:
      script: "/scripts/optimize_performance.sh"
      timeout: "90s"
      max_attempts: 3
    
    tvendor_system_failure:
      script: "/scripts/recover_tvendor_system.sh"
      timeout: "180s"
      max_attempts: 2
  
  # Performance Thresholds
  performance_thresholds: |
    response_time_p95: "500ms"
    response_time_p99: "1000ms"
    error_rate: "1%"
    memory_usage: "85%"
    cpu_usage: "80%"
    database_connections: "90%"
    kafka_lag: "1000"
  
  # Business Metrics Monitoring
  business_metrics: |
    supplier_registration_rate:
      threshold: "10/hour"
      alert_on_drop: true
    
    tvendor_approval_rate:
      threshold: "80%"
      alert_on_drop: true
    
    purchase_order_volume:
      threshold: "100/hour"
      alert_on_drop: true
    
    commission_calculation_accuracy:
      threshold: "99.9%"
      alert_on_drop: true
    
    supplier_performance_score:
      threshold: "4.0/5.0"
      alert_on_drop: true
  
  # Circuit Breaker Configuration
  circuit_breaker: |
    failure_threshold: 5
    recovery_timeout: "30s"
    half_open_max_calls: 3
    
    services:
      - name: "tenant-service"
        endpoint: "http://tenant-service:8003"
        critical: true
      - name: "product-service"
        endpoint: "http://product-service:8007"
        critical: true
      - name: "user-service"
        endpoint: "http://user-service:8002"
        critical: false
      - name: "payment-service"
        endpoint: "http://payment-service:8009"
        critical: true
  
  # Alert Configuration
  alerts: |
    high_error_rate:
      condition: "error_rate > 1%"
      duration: "5m"
      severity: "critical"
      
    slow_response:
      condition: "response_time_p95 > 500ms"
      duration: "2m"
      severity: "warning"
      
    database_issues:
      condition: "database_connection_failures > 0"
      duration: "1m"
      severity: "critical"
      
    memory_pressure:
      condition: "memory_usage > 85%"
      duration: "3m"
      severity: "warning"
      
    tvendor_system_down:
      condition: "tvendor_health_check_failures > 2"
      duration: "1m"
      severity: "critical"
      
    supplier_onboarding_issues:
      condition: "supplier_registration_failures > 5"
      duration: "5m"
      severity: "warning"
  
  # Auto-scaling Configuration
  auto_scaling: |
    enabled: true
    min_replicas: 2
    max_replicas: 20
    
    scale_up_triggers:
      - metric: "cpu_usage"
        threshold: "70%"
        duration: "2m"
      - metric: "memory_usage"
        threshold: "75%"
        duration: "2m"
      - metric: "request_rate"
        threshold: "1000/s"
        duration: "1m"
    
    scale_down_triggers:
      - metric: "cpu_usage"
        threshold: "30%"
        duration: "5m"
      - metric: "memory_usage"
        threshold: "40%"
        duration: "5m"
      - metric: "request_rate"
        threshold: "100/s"
        duration: "10m"
  
  # Logging Configuration
  logging: |
    level: "INFO"
    format: "json"
    
    structured_fields:
      - "timestamp"
      - "level"
      - "service"
      - "trace_id"
      - "span_id"
      - "tenant_id"
      - "supplier_id"
      - "tvendor_id"
      - "order_id"
    
    sensitive_fields:
      - "password"
      - "token"
      - "api_key"
      - "credit_card"
      - "bank_account"
  
  # Backup and Recovery
  backup_recovery: |
    database_backup:
      enabled: true
      schedule: "0 2 * * *"  # Daily at 2 AM
      retention: "30d"
      
    configuration_backup:
      enabled: true
      schedule: "0 1 * * 0"  # Weekly on Sunday at 1 AM
      retention: "90d"
      
    disaster_recovery:
      rpo: "1h"  # Recovery Point Objective
      rto: "15m"  # Recovery Time Objective
      
  # Feature Flags
  feature_flags: |
    tvendor_auto_approval: false
    advanced_metrics: true
    real_time_notifications: true
    bulk_operations: true
    performance_optimization: true
    
---
apiVersion: v1
kind: Secret
metadata:
  name: supplier-service-ghost-secrets
  namespace: trix-core
  labels:
    app: supplier-service
    component: ghost-function
type: Opaque
stringData:
  # Ghost Function Service Secrets
  ghost_function_api_key: "ghost-supplier-service-key-2024"
  monitoring_webhook_url: "https://monitoring.trix.com/webhooks/supplier-service"
  alert_webhook_url: "https://alerts.trix.com/webhooks/supplier-service"
  
  # External Service API Keys
  notification_service_key: "notif-supplier-key-2024"
  metrics_service_key: "metrics-supplier-key-2024"
  
  # Recovery Script Credentials
  database_recovery_token: "db-recovery-token-2024"
  messaging_recovery_token: "msg-recovery-token-2024"
