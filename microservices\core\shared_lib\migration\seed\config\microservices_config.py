"""
Configuração de Microserviços para o Sistema de Seed Distribuído
================================================================

Define todos os microserviços, suas configurações de banco de dados,
prioridades de execução e dependências.
"""

import os
from typing import Dict, List, Optional

# Configuração completa de todos os microserviços
MICROSERVICES_SEEDS = {
    # ========================================
    # CORE SERVICES (Priority 1-10)
    # ========================================
    'ghost_function': {
        'module': 'core_services.ghost_function',
        'db_url': os.getenv(
            'GHOST_DATABASE_URL',
            'postgresql+asyncpg://postgres:postgres@citus_coordinator:5432/trix_db'
        ),
        'priority': 0,  # Executado primeiro (crítico para resiliência)
        'depends_on': [],
        'health_check_timeout': 60,
        'retry_attempts': 5,
        'description': 'Sistema de resiliência e proxy inteligente'
    },

    'auth': {
        'module': 'core_services.auth',
        'db_url': os.getenv(
            'AUTH_DATABASE_URL',
            'postgresql+asyncpg://trixuser:trixpass@db:5432/trixdb'
        ),
        'priority': 1,
        'depends_on': ['ghost_function'],  # Depende do ghost function para resiliência
        'health_check_timeout': 30,
        'retry_attempts': 3,
        'description': 'Sistema de autenticação e autorização'
    },

    'users': {
        'module': 'core_services.users',
        'db_url': os.getenv(
            'USERS_DATABASE_URL',
            'postgresql+asyncpg://trixuser:trixpass@db:5432/trixdb'
        ),
        'priority': 2,
        'depends_on': ['ghost_function', 'auth'],
        'health_check_timeout': 30,
        'retry_attempts': 3,
        'description': 'Gestão de usuários do sistema'
    },

    'tenants': {
        'module': 'core_services.tenants',
        'db_url': os.getenv(
            'TENANTS_DATABASE_URL',
            'postgresql+asyncpg://trixuser:trixpass@db:5432/trixdb'
        ),
        'priority': 3,
        'depends_on': ['ghost_function', 'auth', 'users'],
        'health_check_timeout': 30,
        'retry_attempts': 3,
        'description': 'Gestão de tenants e associações'
    },
    
    'i18n': {
        'module': 'core_services.i18n',
        'db_url': os.getenv(
            'I18N_DATABASE_URL',
            'postgresql+asyncpg://trixuser:trixpass@db:5432/trixdb'
        ),
        'priority': 4,
        'depends_on': [],
        'health_check_timeout': 30,
        'retry_attempts': 3,
        'description': 'Sistema de internacionalização'
    },
    
    'allergens': {
        'module': 'core_services.allergens',
        'db_url': os.getenv(
            'ALLERGENS_DATABASE_URL',
            'postgresql+asyncpg://trixuser:trixpass@db:5432/trixdb'
        ),
        'priority': 5,
        'depends_on': [],
        'health_check_timeout': 30,
        'retry_attempts': 3,
        'description': 'Alérgenos padrão da UE'
    },

    'core': {
        'module': 'core_services.core',
        'db_url': os.getenv(
            'CORE_DATABASE_URL',
            'postgresql+asyncpg://trixuser:trixpass@db:5432/trixdb'
        ),
        'priority': 6,
        'depends_on': ['auth', 'users', 'tenants'],
        'health_check_timeout': 30,
        'retry_attempts': 3,
        'description': 'Funcionalidades centrais (roles, permissions, categorias)'
    },
    
    # ========================================
    # INFRASTRUCTURE SERVICES (Priority 6-10)
    # ========================================
    'media': {
        'module': 'core_services.media',
        'db_url': os.getenv(
            'MEDIA_DATABASE_URL',
            'postgresql+asyncpg://media_user:MediaSecure2024!#$@trix-postgres-primary:5432/media_db'
        ),
        'priority': 6,
        'depends_on': ['auth', 'users', 'tenants'],
        'health_check_timeout': 30,
        'retry_attempts': 3,
        'description': 'Sistema de mídia/ftp e gerenciamento de dados e OCR'
    },
    
    'payments': {
        'module': 'infrastructure_services.payment_methods',
        'db_url': os.getenv(
            'PAYMENTS_DATABASE_URL',
            'postgresql+asyncpg://trixuser:trixpass@db:5432/trixdb'
        ),
        'priority': 7,
        'depends_on': ['tenants'],
        'health_check_timeout': 30,
        'retry_attempts': 3,
        'description': 'Processamento de pagamentos'
    },
    
    'products': {
        'module': 'infrastructure_services.products',
        'db_url': os.getenv(
            'PRODUCTS_DATABASE_URL',
            'postgresql+asyncpg://trixuser:trixpass@db:5432/trixdb'
        ),
        'priority': 8,
        'depends_on': ['core'],
        'health_check_timeout': 30,
        'retry_attempts': 3,
        'description': 'Catálogo de produtos'
    },
    
    'commerce': {
        'module': 'infrastructure_services.commerce',
        'db_url': os.getenv(
            'COMMERCE_DATABASE_URL',
            'postgresql+asyncpg://trixuser:trixpass@db:5432/trixdb'
        ),
        'priority': 10,
        'depends_on': ['products', 'payments', 'suppliers'],
        'health_check_timeout': 30,
        'retry_attempts': 3,
        'description': 'Sistema de e-commerce'
    },

    'suppliers': {
        'module': 'infrastructure_services.suppliers',
        'db_url': os.getenv(
            'SUPPLIERS_DATABASE_URL',
            'postgresql+asyncpg://trixuser:trixpass@db:5432/trixdb'
        ),
        'priority': 8,
        'depends_on': ['tenants', 'products'],
        'health_check_timeout': 30,
        'retry_attempts': 3,
        'description': 'Sistema de fornecedores e TVendors'
    },
    
    'domains': {
        'module': 'infrastructure_services.domains',
        'db_url': os.getenv(
            'DOMAINS_DATABASE_URL',
            'postgresql+asyncpg://trixuser:trixpass@db:5432/trixdb'
        ),
        'priority': 11,
        'depends_on': ['tenants'],
        'health_check_timeout': 30,
        'retry_attempts': 3,
        'description': 'Gestão de domínios'
    },
    
    # ========================================
    # SHARED SERVICES (Priority 12-16)
    # ========================================
    'notifications': {
        'module': 'shared_services.notifications',
        'db_url': os.getenv(
            'NOTIFICATIONS_DATABASE_URL',
            'postgresql+asyncpg://trixuser:trixpass@db:5432/trixdb'
        ),
        'priority': 12,
        'depends_on': ['users'],
        'health_check_timeout': 30,
        'retry_attempts': 3,
        'description': 'Sistema de notificações'
    },

    'email': {
        'module': 'shared_services.email',
        'db_url': os.getenv(
            'EMAIL_DATABASE_URL',
            'postgresql+asyncpg://trixuser:trixpass@db:5432/trixdb'
        ),
        'priority': 13,
        'depends_on': ['users', 'tenants'],
        'health_check_timeout': 30,
        'retry_attempts': 3,
        'description': 'Serviços de email'
    },

    'financial': {
        'module': 'shared_services.financial',
        'db_url': os.getenv(
            'FINANCIAL_DATABASE_URL',
            'postgresql+asyncpg://trixuser:trixpass@db:5432/trixdb'
        ),
        'priority': 14,
        'depends_on': ['tenants', 'payments', 'suppliers'],
        'health_check_timeout': 30,
        'retry_attempts': 3,
        'description': 'Sistema financeiro'
    },

    'hr': {
        'module': 'shared_services.hr',
        'db_url': os.getenv(
            'HR_DATABASE_URL',
            'postgresql+asyncpg://trixuser:trixpass@db:5432/trixdb'
        ),
        'priority': 15,
        'depends_on': ['tenants', 'users'],
        'health_check_timeout': 30,
        'retry_attempts': 3,
        'description': 'Recursos humanos'
    },

    'crm': {
        'module': 'shared_services.crm',
        'db_url': os.getenv(
            'CRM_DATABASE_URL',
            'postgresql+asyncpg://trixuser:trixpass@db:5432/trixdb'
        ),
        'priority': 16,
        'depends_on': ['tenants', 'users', 'suppliers'],
        'health_check_timeout': 30,
        'retry_attempts': 3,
        'description': 'Gestão de relacionamento'
    },
    
    # ========================================
    # TENANT-SPECIFIC SERVICES (Priority 17-19)
    # ========================================
    'restaurant': {
        'module': 'tenant_services.restaurant',
        'db_url': os.getenv(
            'RESTAURANT_DATABASE_URL',
            'postgresql+asyncpg://trixuser:trixpass@db:5432/trixdb'
        ),
        'priority': 17,
        'depends_on': ['tenants', 'financial', 'products', 'suppliers'],
        'health_check_timeout': 30,
        'retry_attempts': 3,
        'description': 'Módulo de restaurante'
    },

    'consultancy': {
        'module': 'tenant_services.consultancy',
        'db_url': os.getenv(
            'CONSULTANCY_DATABASE_URL',
            'postgresql+asyncpg://trixuser:trixpass@db:5432/trixdb'
        ),
        'priority': 18,
        'depends_on': ['tenants', 'financial'],
        'health_check_timeout': 30,
        'retry_attempts': 3,
        'description': 'Módulo de consultoria'
    },

    'shop': {
        'module': 'tenant_services.shop',
        'db_url': os.getenv(
            'SHOP_DATABASE_URL',
            'postgresql+asyncpg://trixuser:trixpass@db:5432/trixdb'
        ),
        'priority': 19,
        'depends_on': ['tenants', 'commerce', 'products', 'suppliers'],
        'health_check_timeout': 30,
        'retry_attempts': 3,
        'description': 'Módulo de loja'
    }
}


def get_microservices_by_priority() -> List[str]:
    """Retorna lista de microserviços ordenada por prioridade."""
    return sorted(
        MICROSERVICES_SEEDS.keys(),
        key=lambda ms: MICROSERVICES_SEEDS[ms]['priority']
    )


def get_microservices_by_category() -> Dict[str, List[str]]:
    """Retorna microserviços agrupados por categoria."""
    categories = {
        'core': [],
        'infrastructure': [],
        'shared': [],
        'tenant_specific': []
    }
    
    for ms_name, config in MICROSERVICES_SEEDS.items():
        priority = config['priority']
        if 1 <= priority <= 5:
            categories['core'].append(ms_name)
        elif 6 <= priority <= 10:
            categories['infrastructure'].append(ms_name)
        elif 11 <= priority <= 15:
            categories['shared'].append(ms_name)
        elif 17 <= priority <= 19:
            categories['tenant_specific'].append(ms_name)
    
    return categories


def resolve_dependencies(microservices: List[str]) -> List[str]:
    """Resolve dependências e retorna ordem correta de execução."""
    resolved = []
    remaining = microservices.copy()
    
    while remaining:
        progress = False
        for ms in remaining.copy():
            config = MICROSERVICES_SEEDS.get(ms, {})
            dependencies = config.get('depends_on', [])
            
            # Verificar se todas as dependências já foram resolvidas
            if all(dep in resolved for dep in dependencies):
                resolved.append(ms)
                remaining.remove(ms)
                progress = True
        
        if not progress:
            # Dependência circular ou microserviço não encontrado
            raise ValueError(f"Circular dependency or missing microservice in: {remaining}")
    
    return resolved


def get_microservice_config(microservice: str) -> Optional[Dict]:
    """Retorna configuração de um microserviço específico."""
    return MICROSERVICES_SEEDS.get(microservice)


def validate_microservice_config() -> bool:
    """Valida configuração de todos os microserviços."""
    for ms_name, config in MICROSERVICES_SEEDS.items():
        required_fields = ['module', 'db_url', 'priority', 'depends_on', 'description']
        
        for field in required_fields:
            if field not in config:
                raise ValueError(f"Missing required field '{field}' in microservice '{ms_name}'")
        
        # Validar dependências
        for dep in config['depends_on']:
            if dep not in MICROSERVICES_SEEDS:
                raise ValueError(f"Unknown dependency '{dep}' in microservice '{ms_name}'")
    
    return True


# Validar configuração na importação
validate_microservice_config()
