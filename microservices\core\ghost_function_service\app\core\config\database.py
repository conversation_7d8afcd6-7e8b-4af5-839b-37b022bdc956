"""
Ghost Function Service - Database Configuration

Configurações específicas de banco de dados para o Ghost Function Service.
Estende as configurações da shared_lib para funcionalidades específicas.

Author: Trix Platform Team
"""

from microservices.core.shared_lib.config import DatabaseSettings
from .settings import get_settings


class GhostDatabaseSettings(DatabaseSettings):
    """Ghost Function Service specific database settings."""
    
    def __init__(self):
        super().__init__()
        settings = get_settings()
        
        # Override with Ghost Function specific database URL if provided
        if hasattr(settings, 'DATABASE_URL') and settings.DATABASE_URL:
            self.DATABASE_URL = settings.DATABASE_URL
        else:
            # Default Ghost Function database URL
            self.DATABASE_URL = "postgresql+asyncpg://ghost_user:GhostSecure2024!#$@trix-postgres-primary:5432/ghost_db"
        
        # Ghost Function specific database settings
        self.DB_POOL_SIZE = 20  # Higher pool size for proxy operations
        self.DB_MAX_OVERFLOW = 30  # Higher overflow for peak loads
        self.DB_ECHO = False  # Disable echo in production for performance


# Global database settings instance
ghost_db_settings = GhostDatabaseSettings()


def get_ghost_database_settings() -> GhostDatabaseSettings:
    """Get the Ghost Function database settings instance."""
    return ghost_db_settings
