"""
Media System Observability Package
===================================

Observabilidade específica do Media System, integrando com shared_lib
para métricas, logging e tracing padronizados.

✅ MIGRAÇÃO CONCLUÍDA - Usando shared_lib para componentes comuns
"""

# Re-export shared observability components
from microservices.core.shared_lib.infrastructure.observability import (
    MetricsCollector,
    get_metrics_collector,
    TracingManager,
    StructuredLogger
)

# Import media-specific metrics
from .metrics import (
    media_uploads_counter,
    media_downloads_counter,
    processing_duration_histogram,
    storage_usage_gauge,
    cdn_hit_ratio_gauge,
    media_errors_counter,
    processing_queue_size_gauge,
    media_metrics_manager
)

# Import media-specific logging
from .logging import (
    media_logger,
    log_media_upload,
    log_media_processing,
    log_media_access,
    log_media_error
)

# Import media-specific tracing
from .tracing import (
    media_tracer,
    trace_media_operation,
    trace_processing_job,
    trace_storage_operation
)

__all__ = [
    # Shared components (re-exported from shared_lib)
    "MetricsCollector",
    "get_metrics_collector", 
    "TracingManager",
    "StructuredLogger",
    
    # Media-specific metrics
    "media_uploads_counter",
    "media_downloads_counter",
    "processing_duration_histogram",
    "storage_usage_gauge",
    "cdn_hit_ratio_gauge",
    "media_errors_counter",
    "processing_queue_size_gauge",
    "media_metrics_manager",
    
    # Media-specific logging
    "media_logger",
    "log_media_upload",
    "log_media_processing",
    "log_media_access",
    "log_media_error",
    
    # Media-specific tracing
    "media_tracer",
    "trace_media_operation",
    "trace_processing_job",
    "trace_storage_operation"
]
