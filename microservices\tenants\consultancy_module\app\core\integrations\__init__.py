"""
Consultancy Service Integrations Module
=======================================
Service-to-service integration clients and utilities.
"""

from .service_clients import (
    ServiceClient,
    AuthServiceClient,
    UserServiceClient,
    TenantServiceClient,
    FinancialServiceClient,
    HRServiceClient,
    get_auth_client,
    get_user_client,
    get_tenant_client,
    get_financial_client,
    get_hr_client
)

from .auth_client import (
    ConsultancyAuthIntegration,
    consultancy_auth,
    get_consultancy_auth
)

__all__ = [
    # Base service clients
    "ServiceClient",
    "AuthServiceClient",
    "UserServiceClient",
    "TenantServiceClient",
    "FinancialServiceClient",
    "HRServiceClient",
    
    # Service client getters
    "get_auth_client",
    "get_user_client",
    "get_tenant_client",
    "get_financial_client",
    "get_hr_client",
    
    # Consultancy-specific integrations
    "ConsultancyAuthIntegration",
    "consultancy_auth",
    "get_consultancy_auth",
]
