"""
Consultancy Service URLs Configuration
=====================================
Configuration for service-to-service communication URLs.
"""

from typing import Dict, Optional
from .settings import get_settings


class ServiceURLsConfig:
    """Configuration for external service URLs."""
    
    def __init__(self):
        self.settings = get_settings()
    
    @property
    def auth_service_url(self) -> str:
        """Auth Service URL."""
        return self.settings.AUTH_SERVICE_URL
    
    @property
    def user_service_url(self) -> str:
        """User Service URL."""
        return self.settings.USER_SERVICE_URL
    
    @property
    def tenant_service_url(self) -> str:
        """Tenant Service URL."""
        return self.settings.TENANT_SERVICE_URL
    
    @property
    def financial_service_url(self) -> str:
        """Financial Service URL."""
        return self.settings.FINANCIAL_SERVICE_URL
    
    @property
    def hr_service_url(self) -> str:
        """HR Service URL."""
        return self.settings.HR_SERVICE_URL
    
    def get_service_url(self, service_name: str) -> Optional[str]:
        """Get URL for a specific service."""
        service_urls = {
            "auth": self.auth_service_url,
            "user": self.user_service_url,
            "tenant": self.tenant_service_url,
            "financial": self.financial_service_url,
            "hr": self.hr_service_url
        }
        return service_urls.get(service_name)
    
    def get_all_service_urls(self) -> Dict[str, str]:
        """Get all configured service URLs."""
        return {
            "auth": self.auth_service_url,
            "user": self.user_service_url,
            "tenant": self.tenant_service_url,
            "financial": self.financial_service_url,
            "hr": self.hr_service_url
        }


# Global service URLs configuration instance
service_urls_config = ServiceURLsConfig()


def get_service_urls_config() -> ServiceURLsConfig:
    """Get service URLs configuration."""
    return service_urls_config
