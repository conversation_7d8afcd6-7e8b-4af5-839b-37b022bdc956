# 📧 Email Module - Documentação Técnica Completa

> **🔗 Integração com Shared Lib**: Este módulo foi **MIGRADO** para utilizar configurações e componentes centralizados da `microservices/core/shared_lib` para garantir consistência e reduzir duplicação entre microserviços. **MIGRAÇÃO CONCLUÍDA** - Todas as configurações comuns foram movidas para a shared_lib. Veja a [seção de integração](#-integração-com-shared-lib) para detalhes.

## 📋 Visão Geral

O **Email Module** é um microserviço compartilhado fundamental da plataforma Trix responsável pelo gerenciamento completo de emails, funcionando como um sistema de email corporativo integrado (similar ao Gmail). Implementa funcionalidades avançadas de webmail, gestão de domínios, templates, entrega de emails, analytics e integração automática com outros módulos do sistema, especialmente o **Financial Module** para processamento automático de faturas recebidas por email.

### 🎯 **Informações Básicas**
- **Porta:** 8012
- **Database:** postgres-email (PostgreSQL) - Porta 5445
- **Redis:** DB 12
- **Status:** ✅ **MIGRAÇÃO CONCLUÍDA - PRONTO PARA PRODUÇÃO**
- **Versão:** 2.0.0 (Integrado com Shared Lib)
- **Tipo:** Microserviço Compartilhado (Shared)
- **Shared Lib Integration:** ✅ **100% MIGRADO E TESTADO**
- **Docker Build:** ✅ **FUNCIONANDO** (Testado em 2025-01-24)

## 🏗️ **Arquitetura Atual**

## 🔗 **Integração com Shared Lib (MIGRAÇÃO 100% CONCLUÍDA)**

O Email Module agora utiliza completamente a `shared_lib` para:

> **🎉 MIGRAÇÃO CONCLUÍDA COM SUCESSO**: Todas as configurações comuns foram movidas para a shared_lib e o Email Module foi limpo e otimizado. Configurações específicas de email foram mantidas e organizadas.

### **📋 Configurações Compartilhadas (100% MIGRADAS)**
- **Email Provider Configuration**: ✅ Configuração centralizada de SendGrid, AWS SES, Mailgun, SMTP
- **SMTP/IMAP Configuration**: ✅ Configurações de servidor de email padronizadas
- **Email Security Settings**: ✅ DKIM, SPF, DMARC, DNS records centralizados
- **Email Storage Settings**: ✅ Configurações de armazenamento e quotas
- **Email Delivery Settings**: ✅ Configurações de entrega, retry, rate limiting
- **Email Template Settings**: ✅ Configurações de templates centralizadas

### **🛠️ Utilitários Compartilhados (100% MIGRADOS)**
- **Email Provider Clients**: ✅ Clientes SendGrid, SES, Mailgun padronizados
- **SMTP/IMAP Clients**: ✅ Clientes de email padronizados
- **Email Security Utils**: ✅ Funções de DKIM, SPF, DMARC
- **Email Template Utils**: ✅ Utilitários de template padronizados

### **🧹 Limpeza Realizada (CONCLUÍDA)**
- **Duplicate Email Configs**: ❌ Removidas (movidas para shared_lib)
- **Provider Configurations**: ❌ Removidas (centralizadas na shared_lib)
- **SMTP/IMAP Settings**: ❌ Removidas (padronizadas na shared_lib)
- **Template Configs**: ❌ Removidas (centralizadas na shared_lib)
- **Docker Build**: ✅ **TESTADO E FUNCIONANDO** (Build concluído com sucesso)

### **📦 Como Usar (MIGRAÇÃO CONCLUÍDA)**
```python
# Importar configurações compartilhadas
from microservices.core.shared_lib.config.email_config import (
    shared_email_settings,
    get_email_settings,
    get_smtp_config,
    get_imap_config,
    get_provider_config,
    EMAIL_PROVIDERS
)

# Importar clientes de messaging compartilhados
from microservices.core.shared_lib.infrastructure.messaging import (
    KafkaClient,
    RabbitMQClient,
    RedisClient
)

# Importar utilitários de observabilidade compartilhados
from microservices.core.shared_lib.infrastructure.observability import (
    MetricsCollector,
    get_metrics_collector
)

# Importar métricas específicas do email_service (re-exportadas da shared_lib)
from microservices.shared.email_module.app.core.observability import (
    email_requests_counter,
    integration_requests_counter,
    integration_duration_histogram,
    metrics_manager
)

# Exemplo de uso (FUNCIONANDO 100%)
smtp_config = get_smtp_config()
provider_config = get_provider_config("sendgrid")
email_settings = get_email_settings()

# Clientes de messaging
kafka_client = KafkaClient("email-service")
rabbitmq_client = RabbitMQClient("email-service")
redis_client = RedisClient("email-service")
metrics = get_metrics_collector("email-service", "2.0.0", "production")

# Usar métricas específicas do email
email_requests_counter.inc({"method": "send_email", "status": "success"})
integration_requests_counter.inc({"service": "financial-module", "status": "success"})

# Usar configurações específicas do email module
from microservices.shared.email_module.app.core.email_config import (
    email_settings as email_service_settings,
    get_combined_email_config
)
```

### 📁 **Estrutura de Diretórios (Atualizada)**
```
microservices/shared/email_module/
├── app/
│   ├── api/                             # APIs principais
│   │   ├── endpoints.py                 # Gestão de domínios, contas e aliases
│   │   ├── webmail.py                   # Interface webmail
│   │   └── schemas.py                   # Schemas Pydantic
│   ├── analytics/                       # Analytics de email
│   │   ├── models/                      # Modelos de analytics
│   │   ├── services/                    # Serviços de analytics
│   │   └── api/                         # APIs de analytics
│   ├── attachments/                     # Gestão de anexos
│   ├── core/                           # ✅ Configurações específicas (integra shared_lib)
│   │   ├── email_config.py             # ✅ MIGRADO: Usa shared_lib + específicas
│   │   └── database.py                 # Configurações de banco específicas
│   ├── delivery/                       # Sistema de entrega
│   │   ├── api/                        # APIs de entrega
│   │   │   └── delivery_api.py         # Endpoints de entrega
│   │   ├── models/                     # Modelos de entrega
│   │   │   └── email_delivery.py       # Modelo de entrega
│   │   ├── schemas/                    # Schemas de entrega
│   │   │   └── delivery_schemas.py     # Schemas Pydantic
│   │   ├── services/                   # Serviços de entrega
│   │   │   └── delivery_service.py     # Lógica de entrega
│   │   └── tasks.py                    # Tarefas assíncronas
│   ├── models/                         # Modelos principais
│   │   ├── email_account.py            # Contas de email
│   │   ├── email_alias.py              # Aliases de email
│   │   ├── email_domain.py             # Domínios de email
│   │   └── email_metadata.py           # Metadados de emails
│   ├── services/                       # Serviços principais
│   │   ├── auth_service.py             # Autenticação de email
│   │   ├── email_actions_service.py    # Ações de email
│   │   ├── provision_service.py        # Provisionamento
│   │   └── quota_service.py            # Gestão de quotas
│   ├── templates/                      # Sistema de templates
│   │   ├── api/                        # APIs de templates
│   │   │   └── templates_api.py        # Endpoints de templates
│   │   ├── models/                     # Modelos de templates
│   │   │   └── email_template.py       # Modelo de templates
│   │   ├── schemas/                    # Schemas de templates
│   │   │   └── template_schemas.py     # Schemas Pydantic
│   │   └── services/                   # Serviços de templates
│   │       └── template_service.py     # Lógica de templates
│   ├── utils/                          # Utilitários
│   ├── websocket/                      # WebSockets em tempo real
│   ├── celery_app.py                   # Configuração Celery
│   ├── config.py                       # Configurações
│   ├── exceptions.py                   # Exceções customizadas
│   └── main.py                         # Aplicação FastAPI
├── docker/
│   ├── Dockerfile                      # Container configuration
│   └── docker-compose.yml              # Service orchestration
├── migrations/
│   ├── env.py                          # Alembic environment
│   └── versions/                       # Migration files
├── k8s/                                # Kubernetes manifests
├── tests/                              # Test suites
├── requirements.txt                    # ✅ Email-specific dependencies
└── alembic.ini                        # Migration configuration

# Shared Library Integration (MIGRAÇÃO CONCLUÍDA)
../../../core/shared_lib/config/email_config.py    # 🔗 Configurações de email compartilhadas
├── EmailProviderSettings              # 🔗 MOVIDO: Configurações de provedores
├── SMTPSettings                       # 🔗 MOVIDO: Configurações SMTP
├── IMAPSettings                       # 🔗 MOVIDO: Configurações IMAP
├── EmailSecuritySettings              # 🔗 MOVIDO: DKIM, SPF, DMARC
├── EmailStorageSettings               # 🔗 MOVIDO: Armazenamento e quotas
├── EmailDeliverySettings              # 🔗 MOVIDO: Entrega e rate limiting
├── EmailTemplateSettings              # 🔗 MOVIDO: Configurações de templates
└── SharedEmailSettings                # 🔗 Configurações centralizadas

../../../core/shared_lib/infrastructure/           # 🔗 Componentes de infraestrutura
├── messaging/                         # 🔗 Clientes de messaging compartilhados
│   ├── kafka_client.py                # 🔗 Cliente Kafka para eventos de email
│   ├── rabbitmq_client.py             # 🔗 Cliente RabbitMQ para notificações rápidas
│   ├── redis_client.py                # 🔗 Cliente Redis para cache e sessões
│   └── __init__.py                    # 🔗 Exportações dos clientes
├── observability/                     # 🔗 Utilitários de monitoramento
│   ├── metrics.py                     # 🔗 Métricas Prometheus compartilhadas
│   ├── tracing.py                     # 🔗 Tracing distribuído compartilhado
│   ├── logging.py                     # 🔗 Logging estruturado compartilhado
│   └── __init__.py                    # 🔗 Exportações de observabilidade
├── security/                          # 🔗 Utilitários de segurança
│   ├── rate_limiter.py                # 🔗 Rate limiting para APIs de email
│   ├── session_manager.py             # 🔗 Gerenciamento de sessões webmail
│   ├── encryption.py                  # 🔗 Criptografia para anexos e dados
│   └── __init__.py                    # 🔗 Exportações de segurança
├── database/                          # 🔗 Utilitários de banco de dados
│   ├── connection.py                  # 🔗 Conexões de banco compartilhadas
│   ├── sharding.py                    # 🔗 Sharding para emails por tenant
│   └── __init__.py                    # 🔗 Exportações de banco
└── __init__.py                        # 🔗 Exportações da infraestrutura

../../../core/shared_lib/utils/                    # 🔗 Utilitários comuns
├── event_sourcing.py                  # 🔗 Event sourcing para auditoria de emails
├── common.py                          # 🔗 Utilitários comuns
└── __init__.py                        # 🔗 Exportações dos utilitários
```

### 🔧 **Componentes Principais**

#### **1. Sistema de Domínios de Email**
- **Gestão de Domínios:** Criação, configuração e verificação de domínios
- **DNS Management:** Configuração automática de registros MX, SPF, DKIM, DMARC
- **Verificação DNS:** Validação automática de configurações
- **DKIM Keys:** Geração e gestão de chaves DKIM

#### **2. Contas de Email**
- **Criação de Contas:** Provisionamento automático de contas de email
- **Autenticação:** Sistema de autenticação integrado
- **Quotas:** Gestão de limites de armazenamento
- **Passwords:** Gestão segura de senhas com hash

#### **3. Sistema Webmail**
- **Interface Completa:** Interface webmail similar ao Gmail
- **Mailboxes:** Gestão de caixas de entrada, enviados, lixeira
- **Operações:** Envio, recebimento, organização de emails
- **Anexos:** Suporte completo a anexos

#### **4. Templates de Email**
- **Templates Dinâmicos:** Sistema de templates personalizáveis
- **Variáveis:** Suporte a variáveis dinâmicas
- **Multi-tenant:** Templates específicos por tenant
- **Versionamento:** Controle de versões de templates

#### **5. Sistema de Entrega**
- **Múltiplos Provedores:** SendGrid, AWS SES, Mailgun, SMTP
- **Entrega em Massa:** Suporte a campanhas de email marketing
- **Retry Logic:** Sistema de retry para falhas
- **Analytics:** Tracking de entrega, abertura, cliques

## 🚀 **Funcionalidades Implementadas**

### ✅ **Recursos Atuais**

#### **Gestão de Domínios**
- Criação e configuração de domínios de email
- Verificação automática de DNS
- Geração de registros DNS (MX, SPF, DKIM, DMARC)
- Validação de configurações

#### **Contas de Email**
- Criação automática de contas
- Autenticação segura
- Gestão de quotas de armazenamento
- Alteração de senhas
- Vinculação com usuários do sistema

#### **Interface Webmail**
- Listagem de mailboxes
- Criação de mailboxes personalizados
- Envio e recebimento de emails
- Gestão de anexos
- Operações de email (mover, marcar, deletar)

#### **Templates de Email**
- Criação de templates personalizados
- Sistema de variáveis dinâmicas
- Templates por tenant
- Versionamento de templates

#### **Sistema de Entrega**
- Entrega via múltiplos provedores
- Campanhas de email marketing
- Sistema de filas com Celery
- Retry automático para falhas

#### **Analytics e Tracking**
- Tracking de entrega
- Métricas de abertura
- Análise de cliques
- Relatórios de performance

## 🔗 **Integrações com Outros Microserviços**

### **🔐 Auth Service**
- Autenticação de usuários para webmail
- Tokens JWT para acesso às APIs
- Validação de permissões de email

### **👥 User Service**
- Vinculação de contas de email com usuários
- Sincronização de dados de usuários
- Controle de acesso baseado em usuário

### **🏢 Tenant Service**
- Isolamento de dados por tenant
- Configurações específicas por empresa
- Domínios personalizados por tenant

### **💰 Financial Module** ⭐
- **Processamento Automático de Faturas:** Recebimento e análise automática de emails com faturas
- **Extração de Dados:** OCR e parsing de anexos PDF para extrair informações financeiras
- **Categorização Automática:** Classificação automática de despesas baseada no remetente
- **Integração de Compras:** Registro automático de compras no sistema financeiro
- **Notificações:** Alertas sobre faturas recebidas e processadas

### **📱 Media System**
- Armazenamento de anexos de email
- Processamento de imagens e documentos
- OCR para extração de texto de anexos
- Backup e arquivamento de anexos

### **📊 Notification Service**
- Notificações de novos emails
- Alertas de quota excedida
- Notificações de falhas de entrega
- Integração com WebSockets para tempo real

### **🛒 Commerce Service**
- Emails transacionais de pedidos
- Confirmações de compra
- Notificações de status de pedidos
- Emails de marketing de produtos

### **👥 CRM Service**
- Campanhas de email marketing
- Segmentação de clientes
- Automação de email marketing
- Tracking de engajamento

### **💳 Payment Service**
- Notificações de pagamentos
- Recibos por email
- Alertas de cobrança
- Confirmações de transações

## 📊 **Endpoints Principais**

### **📧 Email Domains**
```
GET    /api/v1/email/domains                    # Listar domínios
POST   /api/v1/email/domains                    # Criar domínio
GET    /api/v1/email/domains/{domain_id}        # Obter domínio
PUT    /api/v1/email/domains/{domain_id}        # Atualizar domínio
DELETE /api/v1/email/domains/{domain_id}        # Remover domínio
GET    /api/v1/email/domains/{domain_id}/dns-records    # Obter registros DNS
POST   /api/v1/email/domains/{domain_id}/verify-dns     # Verificar DNS
POST   /api/v1/email/domains/{domain_id}/generate-dkim  # Gerar DKIM
```

### **👤 Email Accounts**
```
GET    /api/v1/email/domains/{domain_id}/accounts       # Listar contas
POST   /api/v1/email/domains/{domain_id}/accounts       # Criar conta
GET    /api/v1/email/accounts/{account_id}              # Obter conta
PUT    /api/v1/email/accounts/{account_id}              # Atualizar conta
DELETE /api/v1/email/accounts/{account_id}              # Remover conta
GET    /api/v1/email/accounts/{account_id}/usage        # Obter uso
PUT    /api/v1/email/accounts/{account_id}/quota        # Atualizar quota
PUT    /api/v1/email/accounts/{account_id}/password     # Alterar senha
```

### **📮 Webmail**
```
GET    /api/v1/webmail/mailboxes                # Listar mailboxes
POST   /api/v1/webmail/mailboxes                # Criar mailbox
GET    /api/v1/webmail/emails                   # Listar emails
POST   /api/v1/webmail/emails/send              # Enviar email
GET    /api/v1/webmail/emails/{email_id}        # Obter email
PUT    /api/v1/webmail/emails/{email_id}/move   # Mover email
PUT    /api/v1/webmail/emails/{email_id}/flag   # Marcar email
DELETE /api/v1/webmail/emails/{email_id}        # Deletar email
```

### **📄 Templates**
```
GET    /api/v1/templates                        # Listar templates
POST   /api/v1/templates                        # Criar template
GET    /api/v1/templates/{template_id}          # Obter template
PUT    /api/v1/templates/{template_id}          # Atualizar template
DELETE /api/v1/templates/{template_id}          # Remover template
POST   /api/v1/templates/{template_id}/render   # Renderizar template
```

### **🚀 Delivery**
```
POST   /api/v1/delivery/send                    # Enviar email
POST   /api/v1/delivery/bulk                    # Envio em massa
GET    /api/v1/delivery/status/{delivery_id}    # Status de entrega
GET    /api/v1/delivery/analytics               # Analytics de entrega
POST   /api/v1/delivery/campaign                # Criar campanha
```

## 🗄️ **Modelos de Dados**

### **📧 EmailDomain**
```python
class EmailDomain(Base):
    id: UUID
    tenant_id: UUID                    # Vinculação com tenant
    domain_name: str                   # Nome do domínio (ex: empresa.com)
    is_verified: bool                  # Status de verificação DNS
    dkim_selector: str                 # Seletor DKIM
    dkim_private_key: str             # Chave privada DKIM
    dkim_public_key: str              # Chave pública DKIM
    spf_record: str                   # Registro SPF
    dmarc_record: str                 # Registro DMARC
    mx_records: List[str]             # Registros MX
    created_at: datetime
    updated_at: datetime
```

### **👤 EmailAccount**
```python
class EmailAccount(Base):
    id: UUID
    email_domain_id: UUID             # Referência ao domínio
    user_id: UUID                     # Vinculação com usuário (opcional)
    username: str                     # Parte antes do @
    full_email: str                   # Email completo (username@domain)
    password_hash: str                # Hash da senha
    quota_mb: int                     # Quota em MB (padrão: 1024)
    is_active: bool                   # Status da conta
    last_login: datetime              # Último login
    created_at: datetime
    updated_at: datetime
```

### **📮 EmailMetadata**
```python
class EmailMetadata(Base):
    id: UUID
    email_account_id: UUID            # Conta proprietária
    message_id: str                   # ID único da mensagem
    subject: str                      # Assunto
    sender: str                       # Remetente
    recipients: List[str]             # Destinatários
    cc: List[str]                     # Cópia
    bcc: List[str]                    # Cópia oculta
    mailbox: str                      # Mailbox (INBOX, SENT, etc.)
    is_read: bool                     # Status de leitura
    is_flagged: bool                  # Marcado como importante
    size_bytes: int                   # Tamanho em bytes
    has_attachments: bool             # Possui anexos
    received_at: datetime             # Data de recebimento
    created_at: datetime
```

### **📄 EmailTemplate**
```python
class EmailTemplate(Base):
    id: UUID
    tenant_id: UUID                   # Vinculação com tenant
    name: str                         # Nome do template
    subject: str                      # Assunto do template
    html_content: str                 # Conteúdo HTML
    text_content: str                 # Conteúdo texto
    variables: Dict[str, Any]         # Variáveis disponíveis
    category: str                     # Categoria (transacional, marketing)
    is_active: bool                   # Status ativo
    version: int                      # Versão do template
    created_by: UUID                  # Criado por usuário
    created_at: datetime
    updated_at: datetime
```

### **🚀 EmailDelivery**
```python
class EmailDelivery(Base):
    id: UUID
    tenant_id: UUID                   # Vinculação com tenant
    template_id: UUID                 # Template utilizado (opcional)
    sender_email: str                 # Email remetente
    recipient_email: str              # Email destinatário
    subject: str                      # Assunto
    content_html: str                 # Conteúdo HTML
    content_text: str                 # Conteúdo texto
    provider: str                     # Provedor usado (SendGrid, SES, etc.)
    status: str                       # Status (pending, sent, failed, delivered)
    external_id: str                  # ID do provedor externo
    sent_at: datetime                 # Data de envio
    delivered_at: datetime            # Data de entrega
    opened_at: datetime               # Data de abertura
    clicked_at: datetime              # Data de clique
    error_message: str                # Mensagem de erro (se houver)
    retry_count: int                  # Número de tentativas
    created_at: datetime
    updated_at: datetime
```

## ⚙️ **Configurações (MIGRADAS PARA SHARED_LIB)**

### **🔧 Configurações Principais (AGORA NA SHARED_LIB)**

> **✅ MIGRAÇÃO CONCLUÍDA**: Todas as configurações foram movidas para `microservices/core/shared_lib/config/email_config.py` para reutilização entre microserviços.

```python
# ✅ NOVO: Usando configurações da shared_lib
from microservices.core.shared_lib.config.email_config import (
    shared_email_settings,
    get_smtp_config,
    get_imap_config,
    get_provider_config,
    get_dns_records
)

# Exemplo de uso das configurações compartilhadas
smtp_config = get_smtp_config()
# Retorna: {
#     "host": "localhost",
#     "port": 587,
#     "use_tls": True,
#     "username": None,
#     "password": None,
#     "timeout": 30,
#     "max_connections": 10
# }

imap_config = get_imap_config()
# Retorna: {
#     "host": "localhost",
#     "port": 143,
#     "use_ssl": False,
#     "use_starttls": True,
#     "timeout": 30,
#     "max_connections": 5
# }

# Configurações de provedores
sendgrid_config = get_provider_config("sendgrid")
ses_config = get_provider_config("ses")
mailgun_config = get_provider_config("mailgun")

# DNS records para um domínio
dns_records = get_dns_records("example.com")
# Retorna: {
#     "mx_records": ["10 mail.example.com", "20 mail2.example.com"],
#     "spf_record": "v=spf1 mx a:example.com ~all",
#     "dmarc_record": "v=DMARC1; p=none; sp=none; rua=mailto:<EMAIL>"
# }

# Configurações específicas do Email Service (não compartilhadas)
from microservices.shared.email_module.app.core.email_config import email_settings

email_service_config = {
    "webmail_enabled": email_settings.webmail_enabled,
    "financial_integration_enabled": email_settings.financial_integration_enabled,
    "auto_invoice_processing": email_settings.auto_invoice_processing,
    "email_archiving_enabled": email_settings.email_archiving_enabled,
}
```

### **🎯 Reorganização para Shared Library**

#### **🔧 Configurações Movidas para Shared Library**
As seguintes configurações comuns foram reorganizadas para `microservices/core/shared_lib/`:

**✅ Configurações Comuns (`shared_lib/config/email_config.py`)**:
- **`EmailProviderSettings`**: Configurações de SendGrid, AWS SES, Mailgun, SMTP
- **`SMTPSettings`**: Configuração SMTP comum (host, port, TLS, timeout)
- **`IMAPSettings`**: Configuração IMAP comum (host, port, SSL, timeout)
- **`EmailSecuritySettings`**: DKIM, SPF, DMARC, DNS records (padrões comuns)
- **`EmailStorageSettings`**: Armazenamento, quotas, limites de arquivo
- **`EmailDeliverySettings`**: Rate limiting, retry, batch processing
- **`EmailTemplateSettings`**: Configurações de templates (cache, limites)

**✅ Infraestrutura Comum (`shared_lib/infrastructure/`)**:
- **`messaging/`**: Clientes Kafka, RabbitMQ, Redis (event sourcing)
- **`observability/`**: Métricas Prometheus, tracing Jaeger, logs ELK
- **`security/`**: Rate limiting, encryption, session management
- **`database/`**: Connection pooling, sharding, migrations

**✅ Utilitários Comuns (`shared_lib/utils/`)**:
- **`event_sourcing.py`**: Event sourcing para auditoria de emails
- **`common.py`**: Utilitários comuns de validação e formatação

#### **🎯 Benefícios da Reorganização**

**✅ Consistência Entre Microserviços**:
- Configurações padronizadas em todos os serviços de email
- Mesmos padrões de segurança, observability e messaging
- Redução de duplicação de código entre email_module, notification_service, crm_module

**✅ Manutenibilidade**:
- Atualizações centralizadas na shared_lib
- Versionamento controlado de componentes comuns
- Facilita upgrades e patches de segurança

**✅ Reutilização**:
- Novos microserviços podem importar componentes prontos
- Padrões enterprise já implementados e testados
- Reduz tempo de desenvolvimento de novos serviços

#### **🔗 Como Usar a Shared Library**
```python
# Imports da shared library no email_module
from microservices.core.shared_lib.config.email_config import (
    shared_email_settings, get_email_settings, get_smtp_config, get_provider_config
)
from microservices.core.shared_lib.infrastructure.messaging import (
    KafkaClient, RabbitMQClient, RedisClient
)
from microservices.core.shared_lib.infrastructure.observability import (
    MetricsCollector, get_metrics_collector
)
from microservices.core.shared_lib.infrastructure.security import (
    RateLimiter, EncryptionUtils, SessionManager
)
```

#### **✅ Submódulos Específicos Preservados**
- **`webmail/`**: Interface webmail específica do Email Module
- **`financial_integration/`**: Integração com Financial Module específica
- **`analytics/`**: Analytics de email específicos do domínio
- **Vantagem**: Cada submódulo mantém sua estrutura própria (api/, models/, schemas/, services/)

### **📋 Configurações Disponíveis na Shared Lib**

#### **🔌 Email Providers (shared_email_settings.providers)**
- **SendGrid**: API key, from email, from name
- **AWS SES**: Access key, secret key, region, from email
- **Mailgun**: API key, domain, from email
- **SMTP**: Host, port, TLS/SSL, credentials

#### **📧 SMTP/IMAP (shared_email_settings.smtp/imap)**
- **SMTP**: Host, port, TLS/SSL, timeout, max connections
- **IMAP**: Host, port, SSL/STARTTLS, timeout, max connections

#### **🔒 Security (shared_email_settings.security)**
- **DKIM**: Selector, private key path
- **DNS**: MX records, SPF record, DMARC record templates
- **Server**: Email server hostname

#### **💾 Storage (shared_email_settings.storage)**
- **Paths**: Maildir, upload, template, attachment directories
- **Limits**: File sizes, quotas (default 1GB, max 10GB)

#### **🚀 Delivery (shared_email_settings.delivery)**
- **Limits**: Max recipients (100), batch size (50)
- **Retry**: Attempts (3), delay (5 min)
- **Rate Limiting**: Per hour (1000), per day (10000)
- **Analytics**: Track opens/clicks, retention (365 days)

#### **📄 Templates (shared_email_settings.templates)**
- **Language**: Default language (en)
- **Cache**: TTL (1 hour), preview enabled
- **Limits**: Max size (1MB), variables (100)

### **🏗️ Componentes de Infraestrutura da Shared Lib**

#### **📨 Messaging Infrastructure (shared_lib/infrastructure/messaging/)**
```python
# Clientes de messaging compartilhados para escalabilidade
from microservices.core.shared_lib.infrastructure.messaging import (
    KafkaClient,      # Event sourcing para auditoria de emails
    RabbitMQClient,   # Notificações rápidas e filas de email
    RedisClient       # Cache de sessões webmail e rate limiting
)

# Exemplo de uso no Email Module
kafka_client = KafkaClient("email-service")
await kafka_client.publish("email.sent", {
    "email_id": "123",
    "recipient": "<EMAIL>",
    "status": "delivered"
})

rabbitmq_client = RabbitMQClient("email-service")
await rabbitmq_client.publish_notification("email.bounce", bounce_data)

redis_client = RedisClient("email-service")
await redis_client.set_cache("webmail_session:123", session_data, ttl=3600)
```

#### **📊 Observability Infrastructure (shared_lib/infrastructure/observability/)**
```python
# Métricas, tracing e logging compartilhados
from microservices.core.shared_lib.infrastructure.observability import (
    MetricsCollector,     # Métricas Prometheus
    TracingManager,       # Tracing distribuído Jaeger
    StructuredLogger      # Logging estruturado ELK
)

# Métricas específicas do Email Module
metrics = MetricsCollector("email-service", "2.0.0")
metrics.counter("emails_sent_total").inc({"provider": "sendgrid", "status": "success"})
metrics.histogram("email_delivery_duration").observe(1.23, {"provider": "sendgrid"})
metrics.gauge("active_webmail_sessions").set(150)

# Tracing para operações de email
tracer = TracingManager("email-service")
with tracer.start_span("send_email") as span:
    span.set_tag("recipient", "<EMAIL>")
    span.set_tag("provider", "sendgrid")
    # ... operação de envio
```

#### **🔒 Security Infrastructure (shared_lib/infrastructure/security/)**
```python
# Componentes de segurança compartilhados
from microservices.core.shared_lib.infrastructure.security import (
    RateLimiter,          # Rate limiting para APIs
    EncryptionUtils,      # Criptografia para anexos
    SessionManager,       # Gerenciamento de sessões webmail
    SecurityValidator     # Validação de segurança
)

# Rate limiting para APIs de email
rate_limiter = RateLimiter("email-api")
if not await rate_limiter.check_limit(user_id, "send_email", limit=100, window=3600):
    raise HTTPException(429, "Rate limit exceeded")

# Criptografia para anexos sensíveis
encryption = EncryptionUtils()
encrypted_attachment = await encryption.encrypt_file(attachment_data)

# Sessões webmail seguras
session_manager = SessionManager("webmail")
session = await session_manager.create_session(user_id, {"ip": client_ip})
```

#### **🗄️ Database Infrastructure (shared_lib/infrastructure/database/)**
```python
# Utilitários de banco compartilhados
from microservices.core.shared_lib.infrastructure.database import (
    ConnectionManager,    # Connection pooling
    ShardingManager,      # Sharding por tenant
    MigrationManager      # Migrações automáticas
)

# Connection pooling para alta performance
db_manager = ConnectionManager("email-service")
async with db_manager.get_connection() as conn:
    # ... operações de banco

# Sharding por tenant para escalabilidade
sharding = ShardingManager("email-service")
shard_key = sharding.get_shard_key(tenant_id)
db_conn = await sharding.get_connection(shard_key)
```

#### **⚡ Event Sourcing Infrastructure (shared_lib/utils/event_sourcing.py)**
```python
# Event sourcing para auditoria completa de emails
from microservices.core.shared_lib.utils.event_sourcing import (
    EventStore,           # Armazenamento de eventos
    EventPublisher,       # Publicação de eventos
    EventSubscriber       # Subscrição de eventos
)

# Registrar eventos de email para auditoria
event_store = EventStore("email-service")
await event_store.append_event("email.sent", {
    "aggregate_id": email_id,
    "user_id": user_id,
    "tenant_id": tenant_id,
    "recipient": recipient_email,
    "provider": "sendgrid",
    "timestamp": datetime.utcnow(),
    "metadata": {"ip": client_ip, "user_agent": user_agent}
})

# Publicar eventos para outros microserviços
event_publisher = EventPublisher("email-service")
await event_publisher.publish("email.delivered", {
    "email_id": email_id,
    "delivery_time": delivery_time,
    "provider_response": provider_response
})
```

### **� Arquivos da Shared Lib que Precisam Ser Criados**

#### **✅ Já Criados (MIGRAÇÃO CONCLUÍDA)**
- **`shared_lib/config/email_config.py`**: ✅ Configurações de email centralizadas
- **`shared_lib/config/examples/email_usage_example.py`**: ✅ Exemplos de uso

#### **🔄 Precisam Ser Criados (PRÓXIMOS PASSOS)**

**📨 Messaging Infrastructure**:
```
shared_lib/infrastructure/messaging/
├── __init__.py                        # 🔄 Exportações dos clientes
├── kafka_client.py                    # 🔄 Cliente Kafka para eventos
├── rabbitmq_client.py                 # 🔄 Cliente RabbitMQ para notificações
├── redis_client.py                    # 🔄 Cliente Redis para cache
└── base_client.py                     # 🔄 Cliente base comum
```

**📊 Observability Infrastructure**:
```
shared_lib/infrastructure/observability/
├── __init__.py                        # 🔄 Exportações de observabilidade
├── metrics.py                         # 🔄 Métricas Prometheus
├── tracing.py                         # 🔄 Tracing Jaeger
├── logging.py                         # 🔄 Logging estruturado
└── health_check.py                    # 🔄 Health checks padronizados
```

**🔒 Security Infrastructure**:
```
shared_lib/infrastructure/security/
├── __init__.py                        # 🔄 Exportações de segurança
├── rate_limiter.py                    # 🔄 Rate limiting
├── encryption.py                      # 🔄 Criptografia
├── session_manager.py                 # 🔄 Gerenciamento de sessões
└── validator.py                       # 🔄 Validações de segurança
```

**🗄️ Database Infrastructure**:
```
shared_lib/infrastructure/database/
├── __init__.py                        # 🔄 Exportações de banco
├── connection.py                      # 🔄 Connection pooling
├── sharding.py                        # 🔄 Sharding por tenant
├── migration.py                       # 🔄 Migrações automáticas
└── query_builder.py                   # 🔄 Query builder comum
```

**⚡ Event Sourcing**:
```
shared_lib/utils/
├── event_sourcing.py                  # 🔄 Event sourcing
├── common.py                          # 🔄 Utilitários comuns
└── validators.py                      # 🔄 Validadores comuns
```

#### **🎯 Prioridade de Criação**

**🔥 Alta Prioridade (Essencial para Email Module)**:
1. **`messaging/redis_client.py`**: Cache de sessões webmail
2. **`observability/metrics.py`**: Métricas de email
3. **`security/rate_limiter.py`**: Rate limiting para APIs
4. **`database/connection.py`**: Connection pooling

**⚡ Média Prioridade (Melhoria de Performance)**:
1. **`messaging/kafka_client.py`**: Event sourcing
2. **`observability/tracing.py`**: Tracing distribuído
3. **`security/encryption.py`**: Criptografia de anexos
4. **`database/sharding.py`**: Sharding por tenant

**📈 Baixa Prioridade (Funcionalidades Avançadas)**:
1. **`messaging/rabbitmq_client.py`**: Notificações avançadas
2. **`observability/logging.py`**: Logging estruturado
3. **`security/session_manager.py`**: Sessões avançadas
4. **`utils/event_sourcing.py`**: Auditoria completa

### **🚀 Benefícios da Arquitetura Shared Lib**

#### **✅ Escalabilidade**
- **Connection Pooling**: Reutilização de conexões entre microserviços
- **Sharding**: Distribuição de dados por tenant
- **Rate Limiting**: Proteção contra sobrecarga
- **Caching**: Redis compartilhado para performance

#### **✅ Observabilidade**
- **Métricas Padronizadas**: Mesmas métricas em todos os serviços
- **Tracing Distribuído**: Rastreamento de requests entre serviços
- **Logging Estruturado**: Logs padronizados para análise
- **Health Checks**: Monitoramento de saúde unificado

#### **✅ Segurança**
- **Rate Limiting Centralizado**: Proteção contra ataques
- **Criptografia Padronizada**: Mesmos algoritmos em todos os serviços
- **Session Management**: Gerenciamento seguro de sessões
- **Validação Centralizada**: Validações de segurança reutilizáveis

#### **✅ Manutenibilidade**
- **Código Reutilizável**: Menos duplicação entre serviços
- **Atualizações Centralizadas**: Mudanças na shared_lib afetam todos
- **Testes Centralizados**: Testes dos componentes comuns
- **Documentação Unificada**: Documentação centralizada dos padrões

### **�📧 Provedores de Email Suportados**
- **SendGrid:** API para envio em massa
- **AWS SES:** Integração com Amazon SES
- **Mailgun:** API Mailgun para entrega
- **SMTP:** Servidor SMTP personalizado

## 🔒 **Segurança**

### **🛡️ Autenticação e Autorização**
- **JWT Tokens:** Integração com Auth Service
- **Role-Based Access:** Controle baseado em roles (Owner, Manager, Employee)
- **Tenant Isolation:** Isolamento completo de dados por tenant
- **Password Security:** Hash seguro de senhas com bcrypt/Argon2

### **🔐 Segurança de Email**
- **DKIM Signing:** Assinatura digital de emails
- **SPF Records:** Validação de remetentes autorizados
- **DMARC Policy:** Política de autenticação de domínio
- **TLS Encryption:** Criptografia em trânsito

### **📊 Auditoria**
- **Logs de Acesso:** Registro de todos os acessos
- **Tracking de Ações:** Auditoria de operações de email
- **Compliance:** Conformidade com GDPR e outras regulamentações

## 📈 **Monitoramento e Observabilidade**

### **📊 Métricas Principais**
- **Email Delivery Rate:** Taxa de entrega de emails
- **Open Rate:** Taxa de abertura de emails
- **Click Rate:** Taxa de cliques em emails
- **Bounce Rate:** Taxa de rejeição de emails
- **Storage Usage:** Uso de armazenamento por conta
- **API Response Time:** Tempo de resposta das APIs

### **🚨 Alertas**
- **Quota Exceeded:** Alerta de quota excedida
- **Delivery Failures:** Falhas de entrega
- **DNS Issues:** Problemas de configuração DNS
- **High Bounce Rate:** Taxa alta de rejeição

### **📋 Health Checks**
- **Database Connectivity:** Verificação de conexão com banco
- **Email Provider Status:** Status dos provedores de email
- **Storage Availability:** Disponibilidade de armazenamento
- **Service Dependencies:** Status de dependências

## 🔄 **Integração Especial com Financial Module**

### **💰 Processamento Automático de Faturas**

#### **📧 Recebimento de Emails com Faturas**
- **Monitoramento Automático:** Verificação contínua de emails recebidos
- **Filtros Inteligentes:** Identificação automática de emails com faturas
- **Palavras-chave:** Detecção baseada em assunto e conteúdo
- **Remetentes Conhecidos:** Lista de fornecedores cadastrados

#### **📄 Processamento de Anexos**
- **OCR Integration:** Extração de texto de PDFs e imagens
- **Data Extraction:** Identificação de valores, datas, fornecedores
- **Validation:** Validação de dados extraídos
- **Error Handling:** Tratamento de erros de processamento

#### **💳 Integração Financeira**
- **Automatic Entry Creation:** Criação automática de lançamentos financeiros
- **Category Assignment:** Atribuição automática de categorias
- **Supplier Matching:** Vinculação com fornecedores cadastrados
- **Approval Workflow:** Fluxo de aprovação para lançamentos

#### **🔔 Notificações**
- **Processing Alerts:** Alertas de processamento de faturas
- **Error Notifications:** Notificações de erros
- **Approval Requests:** Solicitações de aprovação
- **Summary Reports:** Relatórios de resumo

## 🚀 **Próximos Passos e Roadmap**

### **🔄 Melhorias Planejadas**
- **AI-Powered Analytics:** Analytics avançados com IA
- **Advanced Templates:** Templates com lógica condicional
- **Email Automation:** Automação avançada de email marketing
- **Mobile Push Integration:** Integração com notificações push

### **📈 Escalabilidade**
- **Horizontal Scaling:** Escalonamento horizontal para alta demanda
- **Caching Layer:** Camada de cache para performance
- **CDN Integration:** Integração com CDN para anexos
- **Load Balancing:** Balanceamento de carga avançado

### **🔒 Segurança Avançada**
- **Zero Trust Architecture:** Implementação de arquitetura zero trust
- **Advanced Threat Protection:** Proteção avançada contra ameaças
- **Email Encryption:** Criptografia end-to-end de emails
- **Compliance Automation:** Automação de conformidade

---

## 📞 **Suporte e Contato**

Para questões técnicas relacionadas ao Email Module:
- **Documentação:** `/docs` endpoint para documentação interativa
- **Health Check:** `/health` endpoint para verificação de status
- **Logs:** Consulte os logs do container para troubleshooting
- **Monitoramento:** Dashboard Grafana para métricas em tempo real

---

*Documentação atualizada em: 2025-01-13*
*Versão do Email Module: 1.0.0*
