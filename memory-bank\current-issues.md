# Current Issues and Solutions

## 🔧 Issues Being Tracked

### 1. Pydantic v2 Configuration Issues (CRITICAL - NEEDS INVESTIGATION)
**Status**: 🚨 CRITICAL - CONTAINERS FAILING
**Priority**: CRITICAL
**Affected Services**: core_service (beat, worker containers)

**Problem**: 
- Containers beat and worker failing to start due to Pydantic v2 configuration errors
- Error: "Extra inputs are not permitted [type=extra_forbidden]" for environment variables:
  - LETSENCRYPT_EMAIL
  - CLOUDFLARE_API_TOKEN  
  - FCM_SERVER_KEY
  - QR_CODE_BASE_URL

**Root Cause Analysis**:
- Pydantic v2 requires `SettingsConfigDict` instead of `ConfigDict` for BaseSettings
- Environment variables not defined in the model are being rejected despite `extra="ignore"`
- VaultBaseSettings inheritance chain may be overriding configuration
- Custom FilteredEnvSettingsSource not working as expected

**Actions Taken**:
1. ✅ Updated imports to use `SettingsConfigDict` from `pydantic_settings`
2. ✅ Fixed inheritance from `StandardVaultConfig` to `VaultBaseSettings` 
3. ✅ Added custom `FilteredEnvSettingsSource` to ignore problematic variables
4. ✅ Rebuilt containers with --no-cache multiple times
5. ❌ Containers still restarting with same error

**Current Status**:
- ✅ PARTIAL SUCCESS: Containers can start but restart due to additional variables
- ✅ SOLUTION FOUND: Need to define all environment variables as optional fields
- 🔄 Additional variables discovered: RESELLERCLUB_API_KEY

**Solution Applied**:
1. ✅ Changed inheritance from VaultBaseSettings to BaseSettings (isolated issue)
2. ✅ Removed case_sensitive=True from SettingsConfigDict
3. ✅ Added problematic variables as optional fields with env mapping
4. ✅ Containers now start successfully but restart due to additional undefined variables

**Remaining Work**:
1. 🔄 Add RESELLERCLUB_API_KEY as optional field
2. 🔄 Scan for any other undefined environment variables
3. 🔄 Restore VaultBaseSettings inheritance once all variables are defined
4. 🔄 Test complete functionality

**Research Done**:
- ✅ Used websearch to understand Pydantic v2 settings configuration
- ✅ Used Context7 to get latest pydantic-settings documentation
- ✅ Confirmed `extra="ignore"` should work with `SettingsConfigDict`
- ✅ Verified inheritance from VaultBaseSettings is correct

**Technical Details**:
- File: `microservices/core/core_service/app/core/config/settings.py`
- Class: `Settings(VaultBaseSettings)`
- Configuration: `SettingsConfigDict(case_sensitive=True, extra="ignore")`
- Custom source: `FilteredEnvSettingsSource` to filter problematic variables

**Impact**:
- Core service background tasks (Celery beat/worker) not functioning
- System unable to process scheduled tasks and background jobs
- Affects overall system functionality

## 🔄 Resolution Strategy

### Phase 1: Immediate Isolation
1. Create minimal Settings class without VaultBaseSettings inheritance
2. Test if basic BaseSettings with extra="ignore" works
3. Gradually add back functionality to identify the conflict

### Phase 2: Root Cause Investigation  
1. Examine VaultBaseSettings model_config inheritance
2. Check for multiple model_config definitions in inheritance chain
3. Verify environment variable loading order

### Phase 3: Permanent Solution
1. Fix configuration inheritance properly
2. Ensure auth_service and other services remain unaffected
3. Update documentation with proper Pydantic v2 patterns

## 📝 Notes
- Auth service documentation shows proper configuration patterns
- Issue may be specific to core_service configuration
- Need to maintain compatibility with existing vault integration
