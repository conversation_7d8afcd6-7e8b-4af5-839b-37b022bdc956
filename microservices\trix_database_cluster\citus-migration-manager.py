#!/usr/bin/env python3
"""
🔄 Trix Citus Migration Manager
Gerencia migrations distribuídas no cluster Citus por microserviço
"""

import asyncio
import asyncpg
import logging
import json
import os
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from pathlib import Path
import importlib.util

# Configuração de logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class MigrationTask:
    """Tarefa de migration para um microserviço"""
    microservice: str
    coordinator: str
    database: str
    tables: List[str]
    distribution_key: str
    shard_count: int
    replication_factor: int
    migration_file: Optional[str] = None

class CitusMigrationManager:
    """Gerenciador de migrations distribuídas para Citus"""
    
    def __init__(self):
        self.coordinators_config = self._load_coordinators_config()
        self.microservices_mapping = self._load_microservices_mapping()
        self.migration_tasks = self._prepare_migration_tasks()
        
    def _load_coordinators_config(self) -> Dict[str, Dict[str, Any]]:
        """Carrega configuração dos coordenadores"""
        return {
            'core': {
                'host': 'trix-citus-core-coordinator',
                'port': 5432,
                'database': 'postgres'
            },
            'tenant': {
                'host': 'trix-citus-tenant-coordinator',
                'port': 5433,
                'database': 'postgres'
            },
            'shared': {
                'host': 'trix-citus-shared-coordinator',
                'port': 5434,
                'database': 'postgres'
            }
        }
    
    def _load_microservices_mapping(self) -> Dict[str, Dict[str, Any]]:
        """Carrega mapeamento de microserviços"""
        return {
            # Core Services
            'auth_service': {
                'coordinator': 'core',
                'database': 'auth_db',
                'distribution_key': 'user_id',
                'tables': ['users', 'auth_sessions', 'auth_tokens', 'roles', 'permissions', 'mfa_settings'],
                'shard_count': 32,
                'replication_factor': 2
            },
            'user_service': {
                'coordinator': 'core',
                'database': 'users_db',
                'distribution_key': 'user_id',
                'tables': ['user_profiles', 'user_preferences', 'user_addresses'],
                'shard_count': 32,
                'replication_factor': 2
            },
            'tenant_service': {
                'coordinator': 'core',
                'database': 'tenants_db',
                'distribution_key': 'tenant_id',
                'tables': ['tenants', 'tenant_settings', 'tenant_user_associations'],
                'shard_count': 16,
                'replication_factor': 2
            },
            
            # Tenant Services
            'restaurant_module': {
                'coordinator': 'tenant',
                'database': 'restaurant_db',
                'distribution_key': 'tenant_id',
                'tables': ['restaurant_menus', 'restaurant_orders', 'restaurant_tables', 'restaurant_inventories', 'menu_categories'],
                'shard_count': 64,
                'replication_factor': 2
            },
            'consultancy_module': {
                'coordinator': 'tenant',
                'database': 'consultancy_db',
                'distribution_key': 'tenant_id',
                'tables': ['consultancy_cases', 'consultancy_billings', 'consultancy_workflows'],
                'shard_count': 32,
                'replication_factor': 2
            },
            
            # Shared Services
            'hr_module': {
                'coordinator': 'shared',
                'database': 'hr_db',
                'distribution_key': 'tenant_id',
                'tables': ['hr_employees', 'hr_departments', 'hr_lms_courses', 'hr_performances'],
                'shard_count': 32,
                'replication_factor': 2
            },
            'crm_module': {
                'coordinator': 'shared',
                'database': 'crm_db',
                'distribution_key': 'tenant_id',
                'tables': ['crm_accounts', 'crm_contacts', 'crm_interactions'],
                'shard_count': 32,
                'replication_factor': 2
            },
            'email_module': {
                'coordinator': 'shared',
                'database': 'email_db',
                'distribution_key': 'tenant_id',
                'tables': ['email_accounts', 'email_domains', 'email_aliases'],
                'shard_count': 16,
                'replication_factor': 2
            }
        }
    
    def _prepare_migration_tasks(self) -> List[MigrationTask]:
        """Prepara tarefas de migration por microserviço"""
        tasks = []
        
        for service_name, config in self.microservices_mapping.items():
            task = MigrationTask(
                microservice=service_name,
                coordinator=config['coordinator'],
                database=config['database'],
                tables=config['tables'],
                distribution_key=config['distribution_key'],
                shard_count=config['shard_count'],
                replication_factor=config['replication_factor']
            )
            tasks.append(task)
        
        return tasks
    
    async def run_distributed_migrations(self):
        """Executa migrations distribuídas por coordenador"""
        logger.info("🔄 Iniciando migrations distribuídas no cluster Citus...")
        
        try:
            # 1. Verificar conectividade
            await self._check_coordinators_connectivity()
            
            # 2. Executar migrations por coordenador
            await self._execute_migrations_by_coordinator()
            
            # 3. Configurar distribuição de tabelas
            await self._setup_distributed_tables()
            
            # 4. Verificar integridade
            await self._verify_migrations()
            
            logger.info("✅ Migrations distribuídas executadas com sucesso!")
            
        except Exception as e:
            logger.error(f"❌ Erro nas migrations distribuídas: {e}")
            raise
    
    async def _check_coordinators_connectivity(self):
        """Verifica conectividade com coordenadores"""
        logger.info("🔍 Verificando conectividade com coordenadores...")
        
        for coord_name, coord_config in self.coordinators_config.items():
            try:
                conn = await asyncpg.connect(
                    host=coord_config['host'],
                    port=coord_config['port'],
                    user='postgres',
                    password='TrixSuperSecure2024!',
                    database=coord_config['database']
                )
                await conn.execute('SELECT 1')
                await conn.close()
                logger.info(f"✅ Coordenador {coord_name} conectado")
            except Exception as e:
                logger.error(f"❌ Falha ao conectar coordenador {coord_name}: {e}")
                raise
    
    async def _execute_migrations_by_coordinator(self):
        """Executa migrations agrupadas por coordenador"""
        logger.info("📊 Executando migrations por coordenador...")
        
        # Agrupar tarefas por coordenador
        tasks_by_coordinator = {}
        for task in self.migration_tasks:
            if task.coordinator not in tasks_by_coordinator:
                tasks_by_coordinator[task.coordinator] = []
            tasks_by_coordinator[task.coordinator].append(task)
        
        # Executar migrations por coordenador
        for coord_name, tasks in tasks_by_coordinator.items():
            logger.info(f"🎯 Executando migrations no coordenador {coord_name}...")
            await self._execute_coordinator_migrations(coord_name, tasks)
    
    async def _execute_coordinator_migrations(self, coord_name: str, tasks: List[MigrationTask]):
        """Executa migrations em um coordenador específico"""
        coord_config = self.coordinators_config[coord_name]
        
        for task in tasks:
            logger.info(f"📋 Executando migration para {task.microservice}...")
            
            # Conectar ao banco específico do microserviço
            conn = await asyncpg.connect(
                host=coord_config['host'],
                port=coord_config['port'],
                user='postgres',
                password='TrixSuperSecure2024!',
                database=task.database
            )
            
            try:
                # Criar extensão Citus no banco do microserviço
                await conn.execute('CREATE EXTENSION IF NOT EXISTS citus;')
                
                # Executar migration consolidada (se existir)
                await self._execute_consolidated_migration(conn, task)
                
                logger.info(f"✅ Migration executada para {task.microservice}")
                
            except Exception as e:
                logger.error(f"❌ Erro na migration de {task.microservice}: {e}")
                raise
            finally:
                await conn.close()
    
    async def _execute_consolidated_migration(self, conn: asyncpg.Connection, task: MigrationTask):
        """Executa a migration consolidada para um microserviço"""
        # Verificar se migration já foi executada
        try:
            await conn.execute('SELECT 1 FROM alembic_version LIMIT 1')
            logger.info(f"ℹ️ Migration já executada para {task.microservice}")
            return
        except:
            # Tabela alembic_version não existe, executar migration
            pass
        
        # Carregar e executar migration consolidada
        migration_file = Path("microservices/core/shared_lib/migration/versions/consolidated_20250721_144511_all_microservices.py")
        
        if migration_file.exists():
            logger.info(f"📄 Executando migration consolidada para {task.microservice}...")
            
            # Aqui você executaria a migration consolidada
            # Por simplicidade, vamos criar as tabelas básicas
            await self._create_basic_tables(conn, task)
        else:
            logger.warning(f"⚠️ Arquivo de migration não encontrado para {task.microservice}")
    
    async def _create_basic_tables(self, conn: asyncpg.Connection, task: MigrationTask):
        """Cria tabelas básicas para o microserviço"""
        logger.info(f"🏗️ Criando tabelas básicas para {task.microservice}...")
        
        # Criar tabela de controle Alembic
        await conn.execute('''
            CREATE TABLE IF NOT EXISTS alembic_version (
                version_num VARCHAR(32) NOT NULL,
                CONSTRAINT alembic_version_pkc PRIMARY KEY (version_num)
            )
        ''')
        
        # Inserir versão da migration
        await conn.execute(
            "INSERT INTO alembic_version (version_num) VALUES ($1) ON CONFLICT DO NOTHING",
            "consolidated_20250721_144511"
        )
        
        # Criar tabelas específicas baseadas no microserviço
        if task.microservice == 'auth_service':
            await self._create_auth_tables(conn, task)
        elif task.microservice == 'user_service':
            await self._create_user_tables(conn, task)
        elif task.microservice == 'tenant_service':
            await self._create_tenant_tables(conn, task)
        elif task.microservice == 'restaurant_module':
            await self._create_restaurant_tables(conn, task)
        # Adicionar outros microserviços conforme necessário
    
    async def _create_auth_tables(self, conn: asyncpg.Connection, task: MigrationTask):
        """Cria tabelas do auth_service"""
        await conn.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                email VARCHAR(255) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                is_active BOOLEAN DEFAULT true,
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW()
            )
        ''')
        
        await conn.execute('''
            CREATE TABLE IF NOT EXISTS auth_sessions (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                user_id UUID REFERENCES users(id),
                session_token VARCHAR(255) UNIQUE NOT NULL,
                expires_at TIMESTAMP NOT NULL,
                created_at TIMESTAMP DEFAULT NOW()
            )
        ''')
    
    async def _create_user_tables(self, conn: asyncpg.Connection, task: MigrationTask):
        """Cria tabelas do user_service"""
        await conn.execute('''
            CREATE TABLE IF NOT EXISTS user_profiles (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                user_id UUID NOT NULL,
                first_name VARCHAR(100),
                last_name VARCHAR(100),
                phone VARCHAR(20),
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW()
            )
        ''')
    
    async def _create_tenant_tables(self, conn: asyncpg.Connection, task: MigrationTask):
        """Cria tabelas do tenant_service"""
        await conn.execute('''
            CREATE TABLE IF NOT EXISTS tenants (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                name VARCHAR(255) NOT NULL,
                domain VARCHAR(100) UNIQUE,
                is_active BOOLEAN DEFAULT true,
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW()
            )
        ''')
    
    async def _create_restaurant_tables(self, conn: asyncpg.Connection, task: MigrationTask):
        """Cria tabelas do restaurant_module"""
        await conn.execute('''
            CREATE TABLE IF NOT EXISTS restaurant_menus (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                tenant_id UUID NOT NULL,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                price DECIMAL(10,2),
                is_active BOOLEAN DEFAULT true,
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW()
            )
        ''')
    
    async def _setup_distributed_tables(self):
        """Configura distribuição de tabelas no Citus"""
        logger.info("🔗 Configurando distribuição de tabelas...")
        
        for task in self.migration_tasks:
            coord_config = self.coordinators_config[task.coordinator]
            
            conn = await asyncpg.connect(
                host=coord_config['host'],
                port=coord_config['port'],
                user='postgres',
                password='TrixSuperSecure2024!',
                database=task.database
            )
            
            try:
                for table in task.tables:
                    # Verificar se tabela existe
                    table_exists = await conn.fetchval(
                        "SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = $1)",
                        table
                    )
                    
                    if table_exists:
                        # Verificar se já está distribuída
                        is_distributed = await conn.fetchval(
                            "SELECT EXISTS (SELECT 1 FROM pg_dist_partition WHERE logicalrelid = $1::regclass)",
                            table
                        )
                        
                        if not is_distributed:
                            # Distribuir tabela
                            await conn.execute(
                                f"SELECT create_distributed_table($1, $2, shard_count => $3)",
                                table, task.distribution_key, task.shard_count
                            )
                            logger.info(f"✅ Tabela {table} distribuída com chave {task.distribution_key}")
                        else:
                            logger.info(f"ℹ️ Tabela {table} já está distribuída")
                    else:
                        logger.warning(f"⚠️ Tabela {table} não existe em {task.microservice}")
                
            finally:
                await conn.close()
    
    async def _verify_migrations(self):
        """Verifica integridade das migrations"""
        logger.info("🔍 Verificando integridade das migrations...")
        
        for task in self.migration_tasks:
            coord_config = self.coordinators_config[task.coordinator]
            
            try:
                conn = await asyncpg.connect(
                    host=coord_config['host'],
                    port=coord_config['port'],
                    user='postgres',
                    password='TrixSuperSecure2024!',
                    database=task.database
                )
                
                # Verificar versão Alembic
                version = await conn.fetchval("SELECT version_num FROM alembic_version")
                
                # Contar tabelas distribuídas
                distributed_count = await conn.fetchval(
                    "SELECT COUNT(*) FROM pg_dist_partition"
                )
                
                logger.info(f"✅ {task.microservice}: versão {version}, {distributed_count} tabelas distribuídas")
                
                await conn.close()
                
            except Exception as e:
                logger.error(f"❌ Erro na verificação de {task.microservice}: {e}")

if __name__ == "__main__":
    async def main():
        manager = CitusMigrationManager()
        await manager.run_distributed_migrations()
    
    asyncio.run(main())
