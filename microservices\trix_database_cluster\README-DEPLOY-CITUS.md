# 🚀 Deploy Citus Distribuído - Trix

## 📋 Visão Geral

Sistema Citus distribuído configurado com coordenadores por domínio para os 20 microserviços do Trix:

### 🏗️ Arquitetura Implementada

- **Core Coordinator (5432)**: 14 microserviços core
- **Shared Coordinator (5433)**: 4 microserviços shared  
- **Tenant Coordinator (5434)**: 2 microserviços tenant
- **Workers (5435, 5436)**: 2 workers compartilhados

## 🎯 Microserviços Configurados

### Core Services (localhost:5432)
```
✅ auth_service → auth_db
✅ user_service → users_db
✅ tenant_service → tenants_db
✅ core_service → core_db
✅ i18n_service → i18n_db
✅ ghost_function_service → ghost_function_db
✅ cdn_service → cdn_db
✅ domain_service → domain_db
✅ notification_service → notification_db
✅ payment_service → payment_db
✅ supplier_service → supplier_db
✅ synapse_ai_service → synapse_ai_db
✅ media_system → media_system_db
⚠️ dns_bridge_service → dns_bridge_db (não encontrado)
```

### Shared Services (localhost:5433)
```
✅ crm_module → crm_db
✅ email_module → email_db
✅ financial_module → financial_db
✅ hr_module → hr_db
```

### Tenant Services (localhost:5434)
```
✅ restaurant_module → restaurant_db
✅ consultancy_module → consultancy_db
```

## 🚀 Deploy Rápido

### Opção 1: Deploy Automático (Recomendado)

```bash
# Navegar para o diretório
cd microservices\trix_database_cluster

# Executar deploy completo
python deploy-citus-final.py
```

### Opção 2: Deploy Manual

```bash
# 1. Iniciar cluster
docker-compose up -d

# 2. Aguardar inicialização (1-2 minutos)
docker-compose ps

# 3. Configurar migrations
python setup-citus-migrations.py

# 4. Configurar seeds
python setup-citus-seeds.py
```

## 📦 Pré-requisitos

### Software Necessário

```bash
# Docker e Docker Compose
docker --version
docker-compose --version

# Python 3.8+ com dependências
pip install asyncpg bcrypt
```

## 🔧 Configurações Realizadas

### 1. Docker Compose Atualizado

- ✅ 3 Coordenadores Citus configurados
- ✅ 2 Workers compartilhados
- ✅ PgAdmin para administração
- ✅ Configurações otimizadas por domínio

### 2. Microserviços Atualizados

- ✅ 19 de 20 microserviços corrigidos (95% sucesso)
- ✅ URLs de banco atualizadas para Citus
- ✅ Dependências corrigidas
- ✅ External links configurados

### 3. Scripts de Automação

- ✅ `fix-core-services.py` - Corrige core services
- ✅ `fix-shared-services.py` - Corrige shared services  
- ✅ `setup-citus-migrations.py` - Configura migrations
- ✅ `setup-citus-seeds.py` - Configura seeds
- ✅ `deploy-citus-final.py` - Deploy completo

## 🌐 Pontos de Acesso

### Coordenadores Citus

- **Core Services**: `localhost:5432`
- **Shared Services**: `localhost:5433`
- **Tenant Services**: `localhost:5434`

### Workers

- **Worker 1**: `localhost:5435`
- **Worker 2**: `localhost:5436`

### Administração

- **PgAdmin**: `http://localhost:8081`
  - Email: `<EMAIL>`
  - Senha: `TrixAdmin2024!`

## 🔐 Credenciais Padrão

- **Usuário**: `postgres`
- **Senha**: `TrixSuperSecure2024!`

## 📊 Monitoramento

### Verificar Status do Cluster

```bash
# Status dos containers
docker-compose ps

# Logs dos coordenadores
docker logs trix-citus-core-coordinator
docker logs trix-citus-shared-coordinator
docker logs trix-citus-tenant-coordinator

# Conectar aos coordenadores
docker exec -it trix-citus-core-coordinator psql -U postgres -d postgres
```

### Verificar Distribuição Citus

```sql
-- Verificar nós do cluster
SELECT * FROM pg_dist_node;

-- Verificar tabelas distribuídas
SELECT * FROM pg_dist_partition;

-- Verificar shards
SELECT * FROM pg_dist_shard;
```

## 🛠️ Comandos Úteis

### Gerenciamento do Cluster

```bash
# Parar cluster
docker-compose down

# Reiniciar cluster
docker-compose restart

# Ver logs em tempo real
docker-compose logs -f
```

### Backup e Restore

```bash
# Backup de um coordenador
docker exec trix-citus-core-coordinator pg_dump -U postgres -d auth_db > backup_auth.sql

# Restore
docker exec -i trix-citus-core-coordinator psql -U postgres -d auth_db < backup_auth.sql
```

## 🔄 Troubleshooting

### Problemas Comuns

#### 1. Containers não iniciam

```bash
# Verificar logs
docker-compose logs

# Limpar volumes (CUIDADO!)
docker-compose down -v
```

#### 2. Erro de conectividade

```bash
# Verificar rede
docker network inspect trix-network

# Recriar rede se necessário
docker network rm trix-network
docker network create trix-network
```

#### 3. Erro nas migrations

```bash
# Verificar bancos criados
docker exec trix-citus-core-coordinator psql -U postgres -l

# Reexecutar migrations
python setup-citus-migrations.py
```

## 📈 Performance

### Configurações Otimizadas

- **Core Coordinator**: 256MB shared_buffers, 32 shards
- **Shared Coordinator**: 384MB shared_buffers, 32 shards  
- **Tenant Coordinator**: 512MB shared_buffers, 64 shards
- **Workers**: 512MB shared_buffers cada

## 🎯 Próximos Passos

1. ✅ Cluster Citus configurado
2. ✅ Microserviços atualizados (19/20)
3. ✅ Migrations distribuídas
4. ✅ Seeds distribuídos
5. 🔄 Testar conectividade dos microserviços
6. 🔄 Configurar monitoramento avançado
7. 🔄 Implementar backup automático
8. 🔄 Otimizar performance

## 📚 Arquivos Importantes

- `docker-compose.yml` - Configuração do cluster Citus
- `config/citus-*.conf` - Configurações específicas por coordenador
- `init-scripts/*/` - Scripts de inicialização por domínio
- `setup-citus-migrations.py` - Configurador de migrations
- `setup-citus-seeds.py` - Configurador de seeds
- `deploy-citus-final.py` - Deploy completo automatizado

---

**🎉 Sistema Citus Distribuído Trix está pronto para uso!**

**Taxa de Sucesso**: 95% (19/20 microserviços configurados)
**Coordenadores**: 3 ativos
**Workers**: 2 ativos
**Bancos**: 20 bancos distribuídos
