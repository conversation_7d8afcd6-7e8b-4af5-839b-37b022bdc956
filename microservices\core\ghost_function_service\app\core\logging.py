"""
Ghost Function Service - Logging Configuration

Configuração de logging estruturado usando structlog.
Fornece logs consistentes e observabilidade completa.
Utiliza a shared_lib para configurações centralizadas.

Author: Trix Platform Team
"""

import logging
import sys
from typing import Any, Dict

from app.core.config import get_settings
from microservices.core.shared_lib.config import (
    setup_logging,
    get_logger,
    StructuredLogger,
    LoggerMixin,
    log_performance_metric,
    log_business_event
)

settings = get_settings()


def setup_ghost_logging() -> StructuredLogger:
    """
    Configura o sistema de logging estruturado para o Ghost Function Service.

    Usa a shared_lib para configurações centralizadas.

    Returns:
        StructuredLogger: Logger configurado
    """
    return setup_logging(
        service_name="ghost-function-service",
        service_version="3.0.0"
    )


def log_ghost_operation(
    operation: str,
    service_name: str,
    duration_ms: float = None,
    **kwargs
):
    """
    Log específico para operações do Ghost Function Service.

    Args:
        operation: Nome da operação
        service_name: Nome do serviço afetado
        duration_ms: Duração da operação em ms
        **kwargs: Contexto adicional
    """
    log_performance_metric(
        service_name="ghost-function-service",
        operation=operation,
        duration_ms=duration_ms or 0,
        target_service=service_name,
        **kwargs
    )


def log_ghost_event(
    event_type: str,
    entity_id: str,
    **kwargs
):
    """
    Log específico para eventos de negócio do Ghost Function Service.

    Args:
        event_type: Tipo do evento
        entity_id: ID da entidade afetada
        **kwargs: Contexto adicional
    """
    log_business_event(
        service_name="ghost-function-service",
        event_type=event_type,
        entity_id=entity_id,
        **kwargs
    )


# Instância global do logger para o Ghost Function Service
ghost_logger = None


def get_ghost_logger():
    """
    Obtém o logger configurado para o Ghost Function Service.

    Returns:
        StructuredLogger: Logger configurado
    """
    global ghost_logger
    if ghost_logger is None:
        ghost_logger = setup_ghost_logging()
    return ghost_logger


# Aliases para compatibilidade
def get_logger(name: str = None):
    """Alias para compatibilidade com código existente."""
    return get_ghost_logger().get_logger(name)
