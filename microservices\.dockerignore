# === DOCKER BUILD OPTIMIZATION ===
# This .dockerignore is for builds from ./microservices context

# Exclude everything first
*

# Include only what we need for modules
!tenants/restaurant_module/
!tenants/consultancy_module/
!core/shared_lib/

# Exclude unnecessary files even in included directories
tenants/restaurant_module/tests/
tenants/restaurant_module/k8s/
tenants/restaurant_module/docker/
tenants/restaurant_module/migrations/
tenants/restaurant_module/menus/
tenants/restaurant_module/qr/
tenants/restaurant_module/receipts/
tenants/restaurant_module/*.md
tenants/restaurant_module/alembic.ini

# Exclude unnecessary files for consultancy module
tenants/consultancy_module/tests/
tenants/consultancy_module/k8s/
tenants/consultancy_module/docker/
tenants/consultancy_module/documents/
tenants/consultancy_module/reports/
tenants/consultancy_module/templates/
tenants/consultancy_module/backups/
tenants/consultancy_module/*.md

# Python cache files
**/__pycache__/
**/*.pyc
**/*.pyo
**/*.pyd

# IDE files
**/.vscode/
**/.idea/
**/*.swp
**/*.swo

# OS files
**/.DS_Store
**/Thumbs.db

# Git
**/.git/
**/.gitignore

# Logs
**/*.log
**/logs/

# Environment files
**/.env
**/.env.*

# Temporary files
**/tmp/
**/temp/
**/.tmp/
