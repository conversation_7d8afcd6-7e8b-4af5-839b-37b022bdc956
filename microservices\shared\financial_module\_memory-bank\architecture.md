# Financial Module - Internal Architecture

## 🏗️ **C4 Model - Level 3: Component Diagram**

```mermaid
graph TB
    subgraph "Financial Module Container"
        subgraph "API Layer"
            API_V1["API v1"]
            WS["WebSockets"]
            HEALTH["Health Checks"]
        end
        
        subgraph "Business Logic Layer"
            TRANS_SVC["Transaction Service"]
            BUDGET_SVC["Budget Service"]
            INVOICE_SVC["Invoice Service"]
            REPORT_SVC["Report Service"]
            CONSULT_SVC["Consultancy Service"]
            CATEGORY_SVC["Category Service"]
        end
        
        subgraph "Data Access Layer"
            TRANS_REPO["Transaction Repository"]
            BUDGET_REPO["Budget Repository"]
            INVOICE_REPO["Invoice Repository"]
            CATEGORY_REPO["Category Repository"]
        end
        
        subgraph "Integration Layer"
            USER_CLIENT["User Service Client"]
            TENANT_CLIENT["Tenant Service Client"]
            AUTH_CLIENT["Auth Service Client"]
            PAYMENT_CLIENT["Payment Service Client"]
        end
        
        subgraph "Infrastructure Layer (Shared Lib)"
            FIN_EVENTS["Financial Events"]
            FIN_MESSAGING["Financial Messaging"]
            FIN_SECURITY["Financial Security"]
            FIN_DB["Financial Database Utils"]
            FIN_OBSERV["Financial Observability"]
        end
    end
    
    subgraph "External Systems"
        USER_SVC["User Service"]
        TENANT_SVC["Tenant Service"]
        AUTH_SVC["Auth Service"]
        PAYMENT_SVC["Payment Service"]
        POSTGRES["PostgreSQL + Citus"]
        REDIS["Redis Cache"]
        KAFKA["Kafka"]
        RABBITMQ["RabbitMQ"]
    end
    
    API_V1 --> TRANS_SVC
    API_V1 --> BUDGET_SVC
    API_V1 --> INVOICE_SVC
    API_V1 --> REPORT_SVC
    API_V1 --> CONSULT_SVC
    API_V1 --> CATEGORY_SVC
    
    TRANS_SVC --> TRANS_REPO
    BUDGET_SVC --> BUDGET_REPO
    INVOICE_SVC --> INVOICE_REPO
    CATEGORY_SVC --> CATEGORY_REPO
    
    TRANS_SVC --> USER_CLIENT
    BUDGET_SVC --> TENANT_CLIENT
    INVOICE_SVC --> AUTH_CLIENT
    REPORT_SVC --> PAYMENT_CLIENT
    
    TRANS_REPO --> FIN_DB
    BUDGET_REPO --> FIN_DB
    INVOICE_REPO --> FIN_DB
    CATEGORY_REPO --> FIN_DB
    
    TRANS_SVC --> FIN_EVENTS
    BUDGET_SVC --> FIN_EVENTS
    INVOICE_SVC --> FIN_EVENTS
    
    FIN_EVENTS --> FIN_MESSAGING
    FIN_MESSAGING --> KAFKA
    FIN_MESSAGING --> RABBITMQ
    
    FIN_DB --> POSTGRES
    FIN_MESSAGING --> REDIS
    
    USER_CLIENT --> USER_SVC
    TENANT_CLIENT --> TENANT_SVC
    AUTH_CLIENT --> AUTH_SVC
    PAYMENT_CLIENT --> PAYMENT_SVC
```

## 🔄 **Event-Driven Architecture**

### **Event Flow Diagram**
```mermaid
sequenceDiagram
    participant Client
    participant API
    participant TransactionService
    participant EventPublisher
    participant Kafka
    participant ReportService
    participant NotificationService
    
    Client->>API: POST /transactions
    API->>TransactionService: create_transaction()
    TransactionService->>TransactionService: validate_business_rules()
    TransactionService->>EventPublisher: publish(TransactionCreated)
    EventPublisher->>Kafka: send_event()
    TransactionService->>API: transaction_created
    API->>Client: 201 Created
    
    Kafka->>ReportService: consume(TransactionCreated)
    ReportService->>ReportService: update_financial_reports()
    
    Kafka->>NotificationService: consume(TransactionCreated)
    NotificationService->>NotificationService: send_notification()
```

### **Event Types**
```python
# Financial Events using shared_lib
from microservices.core.shared_lib.config.infrastructure.financial import (
    TransactionEvent,
    BudgetEvent,
    InvoiceEvent,
    CategoryEvent,
    ReportEvent
)

# Event schemas
class TransactionCreatedEvent(TransactionEvent):
    event_type: str = "transaction_created"
    transaction_id: UUID
    tenant_id: UUID
    user_id: UUID
    amount: Decimal
    currency: str
    category_id: Optional[UUID]
    description: str
    timestamp: datetime

class BudgetExceededEvent(BudgetEvent):
    event_type: str = "budget_exceeded"
    budget_id: UUID
    tenant_id: UUID
    current_amount: Decimal
    budget_limit: Decimal
    percentage_exceeded: float
    timestamp: datetime
```

## 🗄️ **Database Architecture**

### **Sharding Strategy**
```mermaid
graph TB
    subgraph "Citus Coordinator"
        COORD["Coordinator Node"]
    end
    
    subgraph "Worker Nodes"
        WORKER1["Worker 1<br/>Shards 1-8"]
        WORKER2["Worker 2<br/>Shards 9-16"]
        WORKER3["Worker 3<br/>Shards 17-24"]
        WORKER4["Worker 4<br/>Shards 25-32"]
    end
    
    subgraph "Read Replicas"
        REPLICA1["Replica 1"]
        REPLICA2["Replica 2"]
        REPLICA3["Replica 3"]
    end
    
    COORD --> WORKER1
    COORD --> WORKER2
    COORD --> WORKER3
    COORD --> WORKER4
    
    WORKER1 --> REPLICA1
    WORKER2 --> REPLICA2
    WORKER3 --> REPLICA3
    
    CLIENT["Financial Module"] --> COORD
    CLIENT --> REPLICA1
    CLIENT --> REPLICA2
    CLIENT --> REPLICA3
```

### **Table Design**
```sql
-- Distributed tables (sharded by tenant_id)
CREATE TABLE transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL,
    user_id UUID NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'EUR',
    category_id UUID,
    description TEXT,
    transaction_type VARCHAR(20) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Distribute table by tenant_id
SELECT create_distributed_table('transactions', 'tenant_id');

-- Reference tables (replicated to all nodes)
CREATE TABLE currencies (
    code VARCHAR(3) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    symbol VARCHAR(10) NOT NULL,
    decimal_places INTEGER DEFAULT 2,
    is_active BOOLEAN DEFAULT true
);

SELECT create_reference_table('currencies');
```

### **Indexing Strategy**
```sql
-- Performance indexes
CREATE INDEX idx_transactions_tenant_date ON transactions (tenant_id, created_at DESC);
CREATE INDEX idx_transactions_user_date ON transactions (user_id, created_at DESC);
CREATE INDEX idx_transactions_category ON transactions (category_id) WHERE category_id IS NOT NULL;
CREATE INDEX idx_transactions_status ON transactions (status);
CREATE INDEX idx_transactions_type ON transactions (transaction_type);

-- Composite indexes for common queries
CREATE INDEX idx_transactions_tenant_status_date ON transactions (tenant_id, status, created_at DESC);
CREATE INDEX idx_transactions_tenant_type_date ON transactions (tenant_id, transaction_type, created_at DESC);

-- JSONB indexes for metadata queries
CREATE INDEX idx_transactions_metadata_gin ON transactions USING GIN (metadata);
```

## 🔐 **Security Architecture**

### **Multi-layer Security**
```mermaid
graph TB
    subgraph "Security Layers"
        subgraph "API Gateway Layer"
            RATE_LIMIT["Rate Limiting"]
            API_AUTH["API Authentication"]
            IP_FILTER["IP Filtering"]
        end
        
        subgraph "Service Layer"
            JWT_VALID["JWT Validation"]
            RBAC["Role-Based Access"]
            TENANT_ISOL["Tenant Isolation"]
        end
        
        subgraph "Data Layer"
            FIELD_ENCRYPT["Field Encryption"]
            AUDIT_LOG["Audit Logging"]
            DATA_MASK["Data Masking"]
        end
        
        subgraph "Infrastructure Layer"
            VAULT["HashiCorp Vault"]
            TLS["TLS Encryption"]
            NETWORK_POL["Network Policies"]
        end
    end
    
    CLIENT["Client"] --> RATE_LIMIT
    RATE_LIMIT --> API_AUTH
    API_AUTH --> IP_FILTER
    IP_FILTER --> JWT_VALID
    JWT_VALID --> RBAC
    RBAC --> TENANT_ISOL
    TENANT_ISOL --> FIELD_ENCRYPT
    FIELD_ENCRYPT --> AUDIT_LOG
    AUDIT_LOG --> DATA_MASK
```

### **Encryption Strategy**
```python
# Using shared_lib financial encryption
from microservices.core.shared_lib.config.infrastructure.financial import FinancialEncryption

encryption = FinancialEncryption()

# Sensitive field encryption
class Transaction(Base):
    __tablename__ = 'transactions'
    
    id = Column(UUID, primary_key=True)
    tenant_id = Column(UUID, nullable=False)
    
    # Encrypted sensitive fields
    _amount = Column('amount', Text)  # Encrypted
    _account_number = Column('account_number', Text)  # Encrypted
    
    @hybrid_property
    def amount(self):
        return encryption.decrypt_decimal(self._amount)
    
    @amount.setter
    def amount(self, value):
        self._amount = encryption.encrypt_decimal(value)
```

## 📊 **Observability Architecture**

### **Monitoring Stack**
```mermaid
graph TB
    subgraph "Financial Module"
        APP["Application"]
        METRICS["Financial Metrics"]
        TRACES["Financial Tracing"]
        LOGS["Financial Logging"]
    end
    
    subgraph "Collection Layer"
        PROMETHEUS["Prometheus"]
        JAEGER["Jaeger"]
        FLUENTD["Fluentd"]
    end
    
    subgraph "Storage Layer"
        PROM_STORAGE["Prometheus TSDB"]
        JAEGER_STORAGE["Jaeger Storage"]
        ELASTICSEARCH["Elasticsearch"]
    end
    
    subgraph "Visualization Layer"
        GRAFANA["Grafana"]
        KIBANA["Kibana"]
        JAEGER_UI["Jaeger UI"]
    end
    
    APP --> METRICS
    APP --> TRACES
    APP --> LOGS
    
    METRICS --> PROMETHEUS
    TRACES --> JAEGER
    LOGS --> FLUENTD
    
    PROMETHEUS --> PROM_STORAGE
    JAEGER --> JAEGER_STORAGE
    FLUENTD --> ELASTICSEARCH
    
    PROM_STORAGE --> GRAFANA
    JAEGER_STORAGE --> JAEGER_UI
    ELASTICSEARCH --> KIBANA
```

### **Custom Financial Metrics**
```python
# Financial-specific metrics using shared_lib
from microservices.core.shared_lib.config.infrastructure.financial import FinancialMetrics

metrics = FinancialMetrics("financial-module")

# Business metrics
metrics.register_counter(
    name="transactions_total",
    description="Total number of transactions",
    labels=["tenant_id", "transaction_type", "status"]
)

metrics.register_histogram(
    name="transaction_amount",
    description="Transaction amounts",
    labels=["tenant_id", "currency"],
    buckets=[10, 50, 100, 500, 1000, 5000, 10000]
)

metrics.register_gauge(
    name="active_budgets",
    description="Number of active budgets",
    labels=["tenant_id"]
)

metrics.register_histogram(
    name="financial_operation_duration",
    description="Duration of financial operations",
    labels=["operation_type", "tenant_id"]
)
```

## 🔄 **Data Flow Architecture**

### **Transaction Processing Flow**
```mermaid
flowchart TD
    START(["Transaction Request"]) --> VALIDATE{"Validate Request"}
    VALIDATE -->|Invalid| ERROR_RESP["Error Response"]
    VALIDATE -->|Valid| AUTH{"Authenticate & Authorize"}
    
    AUTH -->|Unauthorized| AUTH_ERROR["Auth Error"]
    AUTH -->|Authorized| TENANT_CHECK{"Tenant Isolation Check"}
    
    TENANT_CHECK -->|Failed| TENANT_ERROR["Tenant Error"]
    TENANT_CHECK -->|Passed| BUSINESS_RULES{"Business Rules Validation"}
    
    BUSINESS_RULES -->|Failed| BUSINESS_ERROR["Business Rule Error"]
    BUSINESS_RULES -->|Passed| DB_TRANSACTION["Database Transaction"]
    
    DB_TRANSACTION --> ENCRYPT["Encrypt Sensitive Data"]
    ENCRYPT --> SAVE["Save to Database"]
    SAVE --> PUBLISH_EVENT["Publish Event"]
    
    PUBLISH_EVENT --> AUDIT["Audit Log"]
    AUDIT --> METRICS["Update Metrics"]
    METRICS --> SUCCESS_RESP["Success Response"]
    
    ERROR_RESP --> END(["End"])
    AUTH_ERROR --> END
    TENANT_ERROR --> END
    BUSINESS_ERROR --> END
    SUCCESS_RESP --> END
```

### **Report Generation Flow**
```mermaid
flowchart TD
    REPORT_REQ(["Report Request"]) --> CACHE_CHECK{"Check Cache"}
    CACHE_CHECK -->|Hit| RETURN_CACHED["Return Cached Report"]
    CACHE_CHECK -->|Miss| QUERY_BUILDER["Build Optimized Query"]
    
    QUERY_BUILDER --> SHARD_ROUTING["Route to Appropriate Shards"]
    SHARD_ROUTING --> PARALLEL_EXEC["Parallel Execution"]
    
    PARALLEL_EXEC --> AGGREGATE["Aggregate Results"]
    AGGREGATE --> TRANSFORM["Transform Data"]
    TRANSFORM --> CACHE_STORE["Store in Cache"]
    
    CACHE_STORE --> RETURN_REPORT["Return Report"]
    RETURN_CACHED --> END(["End"])
    RETURN_REPORT --> END
```

## 🧪 **Testing Architecture**

### **Testing Pyramid**
```mermaid
graph TB
    subgraph "Testing Pyramid"
        E2E["E2E Tests<br/>5%<br/>Full system integration"]
        INTEGRATION["Integration Tests<br/>15%<br/>Service interactions"]
        UNIT["Unit Tests<br/>80%<br/>Individual components"]
    end
    
    subgraph "Test Types"
        UNIT_TYPES["• Repository tests<br/>• Service tests<br/>• Utility tests<br/>• Model tests"]
        INT_TYPES["• API tests<br/>• Database tests<br/>• Event tests<br/>• External service tests"]
        E2E_TYPES["• User journey tests<br/>• Performance tests<br/>• Security tests<br/>• Chaos tests"]
    end
    
    UNIT --> UNIT_TYPES
    INTEGRATION --> INT_TYPES
    E2E --> E2E_TYPES
```

### **Test Data Management**
```python
# Test fixtures using shared_lib
from microservices.core.shared_lib.config.infrastructure.testing import FinancialTestFixtures

class TestTransactionService:
    @pytest.fixture
    def test_data(self):
        return FinancialTestFixtures.create_test_tenant_with_transactions(
            tenant_count=1,
            transactions_per_tenant=10,
            include_categories=True
        )
    
    async def test_create_transaction(self, test_data):
        # Test with isolated test data
        pass
    
    async def test_transaction_isolation(self, test_data):
        # Test multi-tenant isolation
        pass
```

## 🚀 **Deployment Architecture**

### **Kubernetes Deployment**
```yaml
# Multi-environment deployment
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: financial-module
spec:
  project: trix-financial
  source:
    repoURL: https://github.com/trix/financial-module
    targetRevision: HEAD
    path: k8s/overlays/production
  destination:
    server: https://kubernetes.default.svc
    namespace: financial-production
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
    - CreateNamespace=true
```

### **Service Mesh Configuration**
```yaml
# Istio configuration
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: financial-module
spec:
  hosts:
  - financial-module
  http:
  - match:
    - uri:
        prefix: "/api/v1/"
    route:
    - destination:
        host: financial-module
        port:
          number: 8000
    fault:
      delay:
        percentage:
          value: 0.1
        fixedDelay: 5s
    retries:
      attempts: 3
      perTryTimeout: 10s
```

Esta arquitetura garante escalabilidade, segurança e observabilidade para o Financial Module, seguindo as melhores práticas de microserviços e utilizando componentes compartilhados da shared_lib para consistência e reutilização de código.