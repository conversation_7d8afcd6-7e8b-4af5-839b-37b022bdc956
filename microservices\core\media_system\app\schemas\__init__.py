"""
Media System Schemas Package
============================

Schemas Pydantic para validação de dados do Media System,
organizados seguindo o padrão do User Service.

✅ MIGRAÇÃO CONCLUÍDA - Estrutura organizada e usando shared_lib
"""

# Import all schemas for easy access
from .media import (
    MediaBase,
    MediaCreate,
    MediaUpdate,
    MediaResponse,
    MediaUploadRequest,
    MediaUploadResponse,
    MediaBulkUploadRequest,
    MediaBulkUploadResponse,
    MediaSearchRequest,
    MediaSearchResponse
)

from .processing import (
    ProcessingJobBase,
    ProcessingJobCreate,
    ProcessingJobUpdate,
    ProcessingJobResponse,
    ProcessingRequest,
    ProcessingResponse,
    ThumbnailRequest,
    ThumbnailResponse,
    OCRRequest,
    OCRResponse
)

from .events import (
    MediaEventBase,
    MediaUploadedEvent,
    MediaProcessedEvent,
    MediaDeletedEvent,
    MediaAccessEvent,
    ProcessingJobEvent
)

__all__ = [
    # Media Schemas
    "MediaBase",
    "MediaCreate", 
    "MediaUpdate",
    "MediaResponse",
    "MediaUploadRequest",
    "MediaUploadResponse",
    "MediaBulkUploadRequest",
    "MediaBulkUploadResponse",
    "MediaSearchRequest",
    "MediaSearchResponse",
    
    # Processing Schemas
    "ProcessingJobBase",
    "ProcessingJobCreate",
    "ProcessingJobUpdate", 
    "ProcessingJobResponse",
    "ProcessingRequest",
    "ProcessingResponse",
    "ThumbnailRequest",
    "ThumbnailResponse",
    "OCRRequest",
    "OCRResponse",
    
    # Event Schemas
    "MediaEventBase",
    "MediaUploadedEvent",
    "MediaProcessedEvent",
    "MediaDeletedEvent",
    "MediaAccessEvent",
    "ProcessingJobEvent"
]
