# ============================================================================
# PAYMENT SERVICES - Microserviço de Pagamentos
# ============================================================================
# Porta: 8020 | Database: postgres-payments (5441) | Redis: DB 8
# Submódulos: payments (Processamento de Pagamentos), subscriptions (Gerenciamento de Assinaturas)
# Configurações: Busca variáveis do docker-compose.yml principal
# ============================================================================

# Importa variáveis globais do arquivo principal
x-environment: &global-environment
  SECRET_KEY: ${SECRET_KEY}
  DEBUG: ${DEBUG}
  REDIS_PASSWORD: ${REDIS_PASSWORD}
  JWT_SECRET_KEY: ${JWT_SECRET_KEY}
  PAYMENTS_DB_NAME: ${PAYMENTS_DB_NAME}
  PAYMENTS_DB_USER: ${PAYMENTS_DB_USER}
  PAYMENTS_DB_PASSWORD: ${PAYMENTS_DB_PASSWORD}
  STRIPE_SECRET_KEY: ${STRIPE_SECRET_KEY}
  STRIPE_WEBHOOK_SECRET: ${STRIPE_WEBHOOK_SECRET}
  PAYPAL_CLIENT_ID: ${PAYPAL_CLIENT_ID}
  PAYPAL_CLIENT_SECRET: ${PAYPAL_CLIENT_SECRET}
  PAGSEGURO_EMAIL: ${PAGSEGURO_EMAIL}
  PAGSEGURO_TOKEN: ${PAGSEGURO_TOKEN}
  MERCADOPAGO_ACCESS_TOKEN: ${MERCADOPAGO_ACCESS_TOKEN}

services:
  # ============================================================================
  # PAYMENT SERVICES - Serviço Principal
  # ============================================================================
  trix-infrastructure-payments:
    build:
      context: ../
      dockerfile: Dockerfile
    container_name: trix-infrastructure-payments
    ports:
      - "8020:8000"
    environment:
      <<: *global-environment
      DATABASE_URL: postgresql+asyncpg://${PAYMENTS_DB_USER}:${PAYMENTS_DB_PASSWORD}@trix-db-payments:5432/${PAYMENTS_DB_NAME}
      REDIS_URL: redis://:${REDIS_PASSWORD}@trix-redis:6379/8
      AUTH_SERVICE_URL: http://trix-core-auth:8001
      SERVICE_NAME: payment-services
      SERVICE_VERSION: 1.0.0
      CELERY_BROKER_URL: redis://:${REDIS_PASSWORD}@trix-redis:6379/8
      CELERY_RESULT_BACKEND: redis://:${REDIS_PASSWORD}@trix-redis:6379/8
      PIX_ENABLED: true
      BOLETO_ENABLED: true
    depends_on:
    
