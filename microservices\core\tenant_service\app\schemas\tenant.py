"""
Tenant schemas for API serialization and validation.
"""

from __future__ import annotations
import uuid
from datetime import datetime
from typing import Optional, Dict, Any, TYPE_CHECKING

from pydantic import BaseModel, ConfigDict, Field, field_validator

if TYPE_CHECKING:
    from app.models.tenant import Tenant as TenantModel


class TenantBase(BaseModel):
    """Base tenant schema with common fields."""

    name: str = Field(..., min_length=1, max_length=255, description="Tenant business name")
    slug: str = Field(..., min_length=1, max_length=100, description="URL-friendly identifier")
    description: Optional[str] = Field(None, description="Tenant description")
    tenant_type: str = Field(default="restaurant", description="Type of tenant")
    is_active: bool = Field(default=True, description="Whether tenant is active")
    contact_email: Optional[str] = Field(None, description="Primary contact email")
    contact_phone: Optional[str] = Field(None, description="Primary contact phone")

    # Address fields
    address_line1: Optional[str] = Field(None, description="Primary address line")
    address_line2: Optional[str] = Field(None, description="Secondary address line")
    city: Optional[str] = Field(None, description="City")
    state: Optional[str] = Field(None, description="State/Province")
    postal_code: Optional[str] = Field(None, description="Postal/ZIP code")
    country: Optional[str] = Field(None, description="Country")

    # Business configuration
    business_hours: Optional[Dict[str, Any]] = Field(None, description="Business operating hours")
    max_users: int = Field(default=10, ge=1, description="Maximum number of users allowed")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")

    @field_validator('slug', mode='before')
    @classmethod
    def validate_slug(cls, v):
        """Validate slug format."""
        if not v.replace('-', '').replace('_', '').isalnum():
            raise ValueError('Slug must contain only alphanumeric characters, hyphens, and underscores')
        return v.lower()

    @field_validator('tenant_type', mode='before')
    @classmethod
    def validate_tenant_type(cls, v):
        """Validate tenant type."""
        allowed_types = ['restaurant', 'consultancy', 'shop', 'service', 'retail']
        if v not in allowed_types:
            raise ValueError(f'Tenant type must be one of: {", ".join(allowed_types)}')
        return v


class TenantCreate(TenantBase):
    """Schema for creating a new tenant."""
    pass


class TenantUpdate(BaseModel):
    """Schema for updating an existing tenant."""

    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    tenant_type: Optional[str] = None
    is_active: Optional[bool] = None
    is_verified: Optional[bool] = None
    contact_email: Optional[str] = None
    contact_phone: Optional[str] = None

    # Address fields
    address_line1: Optional[str] = None
    address_line2: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    postal_code: Optional[str] = None
    country: Optional[str] = None

    # Business configuration
    business_hours: Optional[Dict[str, Any]] = None
    max_users: Optional[int] = Field(None, ge=1)
    metadata: Optional[Dict[str, Any]] = None

    @field_validator('tenant_type', mode='before')
    @classmethod
    def validate_tenant_type(cls, v):
        """Validate tenant type."""
        if v is not None:
            allowed_types = ['restaurant', 'consultancy', 'shop', 'service', 'retail']
            if v not in allowed_types:
                raise ValueError(f'Tenant type must be one of: {", ".join(allowed_types)}')
        return v


class TenantInDBBase(TenantBase):
    """Base schema for tenant data from database."""

    id: uuid.UUID
    is_verified: bool = False
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class Tenant(TenantInDBBase):
    """Complete tenant schema for API responses."""

    @property
    def full_address(self) -> Optional[str]:
        """Get formatted full address."""
        address_parts = [
            self.address_line1,
            self.address_line2,
            self.city,
            self.state,
            self.postal_code,
            self.country
        ]
        address_parts = [part for part in address_parts if part]
        return ", ".join(address_parts) if address_parts else None


class TenantSimple(BaseModel):
    """Simplified tenant schema for listings."""

    id: uuid.UUID
    name: str
    slug: str
    tenant_type: str
    is_active: bool
    is_verified: bool

    model_config = ConfigDict(from_attributes=True)


class TenantSummary(BaseModel):
    """Summary tenant schema for dashboard."""

    id: uuid.UUID
    name: str
    slug: str
    tenant_type: str
    is_active: bool
    is_verified: bool
    contact_email: Optional[str] = None
    city: Optional[str] = None
    country: Optional[str] = None
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)
