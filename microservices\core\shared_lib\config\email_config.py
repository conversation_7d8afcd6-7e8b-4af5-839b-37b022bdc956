"""
Shared Email Configuration for Trix Platform.

This module provides centralized email configuration that can be used across
all microservices that need email functionality, including:
- Email Module (primary email service)
- Notification Service (email notifications)
- CRM Module (email marketing)
- Commerce Service (transactional emails)
- Any other service that needs email capabilities

🔗 Integration: This configuration follows the shared_lib pattern established
in the user_service migration and provides consistent email settings across
the entire platform.
"""

import os
from typing import Dict, List, Optional, Any
from pydantic import Field, validator
from pydantic_settings import BaseSettings


class EmailProviderSettings(BaseSettings):
    """Email provider configurations for multiple providers."""
    
    # Default provider selection
    default_provider: str = Field(default="sendgrid", env="EMAIL_DEFAULT_PROVIDER")
    
    # SendGrid Configuration
    sendgrid_api_key: Optional[str] = Field(default=None, env="SENDGRID_API_KEY")
    sendgrid_from_email: str = Field(default="<EMAIL>", env="SENDGRID_FROM_EMAIL")
    sendgrid_from_name: str = Field(default="Trix Platform", env="SENDGRID_FROM_NAME")
    
    # AWS SES Configuration
    aws_access_key_id: Optional[str] = Field(default=None, env="AWS_ACCESS_KEY_ID")
    aws_secret_access_key: Optional[str] = Field(default=None, env="AWS_SECRET_ACCESS_KEY")
    aws_region: str = Field(default="us-east-1", env="AWS_REGION")
    aws_ses_from_email: str = Field(default="<EMAIL>", env="AWS_SES_FROM_EMAIL")
    
    # Mailgun Configuration
    mailgun_api_key: Optional[str] = Field(default=None, env="MAILGUN_API_KEY")
    mailgun_domain: Optional[str] = Field(default=None, env="MAILGUN_DOMAIN")
    mailgun_from_email: str = Field(default="<EMAIL>", env="MAILGUN_FROM_EMAIL")
    
    class Config:
        env_prefix = "EMAIL_PROVIDER_"
        case_sensitive = False


class SMTPSettings(BaseSettings):
    """SMTP server configuration for direct email sending."""
    
    # SMTP Server Configuration
    host: str = Field(default="localhost", env="SMTP_HOST")
    port: int = Field(default=587, env="SMTP_PORT")
    use_tls: bool = Field(default=True, env="SMTP_USE_TLS")
    use_ssl: bool = Field(default=False, env="SMTP_USE_SSL")
    username: Optional[str] = Field(default=None, env="SMTP_USERNAME")
    password: Optional[str] = Field(default=None, env="SMTP_PASSWORD")
    
    # Connection settings
    timeout: int = Field(default=30, env="SMTP_TIMEOUT")
    max_connections: int = Field(default=10, env="SMTP_MAX_CONNECTIONS")
    
    @validator('port')
    def validate_port(cls, v):
        if not 1 <= v <= 65535:
            raise ValueError('Port must be between 1 and 65535')
        return v
    
    class Config:
        env_prefix = "SMTP_"
        case_sensitive = False


class IMAPSettings(BaseSettings):
    """IMAP server configuration for email retrieval."""
    
    # IMAP Server Configuration
    host: str = Field(default="localhost", env="IMAP_HOST")
    port: int = Field(default=143, env="IMAP_PORT")
    use_ssl: bool = Field(default=False, env="IMAP_USE_SSL")
    use_starttls: bool = Field(default=True, env="IMAP_USE_STARTTLS")
    
    # Connection settings
    timeout: int = Field(default=30, env="IMAP_TIMEOUT")
    max_connections: int = Field(default=5, env="IMAP_MAX_CONNECTIONS")
    
    @validator('port')
    def validate_port(cls, v):
        if not 1 <= v <= 65535:
            raise ValueError('Port must be between 1 and 65535')
        return v
    
    class Config:
        env_prefix = "IMAP_"
        case_sensitive = False


class EmailSecuritySettings(BaseSettings):
    """Email security configuration including DKIM, SPF, DMARC."""
    
    # DKIM Configuration
    dkim_selector: str = Field(default="mail", env="DKIM_SELECTOR")
    dkim_private_key_path: str = Field(default="/etc/opendkim/keys", env="DKIM_PRIVATE_KEY_PATH")
    
    # DNS Records Templates
    default_mx_records: List[str] = Field(
        default=["10 mail.{domain}", "20 mail2.{domain}"],
        env="DEFAULT_MX_RECORDS"
    )
    default_spf_record: str = Field(
        default="v=spf1 mx a:{domain} ~all",
        env="DEFAULT_SPF_RECORD"
    )
    default_dmarc_record: str = Field(
        default="v=DMARC1; p=none; sp=none; rua=mailto:dmarc@{domain}",
        env="DEFAULT_DMARC_RECORD"
    )
    
    # Email Server Configuration
    email_server_hostname: str = Field(default="mail.trix.com", env="EMAIL_SERVER_HOSTNAME")
    
    class Config:
        env_prefix = "EMAIL_SECURITY_"
        case_sensitive = False


class EmailStorageSettings(BaseSettings):
    """Email storage and file handling configuration."""
    
    # Maildir Configuration
    maildir_base_path: str = Field(default="/var/vmail", env="MAILDIR_BASE_PATH")
    
    # File Storage
    upload_dir: str = Field(default="/app/uploads", env="EMAIL_UPLOAD_DIR")
    template_storage_dir: str = Field(default="/app/templates", env="EMAIL_TEMPLATE_DIR")
    attachment_storage_dir: str = Field(default="/app/attachments", env="EMAIL_ATTACHMENT_DIR")
    
    # File Size Limits
    max_file_size: int = Field(default=10485760, env="EMAIL_MAX_FILE_SIZE")  # 10MB
    max_attachment_size: int = Field(default=25165824, env="EMAIL_MAX_ATTACHMENT_SIZE")  # 25MB
    
    # Quota Configuration
    default_quota_mb: int = Field(default=1024, env="EMAIL_DEFAULT_QUOTA_MB")  # 1GB
    max_quota_mb: int = Field(default=10240, env="EMAIL_MAX_QUOTA_MB")  # 10GB
    
    class Config:
        env_prefix = "EMAIL_STORAGE_"
        case_sensitive = False


class EmailDeliverySettings(BaseSettings):
    """Email delivery and queue configuration."""
    
    # Delivery Configuration
    max_recipients_per_email: int = Field(default=100, env="EMAIL_MAX_RECIPIENTS")
    email_queue_batch_size: int = Field(default=50, env="EMAIL_BATCH_SIZE")
    email_retry_attempts: int = Field(default=3, env="EMAIL_RETRY_ATTEMPTS")
    email_retry_delay: int = Field(default=300, env="EMAIL_RETRY_DELAY")  # 5 minutes
    
    # Rate Limiting
    email_rate_limit_per_hour: int = Field(default=1000, env="EMAIL_RATE_LIMIT_HOUR")
    email_rate_limit_per_day: int = Field(default=10000, env="EMAIL_RATE_LIMIT_DAY")
    
    # Analytics Configuration
    track_email_opens: bool = Field(default=True, env="EMAIL_TRACK_OPENS")
    track_email_clicks: bool = Field(default=True, env="EMAIL_TRACK_CLICKS")
    analytics_retention_days: int = Field(default=365, env="EMAIL_ANALYTICS_RETENTION")
    bounce_webhook_secret: Optional[str] = Field(default=None, env="EMAIL_BOUNCE_WEBHOOK_SECRET")
    
    class Config:
        env_prefix = "EMAIL_DELIVERY_"
        case_sensitive = False


class EmailTemplateSettings(BaseSettings):
    """Email template configuration."""
    
    # Template Configuration
    default_template_language: str = Field(default="en", env="EMAIL_DEFAULT_LANGUAGE")
    template_cache_ttl: int = Field(default=3600, env="EMAIL_TEMPLATE_CACHE_TTL")  # 1 hour
    enable_template_preview: bool = Field(default=True, env="EMAIL_ENABLE_PREVIEW")
    
    # Template Limits
    max_template_size: int = Field(default=1048576, env="EMAIL_MAX_TEMPLATE_SIZE")  # 1MB
    template_variables_limit: int = Field(default=100, env="EMAIL_TEMPLATE_VAR_LIMIT")
    
    class Config:
        env_prefix = "EMAIL_TEMPLATE_"
        case_sensitive = False


class SharedEmailSettings(BaseSettings):
    """
    Centralized email configuration for the Trix platform.
    
    This class combines all email-related settings and provides a unified
    interface for email configuration across all microservices.
    """
    
    # Service Information
    service_name: str = Field(default="shared-email", env="EMAIL_SERVICE_NAME")
    service_version: str = Field(default="1.0.0", env="EMAIL_SERVICE_VERSION")
    
    # Environment
    environment: str = Field(default="development", env="ENVIRONMENT")
    debug: bool = Field(default=False, env="EMAIL_DEBUG")
    
    # Component Settings
    providers: EmailProviderSettings = EmailProviderSettings()
    smtp: SMTPSettings = SMTPSettings()
    imap: IMAPSettings = IMAPSettings()
    security: EmailSecuritySettings = EmailSecuritySettings()
    storage: EmailStorageSettings = EmailStorageSettings()
    delivery: EmailDeliverySettings = EmailDeliverySettings()
    templates: EmailTemplateSettings = EmailTemplateSettings()
    
    class Config:
        env_prefix = "SHARED_EMAIL_"
        case_sensitive = False
    
    def get_provider_config(self, provider: str) -> Dict[str, Any]:
        """Get configuration for a specific email provider."""
        provider = provider.lower()
        
        if provider == "sendgrid":
            return {
                "api_key": self.providers.sendgrid_api_key,
                "from_email": self.providers.sendgrid_from_email,
                "from_name": self.providers.sendgrid_from_name,
            }
        elif provider == "ses" or provider == "aws_ses":
            return {
                "access_key_id": self.providers.aws_access_key_id,
                "secret_access_key": self.providers.aws_secret_access_key,
                "region": self.providers.aws_region,
                "from_email": self.providers.aws_ses_from_email,
            }
        elif provider == "mailgun":
            return {
                "api_key": self.providers.mailgun_api_key,
                "domain": self.providers.mailgun_domain,
                "from_email": self.providers.mailgun_from_email,
            }
        elif provider == "smtp":
            return {
                "host": self.smtp.host,
                "port": self.smtp.port,
                "use_tls": self.smtp.use_tls,
                "use_ssl": self.smtp.use_ssl,
                "username": self.smtp.username,
                "password": self.smtp.password,
            }
        else:
            raise ValueError(f"Unknown email provider: {provider}")
    
    def get_dns_records(self, domain: str) -> Dict[str, Any]:
        """Get DNS records for a domain."""
        return {
            "mx_records": [record.format(domain=domain) for record in self.security.default_mx_records],
            "spf_record": self.security.default_spf_record.format(domain=domain),
            "dmarc_record": self.security.default_dmarc_record.format(domain=domain),
        }


# Global shared email settings instance
shared_email_settings = SharedEmailSettings()


def get_email_settings() -> SharedEmailSettings:
    """Get the global email settings instance."""
    return shared_email_settings


def get_provider_config(provider: str) -> Dict[str, Any]:
    """Get configuration for a specific email provider."""
    return shared_email_settings.get_provider_config(provider)


def get_smtp_config() -> Dict[str, Any]:
    """Get SMTP configuration."""
    return {
        "host": shared_email_settings.smtp.host,
        "port": shared_email_settings.smtp.port,
        "use_tls": shared_email_settings.smtp.use_tls,
        "use_ssl": shared_email_settings.smtp.use_ssl,
        "username": shared_email_settings.smtp.username,
        "password": shared_email_settings.smtp.password,
        "timeout": shared_email_settings.smtp.timeout,
        "max_connections": shared_email_settings.smtp.max_connections,
    }


def get_imap_config() -> Dict[str, Any]:
    """Get IMAP configuration."""
    return {
        "host": shared_email_settings.imap.host,
        "port": shared_email_settings.imap.port,
        "use_ssl": shared_email_settings.imap.use_ssl,
        "use_starttls": shared_email_settings.imap.use_starttls,
        "timeout": shared_email_settings.imap.timeout,
        "max_connections": shared_email_settings.imap.max_connections,
    }


def get_dns_records(domain: str) -> Dict[str, Any]:
    """Get DNS records for a domain."""
    return shared_email_settings.get_dns_records(domain)


# Email provider constants
EMAIL_PROVIDERS = {
    "sendgrid": "SendGrid",
    "ses": "Amazon SES",
    "aws_ses": "Amazon SES",
    "mailgun": "Mailgun",
    "smtp": "SMTP Server",
}

# Default email templates
DEFAULT_EMAIL_TEMPLATES = {
    "welcome": {
        "subject": "Welcome to {company_name}!",
        "html": "<h1>Welcome {user_name}!</h1><p>Thank you for joining {company_name}.</p>",
        "text": "Welcome {user_name}! Thank you for joining {company_name}.",
    },
    "password_reset": {
        "subject": "Password Reset Request",
        "html": "<h1>Password Reset</h1><p>Click <a href='{reset_link}'>here</a> to reset your password.</p>",
        "text": "Password Reset: {reset_link}",
    },
    "notification": {
        "subject": "Notification from {company_name}",
        "html": "<h1>Notification</h1><p>{message}</p>",
        "text": "Notification: {message}",
    },
}
