"""
Ghost Function Service - Logging Configuration

Configuração de logging específica do Ghost Function Service que estende
as configurações da shared_lib para funcionalidades específicas.

Author: Trix Platform Team
"""

import logging
from microservices.core.shared_lib.infrastructure.observability import get_logger
from ..config.settings import get_settings

# Configurações
settings = get_settings()


def get_ghost_logger(name: str = "ghost-function-service") -> logging.Logger:
    """
    Get a logger instance for Ghost Function Service.
    
    Args:
        name: Logger name (default: ghost-function-service)
        
    Returns:
        logging.Logger: Configured logger instance
    """
    return get_logger(name)


def log_proxy_request(logger: logging.Logger, service_name: str, endpoint: str, 
                     method: str, status_code: int, duration_ms: float):
    """Log proxy request details."""
    logger.info(
        "Proxy request completed",
        extra={
            "service_name": service_name,
            "endpoint": endpoint,
            "method": method,
            "status_code": status_code,
            "duration_ms": duration_ms,
            "event_type": "proxy_request"
        }
    )


def log_failover_event(logger: logging.Logger, service_name: str, from_source: str, 
                      to_source: str, reason: str, duration_ms: float):
    """Log failover event details."""
    logger.warning(
        "Failover event occurred",
        extra={
            "service_name": service_name,
            "from_source": from_source,
            "to_source": to_source,
            "reason": reason,
            "duration_ms": duration_ms,
            "event_type": "failover"
        }
    )


def log_hibernation_change(logger: logging.Logger, service_name: str, 
                          from_level: str, to_level: str):
    """Log hibernation level change."""
    logger.info(
        "Hibernation level changed",
        extra={
            "service_name": service_name,
            "from_level": from_level,
            "to_level": to_level,
            "event_type": "hibernation_change"
        }
    )


def log_circuit_breaker_event(logger: logging.Logger, service_name: str, 
                             state: str, failure_count: int):
    """Log circuit breaker state change."""
    logger.warning(
        "Circuit breaker state changed",
        extra={
            "service_name": service_name,
            "state": state,
            "failure_count": failure_count,
            "event_type": "circuit_breaker"
        }
    )


def log_ghost_wake_up(logger: logging.Logger, service_name: str, 
                     hibernation_level: str, wake_up_time_ms: float):
    """Log ghost instance wake up."""
    logger.info(
        "Ghost instance woke up",
        extra={
            "service_name": service_name,
            "hibernation_level": hibernation_level,
            "wake_up_time_ms": wake_up_time_ms,
            "event_type": "ghost_wake_up"
        }
    )


def log_service_health_change(logger: logging.Logger, service_name: str, 
                             is_healthy: bool, response_time_ms: float):
    """Log service health status change."""
    level = logging.INFO if is_healthy else logging.ERROR
    logger.log(
        level,
        "Service health status changed",
        extra={
            "service_name": service_name,
            "is_healthy": is_healthy,
            "response_time_ms": response_time_ms,
            "event_type": "health_change"
        }
    )


# Global logger instance for Ghost Function Service
ghost_logger = get_ghost_logger()
