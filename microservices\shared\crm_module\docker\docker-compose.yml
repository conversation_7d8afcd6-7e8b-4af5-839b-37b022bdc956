# ============================================================================
# CRM SERVICE - Microserviço de CRM
# ============================================================================
# Porta: 8011 | Database: postgres-crm (5444) | Redis: DB 11
# Submódulos: accounts, contacts, interactions, loyalty, pricing
# ============================================================================

services:
  # ============================================================================
  # CRM SERVICE - Serviço Principal
  # ============================================================================
  trix-shared-crm:
    build:
      context: ../
      dockerfile: docker/Dockerfile
    container_name: trix-shared-crm
    ports:
      - "8011:8011"  # Corrected port mapping to match documentation
    environment:
      # Service configuration
      - SERVICE_NAME=crm-service
      - SERVICE_VERSION=2.0.0
      - SERVICE_PORT=8011
      - ENVIRONMENT=${ENVIRONMENT:-development}
      - DEBUG=${DEBUG:-false}

      # Database configuration (Citus Shared Coordinator)
      - CRM_DATABASE_URL=postgresql+asyncpg://postgres:TrixSuperSecure2024!@trix-citus-shared-coordinator:5432/crm_db
      - CRM_DB_CITUS_COORDINATOR_HOST=trix-citus-shared-coordinator
      - CRM_DB_CITUS_COORDINATOR_PORT=5432
      - CRM_DB_DATABASE_POOL_SIZE=20
      - CRM_DB_DATABASE_MAX_OVERFLOW=30

      # Messaging configuration
      - CRM_MESSAGING_KAFKA_BOOTSTRAP_SERVERS=["trix-kafka:9092"]
      - CRM_MESSAGING_RABBITMQ_URL=amqp://guest:guest@trix-rabbitmq:5672/
      - CRM_MESSAGING_REDIS_URL=redis://:${REDIS_PASSWORD}@trix-redis:6379/11

      # Security configuration
      - CRM_SECURITY_JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - CRM_SECURITY_VAULT_URL=http://trix-vault:8200
      - CRM_SECURITY_OPA_URL=http://trix-opa:8181

      # Service integrations
      - CRM_INTEGRATION_AUTH_SERVICE_URL=http://trix-core-auth:8001
      - CRM_INTEGRATION_USER_SERVICE_URL=http://trix-core-user:8002
      - CRM_INTEGRATION_TENANT_SERVICE_URL=http://trix-core-tenant:8003
      - CRM_INTEGRATION_CORE_SERVICE_URL=http://trix-core-core:8005
      - CRM_INTEGRATION_COMMERCE_SERVICE_URL=http://trix-core-commerce:8009
      - CRM_INTEGRATION_NOTIFICATION_SERVICE_URL=http://trix-core-notification:8010
      - CRM_INTEGRATION_PAYMENT_SERVICE_URL=http://trix-core-payment:8013

      # Observability configuration
      - CRM_OBSERVABILITY_PROMETHEUS_ENABLED=true
      - CRM_OBSERVABILITY_JAEGER_ENABLED=true
      - CRM_OBSERVABILITY_JAEGER_AGENT_HOST=trix-jaeger
      - CRM_OBSERVABILITY_LOG_LEVEL=INFO
      - CRM_OBSERVABILITY_LOG_FORMAT=json
      - SERVICE_VERSION=1.0.0
      - CELERY_BROKER_URL=redis://:${REDIS_PASSWORD}@trix-redis:6379/11
      - CELERY_RESULT_BACKEND=redis://:${REDIS_PASSWORD}@trix-redis:6379/11
      - LOYALTY_PROGRAM_ENABLED=true
      - DYNAMIC_PRICING_ENABLED=true
      - CHURN_PREDICTION_ENABLED=true
      - CLV_CALCULATION_ENABLED=true
    depends_on:
      - trix-redis
    external_links:
      - trix-citus-shared-coordinator:trix-citus-shared-coordinator
    volumes:
      - ../:/app
    networks:
      - trix-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8011/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    labels:
      - "trix.category=shared"
      - "trix.service=microservice"
      - "trix.module=crm"
      - "trix.port=8011"

  # ============================================================================
  # DATABASE - Using Citus Shared Coordinator (External)
  # ============================================================================
  # Database removed - using external Citus Shared Coordinator
  # Connection: trix-citus-shared-coordinator:5432/crm_db

networks:
  trix-network:
    external: true
