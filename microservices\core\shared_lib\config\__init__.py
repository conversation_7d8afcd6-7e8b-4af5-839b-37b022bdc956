"""
Shared Configuration Module for Trix Microservices
==================================================

This module provides centralized configuration classes that can be used
across all Trix microservices to ensure consistency and reduce duplication.

Key Features:
- Centralized Vault configuration
- Environment-specific settings
- Type-safe configuration with Pydantic
- Easy integration with existing services

Usage:
    from microservices.core.shared_lib.config import VaultBaseSettings
    
    class MyServiceSettings(VaultBaseSettings):
        service_name: str = "my-service"
        # Your service-specific settings here
"""

from .vault_config import (
    VaultBaseSettings,
    VaultConfig,
    get_vault_config,
    get_service_vault_path,
    get_database_vault_path,
    get_jwt_vault_path,
    ENVIRONMENT_CONFIGS
)
from .kafka_config import (
    KafkaSettings,
    KafkaTopicConfig,
    get_kafka_settings
)

# Import shared configurations
from .redis_config import (
    RedisSettings,
    get_redis_settings,
    get_service_redis_db,
    get_redis_key,
    get_redis_ttl,
    REDIS_DB_MAPPINGS,
    REDIS_KEY_PATTERNS,
    REDIS_TTL_CONFIGS
)

from .database_config import (
    DatabaseSettings,
    CitusDataSettings,
    get_database_settings,
    get_citus_settings,
    get_service_database_name,
    get_sharding_config,
    get_pool_config,
    DATABASE_MAPPINGS,
    SHARDING_CONFIGS,
    POOL_CONFIGS
)

from .rabbitmq_config import (
    RabbitMQSettings,
    get_rabbitmq_settings,
    get_exchange_config,
    get_queue_config,
    get_routing_key,
    get_service_messaging_config,
    EXCHANGE_CONFIGS,
    QUEUE_CONFIGS,
    ROUTING_KEY_PATTERNS,
    SERVICE_MESSAGING_CONFIGS
)

from .password_config import (
    PasswordSettings,
    SharedPasswordManager,
    shared_password_manager,
    get_password_settings,
    hash_password_async,
    verify_password_async,
    get_password_hash,
    verify_password,
    check_password_strength,
    generate_secure_password,
    generate_secure_token
)

from .security_config import (
    SecuritySettings,
    get_security_settings,
    get_service_security_config,
    SERVICE_SECURITY_CONFIGS
)

from .observability_config import (
    ObservabilitySettings,
    get_observability_settings,
    get_service_observability_config,
    get_metric_buckets,
    get_alert_threshold,
    get_log_level_number,
    SERVICE_OBSERVABILITY_CONFIGS,
    METRIC_BUCKETS,
    ALERT_THRESHOLDS,
    LOG_LEVELS
)

from .tenant_config import (
    TenantModuleBaseSettings,
    RestaurantModuleSettings,
    ConsultancyModuleSettings
)

from .financial_config import (
    FinancialSettings,
    FinancialConfig,
    get_financial_settings,
    get_financial_config,
    DEFAULT_FINANCIAL_CONFIG
)

# HR Module Configuration
from .hr_config import (
    HRServiceSettings,
    get_hr_settings,
    HR_EMPLOYEE_STATUS_CHOICES,
    HR_EMPLOYMENT_TYPE_CHOICES,
    HR_COURSE_DIFFICULTY_LEVELS,
    HR_PERFORMANCE_RATING_SCALE,
    HR_GAMIFICATION_CONFIG,
    HR_DEPARTMENT_TEMPLATES
)

# Media System Configuration
from .media_config import (
    MediaServiceSettings,
    MediaStorageSettings,
    MediaProcessingSettings,
    MediaAISettings,
    MediaCDNSettings,
    MediaSecuritySettings,
    media_settings
)

# Enhanced Tenant modules configuration (NEW)
try:
    from .tenant_modules_config import (
        TenantModuleBaseSettings as EnhancedTenantModuleBaseSettings,
        RestaurantModuleSettings as EnhancedRestaurantModuleSettings,
        HotelModuleSettings,
        RetailModuleSettings,
        get_tenant_module_settings
    )
    ENHANCED_TENANT_MODULES_CONFIG_AVAILABLE = True
except ImportError:
    ENHANCED_TENANT_MODULES_CONFIG_AVAILABLE = False

try:
    from .tenant_integrations import (
        TenantIntegrationManager,
        get_integration_manager,
        get_tenant_integrations_status
    )
    TENANT_INTEGRATIONS_AVAILABLE = True
except ImportError:
    TENANT_INTEGRATIONS_AVAILABLE = False

try:
    from .tenant_observability import (
        TenantObservabilityConfig,
        setup_tenant_observability,
        get_tenant_observability_status
    )
    TENANT_OBSERVABILITY_AVAILABLE = True
except ImportError:
    TENANT_OBSERVABILITY_AVAILABLE = False

try:
    from .tenant_messaging import (
        TenantMessagingConfig,
        setup_tenant_messaging,
        get_tenant_messaging_status
    )
    TENANT_MESSAGING_AVAILABLE = True
except ImportError:
    TENANT_MESSAGING_AVAILABLE = False

# Infrastructure Components
from .infrastructure.observability.logging import (
    StructuredLogger,
    LoggerMixin,
    LoggingSettings,
    get_logger,
    setup_logging,
    log_performance_metric,
    log_business_event
)

from .infrastructure.messaging.cache_client import (
    CacheClient,
    get_cache_client,
    initialize_cache
)

from .infrastructure.database.connection import (
    ConnectionManager
)

from .infrastructure.messaging.redis_client import (
    RedisClient,
    get_redis_client
)

# Import Ghost Function configurations
from .ghost_config import (
    GhostSettings,
    HibernationLevel,
    FallbackStrategy,
    get_ghost_settings,
    get_service_timeout_config,
    get_hibernation_config,
    get_service_ghost_path,
    get_critical_services,
    get_services_by_priority,
    SERVICE_TIMEOUT_CONFIGS,
    HIBERNATION_RESOURCE_CONFIGS
)

# Import Email configurations
from .email_config import (
    EmailProviderSettings,
    SMTPSettings,
    IMAPSettings,
    EmailSecuritySettings,
    EmailStorageSettings,
    EmailDeliverySettings,
    EmailTemplateSettings,
    SharedEmailSettings,
    shared_email_settings,
    get_email_settings,
    get_provider_config,
    get_smtp_config,
    get_imap_config,
    get_dns_records,
    EMAIL_PROVIDERS,
    DEFAULT_EMAIL_TEMPLATES
)

# Import infrastructure clients
try:
    from .infrastructure.messaging import (
        KafkaClient,
        RabbitMQClient,
        RedisClient
    )
    MESSAGING_CLIENTS_AVAILABLE = True
except ImportError:
    MESSAGING_CLIENTS_AVAILABLE = False

try:
    from .infrastructure.observability import (
        MetricsCollector,
        get_metrics_collector
    )
    OBSERVABILITY_AVAILABLE = True
except ImportError:
    OBSERVABILITY_AVAILABLE = False

try:
    from .infrastructure.security import (
        RateLimiter,
        EncryptionUtils,
        SessionManager
    )
    SECURITY_AVAILABLE = True
except ImportError:
    SECURITY_AVAILABLE = False

try:
    from .infrastructure.database import (
        ConnectionManager,
        ShardingUtils
    )
    DATABASE_UTILS_AVAILABLE = True
except ImportError:
    DATABASE_UTILS_AVAILABLE = False

try:
    from .utils import (
        EventSourcingUtils,
        CommonUtils
    )
    UTILS_AVAILABLE = True
except ImportError:
    UTILS_AVAILABLE = False

__all__ = [
    # Vault Configuration
    "VaultBaseSettings",
    "VaultConfig",
    "get_vault_config",
    "get_service_vault_path",
    "get_database_vault_path",
    "get_jwt_vault_path",
    "ENVIRONMENT_CONFIGS",

    # Kafka Configuration
    "KafkaSettings",
    "KafkaTopicConfig",
    "get_kafka_settings",

    # Redis Configuration
    "RedisSettings",
    "get_redis_settings",
    "get_service_redis_db",
    "get_redis_key",
    "get_redis_ttl",
    "REDIS_DB_MAPPINGS",
    "REDIS_KEY_PATTERNS",

    # Email Configuration
    "EmailProviderSettings",
    "SMTPSettings",
    "IMAPSettings",
    "EmailSecuritySettings",
    "EmailStorageSettings",
    "EmailDeliverySettings",
    "EmailTemplateSettings",
    "SharedEmailSettings",
    "shared_email_settings",
    "get_email_settings",
    "get_provider_config",
    "get_smtp_config",
    "get_imap_config",
    "get_dns_records",
    "EMAIL_PROVIDERS",
    "DEFAULT_EMAIL_TEMPLATES",
    "REDIS_TTL_CONFIGS",

    # Database Configuration
    "DatabaseSettings",
    "CitusDataSettings",
    "get_database_settings",
    "get_citus_settings",
    "get_service_database_name",
    "get_sharding_config",
    "get_pool_config",
    "DATABASE_MAPPINGS",
    "SHARDING_CONFIGS",
    "POOL_CONFIGS",

    # RabbitMQ Configuration
    "RabbitMQSettings",
    "get_rabbitmq_settings",
    "get_exchange_config",
    "get_queue_config",
    "get_routing_key",
    "get_service_messaging_config",
    "EXCHANGE_CONFIGS",
    "QUEUE_CONFIGS",
    "ROUTING_KEY_PATTERNS",
    "SERVICE_MESSAGING_CONFIGS",

    # Password Configuration
    "PasswordSettings",
    "SharedPasswordManager",
    "shared_password_manager",
    "get_password_settings",
    "hash_password_async",
    "verify_password_async",
    "get_password_hash",
    "verify_password",
    "check_password_strength",
    "generate_secure_password",
    "generate_secure_token",

    # Security Configuration
    "SecuritySettings",
    "get_security_settings",
    "get_service_security_config",
    "SERVICE_SECURITY_CONFIGS",

    # Observability Configuration
    "ObservabilitySettings",
    "get_observability_settings",
    "get_service_observability_config",
    "get_metric_buckets",
    "get_alert_threshold",
    "get_log_level_number",
    "SERVICE_OBSERVABILITY_CONFIGS",
    "METRIC_BUCKETS",
    "ALERT_THRESHOLDS",
    "LOG_LEVELS",

    # Infrastructure Components
    "StructuredLogger",
    "LoggerMixin",
    "LoggingSettings",
    "get_logger",
    "setup_logging",
    "log_performance_metric",
    "log_business_event",
    "CacheClient",
    "get_cache_client",
    "initialize_cache",
    "ConnectionManager",
    "RedisClient",
    "get_redis_client",

    # Ghost Function Configuration
    "GhostSettings",
    "HibernationLevel",
    "FallbackStrategy",
    "get_ghost_settings",
    "get_service_timeout_config",
    "get_hibernation_config",
    "get_service_ghost_path",
    "get_critical_services",
    "get_services_by_priority",

    # Enhanced Tenant Modules Configuration (NEW)
    "EnhancedTenantModuleBaseSettings",
    "EnhancedRestaurantModuleSettings",
    "HotelModuleSettings",
    "RetailModuleSettings",
    "get_tenant_module_settings",
    "TenantIntegrationManager",
    "get_integration_manager",
    "get_tenant_integrations_status",
    "TenantObservabilityConfig",
    "setup_tenant_observability",
    "get_tenant_observability_status",
    "TenantMessagingConfig",
    "setup_tenant_messaging",
    "get_tenant_messaging_status",

    # Availability flags
    "ENHANCED_TENANT_MODULES_CONFIG_AVAILABLE",
    "TENANT_INTEGRATIONS_AVAILABLE",
    "TENANT_OBSERVABILITY_AVAILABLE",
    "TENANT_MESSAGING_AVAILABLE",
    "SERVICE_TIMEOUT_CONFIGS",
    "HIBERNATION_RESOURCE_CONFIGS",

    # Tenant Configuration
    "TenantModuleBaseSettings",
    "RestaurantModuleSettings",
    "ConsultancyModuleSettings",

    # Financial Configuration
    "FinancialSettings",
    "FinancialConfig",
    "get_financial_settings",
    "get_financial_config",
    "DEFAULT_FINANCIAL_CONFIG",

    # HR Module Configuration
    "HRServiceSettings",
    "get_hr_settings",
    "HR_EMPLOYEE_STATUS_CHOICES",
    "HR_EMPLOYMENT_TYPE_CHOICES",
    "HR_COURSE_DIFFICULTY_LEVELS",
    "HR_PERFORMANCE_RATING_SCALE",
    "HR_GAMIFICATION_CONFIG",
    "HR_DEPARTMENT_TEMPLATES",

    # Media System Configuration
    "MediaServiceSettings",
    "MediaStorageSettings",
    "MediaProcessingSettings",
    "MediaAISettings",
    "MediaCDNSettings",
    "MediaSecuritySettings",
    "media_settings",
]

# Add infrastructure clients to exports if available
if MESSAGING_CLIENTS_AVAILABLE:
    __all__.extend([
        "KafkaClient",
        "RabbitMQClient",
        "RedisClient"
    ])

if OBSERVABILITY_AVAILABLE:
    __all__.extend([
        "MetricsCollector",
        "get_metrics_collector"
    ])

if SECURITY_AVAILABLE:
    __all__.extend([
        "RateLimiter",
        "EncryptionUtils",
        "SessionManager"
    ])

if DATABASE_UTILS_AVAILABLE:
    __all__.extend([
        "ConnectionManager",
        "ShardingUtils"
    ])

if UTILS_AVAILABLE:
    __all__.extend([
        "EventSourcingUtils",
        "CommonUtils"
    ])

