#!/usr/bin/env python3
"""
🔧 Script para corrigir configurações dos Core Services para Citus
Atualiza automaticamente as configurações de banco de dados dos microserviços core
"""

import os
import re
from pathlib import Path
import logging

# Configuração de logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Mapeamento dos microserviços core para seus bancos de dados
CORE_SERVICES_MAPPING = {
    'cdn_service': 'cdn_db',
    'dns_bridge_service': 'dns_bridge_db', 
    'domain_service': 'domain_db',
    'ghost_function_service': 'ghost_function_db',
    'i18n_service': 'i18n_db',
    'media_system': 'media_system_db',
    'notification_service': 'notification_db',
    'payment_service': 'payment_db',
    'supplier_service': 'supplier_db',
    'synapse_ai_service': 'synapse_ai_db'
}

def fix_docker_compose(service_name: str, database_name: str):
    """Corrige o docker-compose.yml de um microserviço"""
    docker_compose_path = Path(f"../core/{service_name}/docker/docker-compose.yml")
    
    if not docker_compose_path.exists():
        logger.warning(f"❌ Docker compose não encontrado: {docker_compose_path}")
        return False
    
    try:
        # Ler arquivo
        with open(docker_compose_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Backup original
        backup_path = docker_compose_path.with_suffix('.yml.backup')
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        # Substituições necessárias
        replacements = [
            # Database URL patterns
            (r'DATABASE_URL=postgresql\+asyncpg://[^@]+@[^:]+:\d+/\w+', 
             f'DATABASE_URL=postgresql+asyncpg://postgres:TrixSuperSecure2024!@trix-citus-core-coordinator:5432/{database_name}'),
            
            # External links para banco antigo
            (r'external_links:\s*\n\s*-\s*db:db', 
             'external_links:\n      - trix-citus-core-coordinator:trix-citus-core-coordinator'),
            
            # Depends on banco antigo
            (r'depends_on:\s*\n\s*-\s*db', 
             'depends_on:'),
            
            # Referências ao banco principal
            (r'@db:', '@trix-citus-core-coordinator:'),
            (r'@trix-postgres-primary:', '@trix-citus-core-coordinator:'),
            (r'@trix-database-cluster-primary:', '@trix-citus-core-coordinator:'),
            
            # Portas antigas
            (r':5432/trixdb', f':5432/{database_name}'),
            (r'trixuser:trixpass', 'postgres:TrixSuperSecure2024!'),
        ]
        
        # Aplicar substituições
        modified = False
        for pattern, replacement in replacements:
            if re.search(pattern, content):
                content = re.sub(pattern, replacement, content)
                modified = True
        
        # Escrever arquivo modificado
        if modified:
            with open(docker_compose_path, 'w', encoding='utf-8') as f:
                f.write(content)
            logger.info(f"✅ {service_name} atualizado")
            return True
        else:
            logger.info(f"ℹ️ {service_name} não precisou de alterações")
            return True
            
    except Exception as e:
        logger.error(f"❌ Erro ao processar {service_name}: {e}")
        return False

def fix_env_files(service_name: str, database_name: str):
    """Corrige arquivos .env se existirem"""
    env_paths = [
        Path(f"../core/{service_name}/.env"),
        Path(f"../core/{service_name}/docker/.env"),
        Path(f"../core/{service_name}/config/.env")
    ]
    
    for env_path in env_paths:
        if env_path.exists():
            try:
                with open(env_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Backup
                backup_path = env_path.with_suffix('.env.backup')
                with open(backup_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                # Substituições
                content = re.sub(r'POSTGRES_HOST=.*', 'POSTGRES_HOST=trix-citus-core-coordinator', content)
                content = re.sub(r'POSTGRES_PORT=.*', 'POSTGRES_PORT=5432', content)
                content = re.sub(r'POSTGRES_DB=.*', f'POSTGRES_DB={database_name}', content)
                content = re.sub(r'POSTGRES_USER=.*', 'POSTGRES_USER=postgres', content)
                content = re.sub(r'POSTGRES_PASSWORD=.*', 'POSTGRES_PASSWORD=TrixSuperSecure2024!', content)
                
                with open(env_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                logger.info(f"✅ {service_name} .env atualizado: {env_path}")
                
            except Exception as e:
                logger.error(f"❌ Erro ao processar .env de {service_name}: {e}")

def main():
    """Função principal"""
    logger.info("🔧 Iniciando correção dos Core Services para Citus...")
    
    success_count = 0
    total_count = len(CORE_SERVICES_MAPPING)
    
    for service_name, database_name in CORE_SERVICES_MAPPING.items():
        logger.info(f"📋 Processando {service_name} → {database_name}")
        
        # Verificar se diretório existe
        service_path = Path(f"../core/{service_name}")
        if not service_path.exists():
            logger.warning(f"⚠️ Diretório não encontrado: {service_path}")
            continue
        
        # Corrigir docker-compose
        if fix_docker_compose(service_name, database_name):
            success_count += 1
        
        # Corrigir arquivos .env
        fix_env_files(service_name, database_name)
    
    # Relatório final
    logger.info("=" * 60)
    logger.info("📊 RELATÓRIO DE CORREÇÃO DOS CORE SERVICES")
    logger.info("=" * 60)
    logger.info(f"🎯 Total de serviços: {total_count}")
    logger.info(f"✅ Corrigidos com sucesso: {success_count}")
    logger.info(f"❌ Falharam: {total_count - success_count}")
    logger.info(f"📈 Taxa de sucesso: {(success_count / total_count) * 100:.1f}%")
    
    if success_count == total_count:
        logger.info("🎉 Todos os Core Services foram corrigidos com sucesso!")
    else:
        logger.warning("⚠️ Alguns serviços não foram corrigidos. Verifique os logs acima.")
    
    logger.info("=" * 60)

if __name__ == "__main__":
    main()
