"""
Authentication Middleware for Media System
==========================================

Middleware de autenticação para o Media System, integrado com o Auth Service
e seguindo o padrão estabelecido no User Service.

✅ MIGRAÇÃO CONCLUÍDA - Usando shared_lib para configurações comuns
"""

from typing import Optional, Dict, Any
from fastapi import HTTPException, status, Depends
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
import httpx
import jwt
from datetime import datetime

from .config.settings import settings


security = HTTPBearer()


class AuthenticationError(HTTPException):
    """Erro de autenticação customizado."""
    
    def __init__(self, detail: str = "Authentication failed"):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=detail,
            headers={"WWW-Authenticate": "Bearer"}
        )


class AuthorizationError(HTTPException):
    """Erro de autorização customizado."""
    
    def __init__(self, detail: str = "Insufficient permissions"):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=detail
        )


class MediaAuthMiddleware:
    """Middleware de autenticação para o Media System."""
    
    def __init__(self):
        self.auth_service_url = settings.USER_SERVICE_URL  # Integração via User Service
        self.timeout = 30
    
    async def validate_token(self, token: str) -> Dict[str, Any]:
        """Valida token via Auth Service."""
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    f"{self.auth_service_url}/api/v1/auth/validate-token",
                    json={"token": token},
                    headers={"Content-Type": "application/json"}
                )
                
                if response.status_code == 200:
                    return response.json()
                elif response.status_code == 401:
                    raise AuthenticationError("Invalid or expired token")
                else:
                    raise AuthenticationError("Token validation failed")
                    
        except httpx.TimeoutException:
            raise AuthenticationError("Authentication service timeout")
        except httpx.RequestError:
            raise AuthenticationError("Authentication service unavailable")
    
    async def get_user_permissions(self, user_id: str, tenant_id: str) -> Dict[str, Any]:
        """Obtém permissões do usuário via User Service."""
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(
                    f"{self.auth_service_url}/api/v1/users/{user_id}/permissions",
                    params={"tenant_id": tenant_id},
                    headers={"Content-Type": "application/json"}
                )
                
                if response.status_code == 200:
                    return response.json()
                else:
                    return {"permissions": [], "roles": []}
                    
        except (httpx.TimeoutException, httpx.RequestError):
            # Em caso de erro, retorna permissões vazias
            return {"permissions": [], "roles": []}


# Instância global do middleware
auth_middleware = MediaAuthMiddleware()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> Dict[str, Any]:
    """
    Dependency para obter usuário atual autenticado.
    
    Uso:
        @app.get("/media")
        async def get_media(current_user: dict = Depends(get_current_user)):
            # usar current_user aqui
    """
    if not credentials:
        raise AuthenticationError("Missing authentication token")
    
    token = credentials.credentials
    user_data = await auth_middleware.validate_token(token)
    
    return user_data


async def get_current_active_user(
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Dependency para obter usuário ativo autenticado.
    """
    if not current_user.get("is_active", False):
        raise AuthenticationError("Inactive user")
    
    return current_user


def require_permissions(required_permissions: list):
    """
    Decorator para exigir permissões específicas.
    
    Uso:
        @app.get("/admin/media")
        @require_permissions(["media.admin"])
        async def admin_media(current_user: dict = Depends(get_current_user)):
            # endpoint protegido
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            current_user = kwargs.get("current_user")
            if not current_user:
                raise AuthenticationError("Authentication required")
            
            user_permissions = current_user.get("permissions", [])
            
            # Verificar se o usuário tem as permissões necessárias
            if not any(perm in user_permissions for perm in required_permissions):
                raise AuthorizationError(
                    f"Required permissions: {', '.join(required_permissions)}"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


# Dependency opcional para desenvolvimento (sem autenticação)
async def get_optional_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> Optional[Dict[str, Any]]:
    """
    Dependency opcional para desenvolvimento.
    Retorna None se não houver token.
    """
    if not credentials:
        return None
    
    try:
        return await get_current_user(credentials)
    except AuthenticationError:
        return None
