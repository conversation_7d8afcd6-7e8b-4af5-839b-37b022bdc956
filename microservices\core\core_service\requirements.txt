# Core FastAPI and Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic[email]==2.5.0
pydantic-settings==2.1.0
email-validator==2.1.0
python-multipart==0.0.6
python-dotenv==1.0.0
websockets==12.0
aiofiles==23.2.1

# Database and ORM
sqlalchemy==2.0.23
alembic==1.12.1
asyncpg==0.29.0
psycopg2-binary==2.9.9

# Caching and Session Management
redis==5.0.1
aioredis==2.0.1

# Background Tasks
celery==5.3.4

# Security and Authentication
python-jose[cryptography]==3.3.0
PyJWT==2.8.0
passlib[bcrypt]==1.7.4
cryptography==41.0.7
hvac==2.0.0

# HTTP Client
httpx==0.25.2

# Event-Driven Architecture - Messaging
aiokafka==0.12.0
kafka-python==2.0.2
aio-pika==9.3.1

# Observability - Metrics
prometheus-client==0.19.0

# Observability - Distributed Tracing
opentelemetry-api==1.21.0
opentelemetry-sdk==1.21.0
opentelemetry-exporter-jaeger-thrift==1.21.0
opentelemetry-instrumentation-fastapi==0.42b0
opentelemetry-instrumentation-sqlalchemy==0.42b0
opentelemetry-instrumentation-redis==0.42b0
opentelemetry-instrumentation-kafka-python==0.42b0

# Observability - Structured Logging
structlog==23.2.0

# Data Processing and Utilities
python-dateutil==2.8.2
pytz==2023.3
pillow==10.1.0
