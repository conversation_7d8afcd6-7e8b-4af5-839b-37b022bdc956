"""
Configuration for Email module.

🔗 MIGRAÇÃO PARA SHARED_LIB: Este arquivo foi refatorado para usar
configurações compartilhadas da shared_lib. Configurações comuns de email
foram movidas para microservices/core/shared_lib/config/email_config.py

Este arquivo agora serve como uma ponte entre as configurações compartilhadas
e as configurações específicas do Email Service.
"""

import os
from typing import Dict, Any

# ✅ NOVO: Importando configurações compartilhadas da shared_lib
try:
    from microservices.core.shared_lib.config.email_config import (
        shared_email_settings,
        get_smtp_config,
        get_imap_config,
        get_dns_records,
        get_provider_config
    )
    SHARED_CONFIG_AVAILABLE = True
except ImportError:
    # Fallback para desenvolvimento local
    SHARED_CONFIG_AVAILABLE = False
    print("⚠️  Shared config not available, using legacy config")

# ✅ MIGRADO: Configurações movidas para shared_lib
# Use as funções get_*_config() ou shared_email_settings diretamente

# Configurações específicas do Email Service (não compartilhadas)
EMAIL_SERVICE_SPECIFIC_CONFIG = {
    "webmail_enabled": os.getenv("WEBMAIL_ENABLED", "true").lower() == "true",
    "financial_integration_enabled": os.getenv("FINANCIAL_INTEGRATION_ENABLED", "true").lower() == "true",
    "auto_invoice_processing": os.getenv("AUTO_INVOICE_PROCESSING", "true").lower() == "true",
    "email_archiving_enabled": os.getenv("EMAIL_ARCHIVING_ENABLED", "true").lower() == "true",
}

# ✅ FALLBACK: Configurações legadas para compatibilidade (serão removidas)
if not SHARED_CONFIG_AVAILABLE:
    # SMTP configuration (LEGACY - use shared_lib instead)
    SMTP_HOST = os.getenv("SMTP_HOST", "localhost")
    SMTP_PORT = int(os.getenv("SMTP_PORT", "587"))
    SMTP_USE_TLS = os.getenv("SMTP_USE_TLS", "True").lower() in ("true", "1", "t", "yes")
    SMTP_USERNAME = os.getenv("SMTP_USERNAME", "")
    SMTP_PASSWORD = os.getenv("SMTP_PASSWORD", "")

    # IMAP configuration (LEGACY - use shared_lib instead)
    IMAP_HOST = os.getenv("IMAP_HOST", "localhost")
    IMAP_PORT = int(os.getenv("IMAP_PORT", "143"))
    IMAP_USE_SSL = os.getenv("IMAP_USE_SSL", "False").lower() in ("true", "1", "t", "yes")

    # Maildir configuration (LEGACY - use shared_lib instead)
    MAILDIR_BASE_PATH = os.getenv("MAILDIR_BASE_PATH", "/var/vmail")

    # Quota configuration (LEGACY - use shared_lib instead)
    DEFAULT_QUOTA_MB = int(os.getenv("DEFAULT_QUOTA_MB", "1024"))  # 1GB

    # DKIM configuration (LEGACY - use shared_lib instead)
    DKIM_SELECTOR = os.getenv("DKIM_SELECTOR", "mail")
    DKIM_PRIVATE_KEY_PATH = os.getenv("DKIM_PRIVATE_KEY_PATH", "/etc/opendkim/keys")

    # DNS configuration (LEGACY - use shared_lib instead)
    DEFAULT_MX_RECORDS = [
        os.getenv("PRIMARY_MX_RECORD", "10 mail.{domain}"),
        os.getenv("SECONDARY_MX_RECORD", "20 mail2.{domain}"),
    ]
    DEFAULT_SPF_RECORD = os.getenv("DEFAULT_SPF_RECORD", "v=spf1 mx a:{domain} ~all")
    DEFAULT_DMARC_RECORD = os.getenv(
        "DEFAULT_DMARC_RECORD", "v=DMARC1; p=none; sp=none; rua=mailto:dmarc@{domain}"
    )

    # Email server configuration (LEGACY - use shared_lib instead)
    EMAIL_SERVER_HOSTNAME = os.getenv("EMAIL_SERVER_HOSTNAME", "mail.example.com")


def get_config() -> Dict[str, Any]:
    """
    Get the configuration as a dictionary.

    ✅ NOVO: Agora usa configurações da shared_lib quando disponível,
    com fallback para configurações legadas.
    """
    if SHARED_CONFIG_AVAILABLE:
        # ✅ NOVO: Usando configurações da shared_lib
        smtp_config = get_smtp_config()
        imap_config = get_imap_config()

        return {
            "smtp": smtp_config,
            "imap": imap_config,
            "maildir": {
                "base_path": shared_email_settings.storage.maildir_base_path,
            },
            "quota": {
                "default_quota_mb": shared_email_settings.storage.default_quota_mb,
            },
            "dkim": {
                "selector": shared_email_settings.security.dkim_selector,
                "private_key_path": shared_email_settings.security.dkim_private_key_path,
            },
            "dns": {
                "mx_records": shared_email_settings.security.default_mx_records,
                "spf_record": shared_email_settings.security.default_spf_record,
                "dmarc_record": shared_email_settings.security.default_dmarc_record,
            },
            "server": {
                "hostname": shared_email_settings.security.email_server_hostname,
            },
            "providers": {
                "default": shared_email_settings.providers.default_provider,
                "sendgrid": get_provider_config("sendgrid"),
                "ses": get_provider_config("ses"),
                "mailgun": get_provider_config("mailgun"),
                "smtp": get_provider_config("smtp"),
            },
            "delivery": {
                "max_recipients": shared_email_settings.delivery.max_recipients_per_email,
                "batch_size": shared_email_settings.delivery.email_queue_batch_size,
                "retry_attempts": shared_email_settings.delivery.email_retry_attempts,
                "retry_delay": shared_email_settings.delivery.email_retry_delay,
            },
            "templates": {
                "default_language": shared_email_settings.templates.default_template_language,
                "cache_ttl": shared_email_settings.templates.template_cache_ttl,
                "enable_preview": shared_email_settings.templates.enable_template_preview,
            },
            "storage": {
                "upload_dir": shared_email_settings.storage.upload_dir,
                "max_file_size": shared_email_settings.storage.max_file_size,
                "template_dir": shared_email_settings.storage.template_storage_dir,
                "attachment_dir": shared_email_settings.storage.attachment_storage_dir,
            },
            "email_service_specific": EMAIL_SERVICE_SPECIFIC_CONFIG,
        }
    else:
        # ✅ FALLBACK: Configurações legadas para compatibilidade
        return {
            "smtp": {
                "host": SMTP_HOST,
                "port": SMTP_PORT,
                "use_tls": SMTP_USE_TLS,
                "username": SMTP_USERNAME,
                "password": SMTP_PASSWORD,
            },
            "imap": {
                "host": IMAP_HOST,
                "port": IMAP_PORT,
                "use_ssl": IMAP_USE_SSL,
            },
            "maildir": {
                "base_path": MAILDIR_BASE_PATH,
            },
            "quota": {
                "default_quota_mb": DEFAULT_QUOTA_MB,
            },
            "dkim": {
                "selector": DKIM_SELECTOR,
                "private_key_path": DKIM_PRIVATE_KEY_PATH,
            },
            "dns": {
                "mx_records": DEFAULT_MX_RECORDS,
                "spf_record": DEFAULT_SPF_RECORD,
                "dmarc_record": DEFAULT_DMARC_RECORD,
            },
            "server": {
                "hostname": EMAIL_SERVER_HOSTNAME,
            },
            "email_service_specific": EMAIL_SERVICE_SPECIFIC_CONFIG,
        }


def get_smtp_configuration() -> Dict[str, Any]:
    """Get SMTP configuration from shared_lib or fallback."""
    if SHARED_CONFIG_AVAILABLE:
        return get_smtp_config()
    else:
        return {
            "host": SMTP_HOST,
            "port": SMTP_PORT,
            "use_tls": SMTP_USE_TLS,
            "username": SMTP_USERNAME,
            "password": SMTP_PASSWORD,
        }


def get_imap_configuration() -> Dict[str, Any]:
    """Get IMAP configuration from shared_lib or fallback."""
    if SHARED_CONFIG_AVAILABLE:
        return get_imap_config()
    else:
        return {
            "host": IMAP_HOST,
            "port": IMAP_PORT,
            "use_ssl": IMAP_USE_SSL,
        }


def get_dns_configuration(domain: str) -> Dict[str, Any]:
    """Get DNS configuration for a domain from shared_lib or fallback."""
    if SHARED_CONFIG_AVAILABLE:
        return get_dns_records(domain)
    else:
        return {
            "mx_records": [record.format(domain=domain) for record in DEFAULT_MX_RECORDS],
            "spf_record": DEFAULT_SPF_RECORD.format(domain=domain),
            "dmarc_record": DEFAULT_DMARC_RECORD.format(domain=domain),
        }


def get_provider_configuration(provider: str) -> Dict[str, Any]:
    """Get email provider configuration from shared_lib or fallback."""
    if SHARED_CONFIG_AVAILABLE:
        return get_provider_config(provider)
    else:
        # Fallback configurations
        fallback_configs = {
            "sendgrid": {
                "api_key": os.getenv("SENDGRID_API_KEY"),
                "from_email": os.getenv("SENDGRID_FROM_EMAIL", "<EMAIL>"),
                "from_name": os.getenv("SENDGRID_FROM_NAME", "Trix Platform"),
            },
            "ses": {
                "access_key_id": os.getenv("AWS_ACCESS_KEY_ID"),
                "secret_access_key": os.getenv("AWS_SECRET_ACCESS_KEY"),
                "region": os.getenv("AWS_REGION", "us-east-1"),
                "from_email": os.getenv("AWS_SES_FROM_EMAIL", "<EMAIL>"),
            },
            "mailgun": {
                "api_key": os.getenv("MAILGUN_API_KEY"),
                "domain": os.getenv("MAILGUN_DOMAIN"),
                "from_email": os.getenv("MAILGUN_FROM_EMAIL", "<EMAIL>"),
            },
            "smtp": get_smtp_configuration(),
        }
        return fallback_configs.get(provider, {})
