# ============================================================================
# CITUS TENANT COORDINATOR CONFIGURATION
# Tenant Services: restaurant, consultancy
# ============================================================================

# Citus Configuration
shared_preload_libraries = 'citus'
citus.shard_count = 64
citus.shard_replication_factor = 2
citus.use_secondary_nodes = 'always'
citus.cluster_name = 'trix-tenant'

# Optimizations for tenant partitioning
citus.enable_repartition_joins = on
citus.enable_fast_path_router_planner = on
citus.multi_shard_modify_mode = 'parallel'

# Connection Settings
max_connections = 300
superuser_reserved_connections = 3

# Memory Settings
shared_buffers = 512MB
effective_cache_size = 2GB
work_mem = 8MB
maintenance_work_mem = 128MB

# WAL Settings
wal_level = replica
max_wal_senders = 10
max_replication_slots = 10
wal_keep_size = 1GB

# Performance Settings
checkpoint_completion_target = 0.9
random_page_cost = 1.1

# Logging
log_destination = 'stderr'
logging_collector = on
log_directory = 'log'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_min_messages = info
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
log_statement = 'mod'
log_duration = on
log_min_duration_statement = 1000

# Autovacuum
autovacuum = on
autovacuum_max_workers = 4
autovacuum_naptime = 1min

# Statistics
track_activities = on
track_counts = on
track_io_timing = on
track_functions = all

# Locale
lc_messages = 'en_US.utf8'
lc_monetary = 'en_US.utf8'
lc_numeric = 'en_US.utf8'
lc_time = 'en_US.utf8'
default_text_search_config = 'pg_catalog.english'
