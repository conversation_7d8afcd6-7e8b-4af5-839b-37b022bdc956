"""
Database configuration for Auth Service with Citus Data support.
"""

import asyncio
from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.pool import NullPool
from sqlalchemy import event, text

from .settings import settings

# Create async engine with Citus Data optimization
engine = create_async_engine(
    settings.database_config.get_database_url(async_driver=True, service_name="auth"),
    echo=settings.debug,
    pool_size=settings.database_config.DB_POOL_SIZE,
    max_overflow=settings.database_config.DB_MAX_OVERFLOW,
    pool_timeout=settings.database_config.DB_POOL_TIMEOUT,
    pool_recycle=settings.database_config.DB_POOL_RECYCLE,
    pool_pre_ping=True,
    # Citus Data specific optimizations
    connect_args={
        "server_settings": {
            "application_name": f"{settings.service_name}-{settings.service_version}",
            "jit": "off",  # Disable JIT for better performance with Citus
        }
    }
)

# Create async session factory
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autoflush=False,
    autocommit=False
)

def get_database_url() -> str:
    """Get the database URL."""
    return settings.database_config.get_database_url(async_driver=True)

def create_engine():
    """Create and return the database engine."""
    return engine

async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency to get database session.
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()

# Event listeners for database optimization
# Note: Citus Data settings removed for compatibility with async engine
# These can be set at the database level if using Citus Data

async def init_database():
    """
    Initialize database connection and verify Citus Data setup.
    """
    async with engine.begin() as conn:
        # Verify Citus Data extension
        result = await conn.execute(
            text("SELECT EXISTS(SELECT 1 FROM pg_extension WHERE extname = 'citus')")
        )
        citus_enabled = result.scalar()
        
        if not citus_enabled:
            print("WARNING: Citus Data extension not found. Running in single-node mode.")
        else:
            print("INFO: Citus Data extension detected. Multi-node mode enabled.")
            
            # Verify distributed tables
            result = await conn.execute(
                text("""
                SELECT table_name
                FROM citus_tables
                WHERE schema_name = 'public'
                """)
            )
            distributed_tables = result.fetchall()
            print(f"INFO: Found {len(distributed_tables)} distributed tables")

async def close_database():
    """
    Close database connections.
    """
    await engine.dispose()
