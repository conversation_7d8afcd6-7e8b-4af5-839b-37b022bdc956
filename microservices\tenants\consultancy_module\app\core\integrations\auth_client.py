"""
Consultancy Service Auth Integration
===================================
Specialized Auth Service integration for consultancy operations.
"""

from typing import Dict, Any, List, Optional
from .service_clients import get_auth_client


class ConsultancyAuthIntegration:
    """Consultancy-specific Auth Service integration."""
    
    def __init__(self):
        self.auth_client = get_auth_client()
    
    async def validate_consultant_token(self, token: str) -> Dict[str, Any]:
        """Validate consultant JWT token."""
        result = await self.auth_client.validate_token(token)
        
        # Add consultancy-specific validation
        if result.get("valid"):
            user_data = result.get("user", {})
            # Check if user has consultancy access
            if not self._has_consultancy_access(user_data):
                result["valid"] = False
                result["error"] = "User does not have consultancy access"
        
        return result
    
    async def check_project_permissions(self, user_id: str, project_id: str, action: str) -> bool:
        """Check if user has permissions for project action."""
        permissions = [f"consultancy.project.{action}"]
        
        # Add project-specific permissions
        permissions.append(f"consultancy.project.{project_id}.{action}")
        
        result = await self.auth_client.check_permissions(user_id, permissions)
        return result.get("has_permission", False)
    
    async def check_client_permissions(self, user_id: str, client_id: str, action: str) -> bool:
        """Check if user has permissions for client action."""
        permissions = [
            f"consultancy.client.{action}",
            f"consultancy.client.{client_id}.{action}"
        ]
        
        result = await self.auth_client.check_permissions(user_id, permissions)
        return result.get("has_permission", False)
    
    async def check_billing_permissions(self, user_id: str, action: str) -> bool:
        """Check if user has billing permissions."""
        permissions = [f"consultancy.billing.{action}"]
        
        result = await self.auth_client.check_permissions(user_id, permissions)
        return result.get("has_permission", False)
    
    async def check_compliance_permissions(self, user_id: str, action: str) -> bool:
        """Check if user has compliance permissions."""
        permissions = [f"consultancy.compliance.{action}"]
        
        result = await self.auth_client.check_permissions(user_id, permissions)
        return result.get("has_permission", False)
    
    def _has_consultancy_access(self, user_data: Dict[str, Any]) -> bool:
        """Check if user has consultancy access."""
        # Check user roles and associations
        roles = user_data.get("roles", [])
        associations = user_data.get("tenant_associations", [])
        
        # Admin always has access
        if "admin" in roles:
            return True
        
        # Check for consultancy tenant associations
        for association in associations:
            if association.get("tenant_type") == "consultancy":
                return True
        
        return False


# Global consultancy auth integration instance
consultancy_auth = ConsultancyAuthIntegration()


def get_consultancy_auth() -> ConsultancyAuthIntegration:
    """Get consultancy auth integration."""
    return consultancy_auth
