services:
  # ============================================================================
  # CITUS CORE COORDINATOR - Core Services Database
  # ============================================================================
  trix-citus-core-coordinator:
    image: citusdata/citus:12.1
    container_name: trix-citus-core-coordinator
    hostname: trix-citus-core-coordinator
    restart: unless-stopped
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: TrixSuperSecure2024!
      CITUS_HOST: trix-citus-core-coordinator
      CITUS_PORT: 5432
      PGUSER: postgres
      PGPASSWORD: TrixSuperSecure2024!
    ports:
      - "5432:5432"
    volumes:
      - citus_core_coordinator_data:/var/lib/postgresql/data
      - ./config/citus-core.conf:/etc/postgresql/postgresql.conf:ro
      - ./config/pg_hba.conf:/etc/postgresql/pg_hba.conf:ro
      - ./init-scripts/core:/docker-entrypoint-initdb.d:ro
      - ./backups:/backups
    command: >
      postgres
      -c shared_preload_libraries=citus
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c wal_level=replica
      -c max_wal_senders=10
      -c max_replication_slots=10
      -c citus.shard_count=32
      -c citus.shard_replication_factor=2
    networks:
      - trix-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d postgres"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    labels:
      - "trix.service=citus-core-coordinator"
      - "trix.cluster=citus"
      - "trix.role=coordinator"
      - "trix.domain=core"
      - "trix.microservices=auth,user,tenant,core,i18n,ghost_function,cdn,dns_bridge,domain,notification,payment,supplier,synapse_ai"

  # ============================================================================
  # CITUS SHARED COORDINATOR - Shared Services Database
  # ============================================================================
  trix-citus-shared-coordinator:
    image: citusdata/citus:12.1
    container_name: trix-citus-shared-coordinator
    hostname: trix-citus-shared-coordinator
    restart: unless-stopped
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: TrixSuperSecure2024!
      CITUS_HOST: trix-citus-shared-coordinator
      CITUS_PORT: 5432
      PGUSER: postgres
      PGPASSWORD: TrixSuperSecure2024!
    ports:
      - "5433:5432"
    volumes:
      - citus_shared_coordinator_data:/var/lib/postgresql/data
      - ./config/citus-shared.conf:/etc/postgresql/postgresql.conf:ro
      - ./config/pg_hba.conf:/etc/postgresql/pg_hba.conf:ro
      - ./init-scripts/shared:/docker-entrypoint-initdb.d:ro
      - ./backups:/backups
    command: >
      postgres
      -c shared_preload_libraries=citus
      -c max_connections=250
      -c shared_buffers=384MB
      -c effective_cache_size=1.5GB
      -c wal_level=replica
      -c max_wal_senders=10
      -c max_replication_slots=10
      -c citus.shard_count=32
      -c citus.shard_replication_factor=2
    networks:
      - trix-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d postgres"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    labels:
      - "trix.service=citus-shared-coordinator"
      - "trix.cluster=citus"
      - "trix.role=coordinator"
      - "trix.domain=shared"
      - "trix.microservices=crm,email,financial,hr"

  # ============================================================================
  # CITUS TENANT COORDINATOR - Tenant Services Database
  # ============================================================================
  trix-citus-tenant-coordinator:
    image: citusdata/citus:12.1
    container_name: trix-citus-tenant-coordinator
    hostname: trix-citus-tenant-coordinator
    restart: unless-stopped
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: TrixSuperSecure2024!
      CITUS_HOST: trix-citus-tenant-coordinator
      CITUS_PORT: 5432
      PGUSER: postgres
      PGPASSWORD: TrixSuperSecure2024!
    ports:
      - "5434:5432"
    volumes:
      - citus_tenant_coordinator_data:/var/lib/postgresql/data
      - ./config/citus-tenant.conf:/etc/postgresql/postgresql.conf:ro
      - ./config/pg_hba.conf:/etc/postgresql/pg_hba.conf:ro
      - ./init-scripts/tenant:/docker-entrypoint-initdb.d:ro
      - ./backups:/backups
    command: >
      postgres
      -c shared_preload_libraries=citus
      -c max_connections=300
      -c shared_buffers=512MB
      -c effective_cache_size=2GB
      -c wal_level=replica
      -c max_wal_senders=10
      -c max_replication_slots=10
      -c citus.shard_count=64
      -c citus.shard_replication_factor=2
      -c citus.enable_repartition_joins=on
    networks:
      - trix-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d postgres"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    labels:
      - "trix.service=citus-tenant-coordinator"
      - "trix.cluster=citus"
      - "trix.role=coordinator"
      - "trix.domain=tenant"
      - "trix.microservices=restaurant,consultancy"

  # ============================================================================
  # CITUS WORKERS - Shared Workers for all Coordinators
  # ============================================================================
  trix-citus-worker-1:
    image: citusdata/citus:12.1
    container_name: trix-citus-worker-1
    hostname: trix-citus-worker-1
    restart: unless-stopped
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: TrixSuperSecure2024!
      PGUSER: postgres
      PGPASSWORD: TrixSuperSecure2024!
    ports:
      - "5435:5432"
    volumes:
      - citus_worker1_data:/var/lib/postgresql/data
      - ./config/citus-worker.conf:/etc/postgresql/postgresql.conf:ro
      - ./config/pg_hba.conf:/etc/postgresql/pg_hba.conf:ro
      - ./backups:/backups
    command: >
      postgres
      -c shared_preload_libraries=citus
      -c max_connections=100
      -c shared_buffers=512MB
      -c effective_cache_size=2GB
      -c wal_level=replica
    networks:
      - trix-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d postgres"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    labels:
      - "trix.service=citus-worker"
      - "trix.cluster=citus"
      - "trix.role=worker"
      - "trix.worker.id=1"

  trix-citus-worker-2:
    image: citusdata/citus:12.1
    container_name: trix-citus-worker-2
    hostname: trix-citus-worker-2
    restart: unless-stopped
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: TrixSuperSecure2024!
      PGUSER: postgres
      PGPASSWORD: TrixSuperSecure2024!
    ports:
      - "5436:5432"
    volumes:
      - citus_worker2_data:/var/lib/postgresql/data
      - ./config/citus-worker.conf:/etc/postgresql/postgresql.conf:ro
      - ./config/pg_hba.conf:/etc/postgresql/pg_hba.conf:ro
      - ./backups:/backups
    command: >
      postgres
      -c shared_preload_libraries=citus
      -c max_connections=100
      -c shared_buffers=512MB
      -c effective_cache_size=2GB
      -c wal_level=replica
    networks:
      - trix-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d postgres"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    labels:
      - "trix.service=citus-worker"
      - "trix.cluster=citus"
      - "trix.role=worker"
      - "trix.worker.id=2"

  # ============================================================================
  # PGADMIN - DATABASE ADMINISTRATION FOR CITUS
  # ============================================================================
  trix-citus-pgadmin:
    image: dpage/pgadmin4:latest
    container_name: trix-citus-pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: ${PGADMIN_EMAIL:-<EMAIL>}
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_PASSWORD:-TrixAdmin2024!}
      PGADMIN_CONFIG_SERVER_MODE: 'False'
      PGADMIN_CONFIG_MASTER_PASSWORD_REQUIRED: 'False'
    ports:
      - "8081:80"
    volumes:
      - citus_pgadmin_data:/var/lib/pgadmin
    depends_on:
      trix-citus-core-coordinator:
        condition: service_healthy
      trix-citus-shared-coordinator:
        condition: service_healthy
      trix-citus-tenant-coordinator:
        condition: service_healthy
    networks:
      - trix-network
    labels:
      - "trix.service=citus-pgadmin"
      - "trix.cluster=citus"
      - "trix.role=admin"

# ============================================================================
# NETWORKS
# ============================================================================
networks:
  trix-network:
    external: true

# ============================================================================
# VOLUMES - CITUS CLUSTER
# ============================================================================
volumes:
  citus_core_coordinator_data:
    driver: local
    name: trix_citus_core_coordinator_data
  citus_shared_coordinator_data:
    driver: local
    name: trix_citus_shared_coordinator_data
  citus_tenant_coordinator_data:
    driver: local
    name: trix_citus_tenant_coordinator_data
  citus_worker1_data:
    driver: local
    name: trix_citus_worker1_data
  citus_worker2_data:
    driver: local
    name: trix_citus_worker2_data
  citus_pgadmin_data:
    driver: local
    name: trix_citus_pgadmin_data
