"""
Enterprise Settings Configuration - Domain Service v2.0.0
Supports Citus Data, Vault, Service Mesh, and Event-Driven Architecture
"""

from pydantic import Field
from pydantic_settings import BaseSettings
from typing import List, Optional, Dict, Any
import os

class DatabaseSettings(BaseSettings):
    """Citus Data distributed database configuration."""
    
    # Primary connection (Citus Coordinator)
    database_url: str = Field(
        default="postgresql+asyncpg://domains_user:domains_pass@trix-citus-coordinator:5432/domains_db",
        env="DATABASE_URL"
    )
    
    # Citus Data specific settings
    shard_count: int = Field(default=32, env="DATABASE_SHARD_COUNT")
    replication_factor: int = Field(default=2, env="DATABASE_REPLICATION_FACTOR")
    
    # PgBouncer connection pooling
    pgbouncer_url: str = Field(
        default="******************************************************************/domains_db",
        env="PGBOUNCER_URL"
    )
    
    # Connection pool settings
    pool_size: int = Field(default=20, env="DATABASE_POOL_SIZE")
    max_overflow: int = Field(default=30, env="DATABASE_MAX_OVERFLOW")
    pool_timeout: int = Field(default=30, env="DATABASE_POOL_TIMEOUT")
    pool_recycle: int = Field(default=3600, env="DATABASE_POOL_RECYCLE")
    
    # Query settings
    echo: bool = Field(default=False, env="DATABASE_ECHO")
    echo_pool: bool = Field(default=False, env="DATABASE_ECHO_POOL")
    
    class Config:
        env_prefix = "DATABASE_"

class MessagingSettings(BaseSettings):
    """Event-driven messaging configuration."""
    
    # Apache Kafka
    kafka_bootstrap_servers: str = Field(
        default="trix-kafka-1:9092,trix-kafka-2:9092,trix-kafka-3:9092",
        env="KAFKA_BOOTSTRAP_SERVERS"
    )
    kafka_topic_domains: str = Field(default="domain-events", env="KAFKA_TOPIC_DOMAINS")
    kafka_topic_dns: str = Field(default="dns-events", env="KAFKA_TOPIC_DNS")
    kafka_topic_ssl: str = Field(default="ssl-events", env="KAFKA_TOPIC_SSL")
    
    # RabbitMQ
    rabbitmq_url: str = Field(
        default="amqp://domains_user:domains_pass@trix-rabbitmq-cluster:5672/",
        env="RABBITMQ_URL"
    )
    rabbitmq_exchange: str = Field(default="domain-exchange", env="RABBITMQ_EXCHANGE")
    
    # Redis Streams
    redis_url: str = Field(
        default="redis://:redis_pass@trix-redis-cluster:6379/15",
        env="REDIS_URL"
    # Configurações do Vault herdadas de StandardVaultConfig
    # VAULT_URL, VAULT_TOKEN, VAULT_ENABLED, etc. já estão definidas
    
    )
    redis_cluster_nodes: str = Field(
        default="trix-redis-1:6379,trix-redis-2:6379,trix-redis-3:6379",
        env="REDIS_CLUSTER_NODES"
    )
    
    class Config:
        env_prefix = "MESSAGING_"

class SecuritySettings(BaseSettings):
    """Security and authentication configuration."""    vault_addr: str = Field(default="https://trix-vault:8200", env="VAULT_ADDR")
    vault_token: str = Field(default="", env="VAULT_TOKEN")
    vault_mount_path: str = Field(default="secret/domain-service", env="VAULT_MOUNT_PATH")
    vault_role: str = Field(default="domain-service-role", env="VAULT_ROLE")
    
    # JWT Settings
    secret_key: str = Field(default="", env="SECRET_KEY")
    algorithm: str = Field(default="HS256", env="ALGORITHM")
    access_token_expire_minutes: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    
    # CORS
    cors_origins: List[str] = Field(
        default=["https://*.trix.com", "https://trix.com"],
        env="CORS_ORIGINS"
    )
    cors_allow_credentials: bool = Field(default=True, env="CORS_ALLOW_CREDENTIALS")
    cors_allow_methods: List[str] = Field(default=["*"], env="CORS_ALLOW_METHODS")
    cors_allow_headers: List[str] = Field(default=["*"], env="CORS_ALLOW_HEADERS")
    
    # Security headers
    security_headers_enabled: bool = Field(default=True, env="SECURITY_HEADERS_ENABLED")
    csrf_protection_enabled: bool = Field(default=True, env="CSRF_PROTECTION_ENABLED")
    xss_protection_enabled: bool = Field(default=True, env="XSS_PROTECTION_ENABLED")
    
    class Config:
        env_prefix = "SECURITY_"

class ServiceMeshSettings(BaseSettings):
    """Istio/Linkerd service mesh configuration."""
    
    service_mesh_enabled: bool = Field(default=True, env="SERVICE_MESH_ENABLED")
    mtls_enabled: bool = Field(default=True, env="MTLS_ENABLED")
    istio_proxy_config: str = Field(default="/etc/istio/proxy", env="ISTIO_PROXY_CONFIG")
    
    # Service discovery
    auth_service_url: str = Field(
        default="http://trix-core-auth.trix-system.svc.cluster.local:8001",
        env="AUTH_SERVICE_URL"
    )
    user_service_url: str = Field(
        default="http://trix-core-user.trix-system.svc.cluster.local:8002",
        env="USER_SERVICE_URL"
    )
    tenant_service_url: str = Field(
        default="http://trix-core-tenant.trix-system.svc.cluster.local:8003",
        env="TENANT_SERVICE_URL"
    )
    core_service_url: str = Field(
        default="http://trix-core-core.trix-system.svc.cluster.local:8004",
        env="CORE_SERVICE_URL"
    )
    supplier_service_url: str = Field(
        default="http://trix-core-supplier.trix-system.svc.cluster.local:8005",
        env="SUPPLIER_SERVICE_URL"
    )
    i18n_service_url: str = Field(
        default="http://trix-core-i18n.trix-system.svc.cluster.local:8006",
        env="I18N_SERVICE_URL"
    )
    notification_service_url: str = Field(
        default="http://trix-core-notification.trix-system.svc.cluster.local:8007",
        env="NOTIFICATION_SERVICE_URL"
    )
    commerce_service_url: str = Field(
        default="http://trix-core-commerce.trix-system.svc.cluster.local:8008",
        env="COMMERCE_SERVICE_URL"
    )
    cdn_service_url: str = Field(
        default="http://trix-core-cdn.trix-system.svc.cluster.local:8009",
        env="CDN_SERVICE_URL"
    )
    media_service_url: str = Field(
        default="http://trix-core-media.trix-system.svc.cluster.local:8010",
        env="MEDIA_SERVICE_URL"
    )
    payment_service_url: str = Field(
        default="http://trix-core-payment.trix-system.svc.cluster.local:8011",
        env="PAYMENT_SERVICE_URL"
    )
    
    class Config:
        env_prefix = "SERVICE_MESH_"

class ObservabilitySettings(BaseSettings):
    """Monitoring and observability configuration."""
    
    # Prometheus
    prometheus_endpoint: str = Field(
        default="http://trix-prometheus:9090",
        env="PROMETHEUS_ENDPOINT"
    )
    metrics_enabled: bool = Field(default=True, env="METRICS_ENABLED")
    metrics_port: int = Field(default=9090, env="METRICS_PORT")
    
    # Jaeger distributed tracing
    jaeger_agent_host: str = Field(default="trix-jaeger-agent", env="JAEGER_AGENT_HOST")
    jaeger_agent_port: int = Field(default=6831, env="JAEGER_AGENT_PORT")
    tracing_enabled: bool = Field(default=True, env="TRACING_ENABLED")
    
    # ELK Stack
    elasticsearch_url: str = Field(
        default="https://trix-elasticsearch:9200",
        env="ELASTICSEARCH_URL"
    )
    
    # Grafana
    grafana_url: str = Field(default="http://trix-grafana:3000", env="GRAFANA_URL")
    
    class Config:
        env_prefix = "OBSERVABILITY_"

class DomainSettings(BaseSettings):
    """Domain-specific configuration."""
    
    # DNS Management (PowerDNS)
    powerdns_api_url: str = Field(default="http://trix-powerdns:8081", env="POWERDNS_API_URL")
    powerdns_api_key_path: str = Field(
        default="secret/dns/powerdns/api_key",
        env="POWERDNS_API_KEY_PATH"
    )
    powerdns_zones_path: str = Field(
        default="/etc/powerdns/zones",
        env="POWERDNS_ZONES_PATH"
    )
    dns_propagation_check_interval: int = Field(default=30, env="DNS_PROPAGATION_CHECK_INTERVAL")
    dns_health_check_interval: int = Field(default=60, env="DNS_HEALTH_CHECK_INTERVAL")
    
    # SSL/TLS Management
    letsencrypt_enabled: bool = Field(default=True, env="LETSENCRYPT_ENABLED")
    letsencrypt_email: str = Field(default="<EMAIL>", env="LETSENCRYPT_EMAIL")
    letsencrypt_staging: bool = Field(default=False, env="LETSENCRYPT_STAGING")
    ssl_auto_renewal: bool = Field(default=True, env="SSL_AUTO_RENEWAL")
    ssl_renewal_days_before: int = Field(default=30, env="SSL_RENEWAL_DAYS_BEFORE")
    ssl_certificate_storage: str = Field(default="vault", env="SSL_CERTIFICATE_STORAGE")
    certificate_transparency_enabled: bool = Field(
        default=True,
        env="CERTIFICATE_TRANSPARENCY_ENABLED"
    )
    
    # CDN Integration (Varnish + MinIO)
    varnish_admin_url: str = Field(default="http://trix-varnish:6082", env="VARNISH_ADMIN_URL")
    varnish_config_path: str = Field(
        default="/etc/varnish/domains",
        env="VARNISH_CONFIG_PATH"
    )
    minio_endpoint: str = Field(default="http://trix-minio:9000", env="MINIO_ENDPOINT")
    minio_access_key_path: str = Field(
        default="secret/cdn/minio/access_key",
        env="MINIO_ACCESS_KEY_PATH"
    )
    minio_secret_key_path: str = Field(
        default="secret/cdn/minio/secret_key",
        env="MINIO_SECRET_KEY_PATH"
    )
    
    # Domain registrars (Vault-managed secrets)
    registrar_configs_path: str = Field(
        default="secret/registrars",
        env="REGISTRAR_CONFIGS_PATH"
    )
    godaddy_api_key_path: str = Field(
        default="secret/registrars/godaddy/api_key",
        env="GODADDY_API_KEY_PATH"
    )
    namecheap_api_key_path: str = Field(
        default="secret/registrars/namecheap/api_key",
        env="NAMECHEAP_API_KEY_PATH"
    )
    opensrs_api_key_path: str = Field(
        default="secret/registrars/opensrs/api_key",
        env="OPENSRS_API_KEY_PATH"
    )
    
    # Default settings
    default_nameservers: List[str] = Field(
        default=["ns1.trix.com", "ns2.trix.com"],
        env="DEFAULT_NAMESERVERS"
    )
    trix_domain_suffix: str = Field(default="trix-sites.com", env="TRIX_DOMAIN_SUFFIX")
    
    class Config:
        env_prefix = "DOMAIN_"

class PerformanceSettings(BaseSettings):
    """Performance and scaling configuration."""
    
    # Server settings
    max_workers: int = Field(default=8, env="MAX_WORKERS")
    worker_connections: int = Field(default=1000, env="WORKER_CONNECTIONS")
    keepalive_timeout: int = Field(default=65, env="KEEPALIVE_TIMEOUT")
    request_timeout: int = Field(default=30, env="REQUEST_TIMEOUT")
    max_request_size: str = Field(default="10MB", env="MAX_REQUEST_SIZE")
    
    # Rate limiting
    rate_limit_per_minute: int = Field(default=1000, env="RATE_LIMIT_PER_MINUTE")
    bulk_operation_limit: int = Field(default=100, env="BULK_OPERATION_LIMIT")
    
    # Caching
    cache_ttl: int = Field(default=3600, env="CACHE_TTL")
    cache_max_size: int = Field(default=10000, env="CACHE_MAX_SIZE")
    
    class Config:
        env_prefix = "PERFORMANCE_"

class ComplianceSettings(BaseSettings):
    """Compliance and audit configuration."""
    
    # Audit logging
    audit_log_enabled: bool = Field(default=True, env="AUDIT_LOG_ENABLED")
    audit_log_level: str = Field(default="INFO", env="AUDIT_LOG_LEVEL")
    
    # GDPR compliance
    gdpr_compliance_enabled: bool = Field(default=True, env="GDPR_COMPLIANCE_ENABLED")
    data_retention_days: int = Field(default=2555, env="DATA_RETENTION_DAYS")  # 7 years
    
    # Compliance reporting
    compliance_reporting_enabled: bool = Field(
        default=True,
        env="COMPLIANCE_REPORTING_ENABLED"
    )
    
    class Config:
        env_prefix = "COMPLIANCE_"

class Settings(BaseSettings, StandardVaultConfig):
    """Main settings class combining all configuration sections."""
    
    # Service info
    service_name: str = Field(default="domain-service", env="SERVICE_NAME")
    service_version: str = Field(default="2.0.0", env="SERVICE_VERSION")
    port: int = Field(default=8015, env="PORT")
    host: str = Field(default="0.0.0.0", env="HOST")
    
    # Environment
    environment: str = Field(default="production", env="ENVIRONMENT")
    debug: bool = Field(default=False, env="DEBUG")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    # Multi-region
    region: str = Field(default="us-east-1", env="REGION")
    availability_zone: str = Field(default="us-east-1a", env="AVAILABILITY_ZONE")
    cross_region_replication: bool = Field(default=True, env="CROSS_REGION_REPLICATION")
    disaster_recovery_enabled: bool = Field(default=True, env="DISASTER_RECOVERY_ENABLED")
    
    # Configuration sections
    database: DatabaseSettings = DatabaseSettings()
    messaging: MessagingSettings = MessagingSettings()
    security: SecuritySettings = SecuritySettings()
    service_mesh: ServiceMeshSettings = ServiceMeshSettings()
    observability: ObservabilitySettings = ObservabilitySettings()
    domain: DomainSettings = DomainSettings()
    performance: PerformanceSettings = PerformanceSettings()
    compliance: ComplianceSettings = ComplianceSettings()

    class Config:
        env_file = ".env"
        case_sensitive = False

# Global settings instance
settings = Settings()
